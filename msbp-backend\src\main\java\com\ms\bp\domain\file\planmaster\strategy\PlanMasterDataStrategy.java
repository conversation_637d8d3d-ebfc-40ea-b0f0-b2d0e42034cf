package com.ms.bp.domain.file.planmaster.strategy;

import com.ms.bp.application.data.strategy.DataAccessStrategy;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 次年度計画マスタデータ取得SQL戦略
 * 複雑なJOINクエリを使用して次年度計画マスタの全データを取得
 * nendo_paramとarea_code_paramの2つのパラメータを使用
 */
public class PlanMasterDataStrategy implements DataAccessStrategy {
    private static final Logger logger = LoggerFactory.getLogger(PlanMasterDataStrategy.class);

    @Override
    public SqlWithParams buildQuery(ExportRequest exportRequest, UserInfo userInfo) {
        logger.info("次年度計画マスタSQL構築開始: ユーザー={}", userInfo.getShainCode());

        // 年度パラメータの設定（DateUtilを使用して次年度を取得）
        String nendoParam = DateUtil.getNextFiscalYear();

        // エリアコードリストの設定
        List<String> areaCodeList = exportRequest.getArea();

        // SQLクエリとパラメータリストを構築
        SqlWithParams queryResult = buildPlanMasterQueryWithParams(nendoParam, areaCodeList);

        logger.info("次年度計画マスタSQL構築完了: 年度={}, エリア数={}", nendoParam, areaCodeList.size());
        return queryResult;
    }

    @Override
    public String getStrategyName() {
        return BusinessConstants.PLAN_MASTER_DEFAULT;
    }

    /**
     * 位置パラメータを使用したSQLクエリとパラメータマップを構築
     * エリアコードリストに基づいてIN句の?プレースホルダーを動的生成
     * パラメータ配列の順序を一致させる必要がある
     */
    private SqlWithParams buildPlanMasterQueryWithParams(String nendoParam, List<String> areaCodeList) {
        // パラメータリストを作成（順序重要）

        // 2. エリアコードパラメータを追加
        String areaCondition;
        // エリアコードの数だけ?プレースホルダーを生成
        String areaPlaceholders = String.join(",", Collections.nCopies(areaCodeList.size(), "?"));
        areaCondition = String.format(" g.area_code IN (%s) ", areaPlaceholders);

        // エリアコードを2回追加（tky_area_code用とarea_code用）
        List<Object> paramList = new ArrayList<>(areaCodeList);
        // 1. 年度パラメータを追加（WHERE句の最初のパラメータ）
        paramList.add(nendoParam);
        paramList.add(nendoParam);
        paramList.add(nendoParam);
        paramList.add(nendoParam);
        paramList.add(nendoParam);
        // SQLクエリを構築
        String sql = buildPlanMasterQueryWithPositionalParams(areaCondition);

        // 順序保持のためLinkedHashMapを使用してパラメータマップを構築
        Map<String, Object> paramMap = new LinkedHashMap<>();
        for (int i = 0; i < paramList.size(); i++) {
            paramMap.put("param" + i, paramList.get(i));
        }

        logger.debug("構築されたパラメータ順序: {}", paramList);

        return new SqlWithParams(sql, paramMap);
    }

    /**
     * 次年度計画マスタ取得用SQLクエリを構築
     */
    private String buildPlanMasterQueryWithPositionalParams(String areaCondition) {
        return String.format("""
                    SELECT
                        -- エリアCD
                        CASE
                            WHEN jk.tky_area_code = target_units.area_code THEN jk.jk_area_code
                            ELSE target_units.area_code
                        END AS area_cd,
            
                        -- エリア名
                        va.area_mei_kanji AS area_mei,
          
                        -- カテCD
                        jk.ctgry_code AS cate_cd,
           
                        -- カテゴリ
                        cat.ctgry_mei_kanji AS category,
            
                        -- グループ
                        target_units.group_code AS group_cd,
            
                        -- ユニット
                        jk.unit_code AS unit_cd,
            
                        -- 担当者
                        tant.tntsh_mei_kanji AS tantousha,
            
                        -- 企業CD
                        jk.kigyo_code AS kigyo_cd,
           
                        -- 企業名
                        kigyo.kgym_kanji AS kigyo_mei,
           
                        -- 業態集計
                        gyt_kanri.gyt_shk_no AS gyoutai_shukei,
            
                        -- 業態名
                        gyt_mei.gyt_mei AS gyoutai_mei,
            
                        -- サブカテゴリ
                        jk.sub_ctgry_code AS sub_category_cd,
            
                        -- サブカテゴリ名
                        cat.sub_ctgry_mei_kanji AS sub_category_mei,
            
                        -- 採算管理単位CD
                        jk.ssnkn_tncd AS saisan_kanri_tani_cd,
            
                        -- 採算管理単位名
                        jk.ssn_kanri_tnm_kanji AS saisan_kanri_tani_mei,
           
                        -- 変更前取組区分
                        fzk.jiyu_shyk_mei_1 AS henkou_mae_torikumi_kubun,
           
                        -- 変更後取組区分
                        jk.hnkg_trkm_kubun AS henkou_go_torikumi_kubun,
           
                        -- 移管先エリアCD
                        jk.iknsk_area_code AS ikan_saki_area_cd,
            
                        -- 移管先エリア名
                        va2.area_mei_kanji AS ikan_saki_area_mei,
            
                        -- 移管先グループCD
                        jk.iknsk_group_code AS ikan_saki_group_cd,
            
                        -- 移管先ユニットCD
                        jk.iknsk_unit_code AS ikan_saki_unit_cd,
            
                        -- 当年度計画累計(参考)
                        COALESCE(keikaku.keikaku_sum, 0) AS tounen_keikaku_ruikei,
            
                        -- 当年度実績累計(参考)
                        COALESCE(jisseki.jisseki_sum, 0) AS tounen_jisseki_ruikei
            
                    FROM (
                        -- 対象ユニットの階層情報を取得
                        SELECT DISTINCT
                            g.area_code,
                            g.group_code,
                            u.unit_code
                        FROM M_GROUPMST g
                        INNER JOIN M_UNITMST u ON u.group_code = g.group_code
                        WHERE %s
                            AND CURRENT_DATE::CHAR(8) >= g.kshb
                            AND CURRENT_DATE::CHAR(8) <= g.shryb
                            AND g.shiyo_knsh_kubun = '0'
                            AND CURRENT_DATE::CHAR(8) >= u.kshb
                            AND CURRENT_DATE::CHAR(8) <= u.shryb
                            AND u.shiyo_knsh_kubun = '0'
                    ) target_units
            
                    -- 採算管理単位を取得（次年度計画マスタと採算管理単位マスタの両方から、必要なフィールドを含めて）
                    INNER JOIN LATERAL (
                        -- パターン1: 次年度計画マスタから
                        SELECT DISTINCT
                            jk.ssnkn_tncd,
                            jk.ssn_kanri_tnm_kanji,
                            jk.hnkg_trkm_kubun,
                            jk.iknsk_area_code,
                            jk.iknsk_group_code,
                            jk.iknsk_unit_code,
                            jk.area_code AS jk_area_code,
                            jk.tky_area_code,
                            -- 採算管理単位マスタから必要な情報を取得
                            COALESCE(skm.unit_code, jk.unit_code) AS unit_code,
                            skm.ctgry_code,
                            skm.sub_ctgry_code,
                            skm.kigyo_code,
                            skm.gyt_code,
                            skm.smk_code,
                            skm.tntsh_code
                        FROM T_JINENDO_KKK jk
                        LEFT JOIN M_SAISANKANRITANIMST skm
                            ON skm.ssnkn_tncd = jk.ssnkn_tncd
                            AND CURRENT_DATE::CHAR(8) >= skm.kshb
                            AND CURRENT_DATE::CHAR(8) <= skm.shryb
                            AND skm.shiyo_knsh_kubun = '0'
                        WHERE jk.nendo = ?
                            AND jk.unit_code = target_units.unit_code
                            AND jk.group_code = target_units.group_code
           
                        UNION
           
                        -- パターン2: 採算管理単位マスタから（次年度計画マスタに存在しないもの）
                        SELECT DISTINCT
                            skm.ssnkn_tncd,
                            skm.ssn_kanri_tnm_kanji,
                            NULL AS hnkg_trkm_kubun,
                            NULL AS iknsk_area_code,
                            NULL AS iknsk_group_code,
                            NULL AS iknsk_unit_code,
                            NULL AS jk_area_code,
                            NULL AS tky_area_code,
                            skm.unit_code,
                            skm.ctgry_code,
                            skm.sub_ctgry_code,
                            skm.kigyo_code,
                            skm.gyt_code,
                            skm.smk_code,
                            skm.tntsh_code
                        FROM M_SAISANKANRITANIMST skm
                        WHERE skm.unit_code = target_units.unit_code
                            AND CURRENT_DATE::CHAR(8) >= skm.kshb
                            AND CURRENT_DATE::CHAR(8) <= skm.shryb
                            AND skm.shiyo_knsh_kubun = '0'
                            AND NOT EXISTS (
                                SELECT 1
                                FROM T_JINENDO_KKK jk2
                                WHERE jk2.nendo = ?
                                    AND jk2.ssnkn_tncd = skm.ssnkn_tncd
                                    AND jk2.group_code = target_units.group_code
                            )
                    ) jk ON TRUE
            
                    -- エリア情報を取得
                    LEFT JOIN LATERAL (
                        SELECT area_mei_kanji
                        FROM M_SOSHIKIAREAMST
                        WHERE area_code = CASE
                            WHEN jk.tky_area_code = target_units.area_code THEN jk.jk_area_code
                            ELSE target_units.area_code
                        END
                            AND CURRENT_DATE::CHAR(8) >= kshb
                            AND CURRENT_DATE::CHAR(8) <= shryb
                            AND shiyo_knsh_kubun = '0'
                        ORDER BY sub_area_code
                        LIMIT 1
                    ) va ON TRUE
            
                    -- カテゴリ情報を取得
                    LEFT JOIN LATERAL (
                        SELECT ctgry_mei_kanji, sub_ctgry_mei_kanji
                        FROM M_CATEGORYMST
                        WHERE ctgry_code = jk.ctgry_code
                            AND sub_ctgry_code = jk.sub_ctgry_code
                            AND shiyo_knsh_kubun = '0'
                        LIMIT 1
                    ) cat ON TRUE
            
                    -- 担当者情報を取得
                    LEFT JOIN LATERAL (
                        SELECT tntsh_mei_kanji
                        FROM M_TANTOSHAMST
                        WHERE tntsh_code = jk.tntsh_code
                            AND unit_code = jk.unit_code
                            AND shiyo_knsh_kubun = '0'
                        LIMIT 1
                    ) tant ON TRUE
            
                    -- 企業情報を取得
                    LEFT JOIN M_KIGYOMST kigyo
                        ON kigyo.kigyo_code = jk.kigyo_code
                        AND kigyo.shiyo_knsh_kubun = '0'
           
                    -- 業態管理情報を取得
                    LEFT JOIN LATERAL (
                        SELECT gyt_shk_no
                        FROM M_GYT_KANRI
                        WHERE gyt_code = jk.gyt_code
                            AND smk_code = jk.smk_code
                            AND CURRENT_DATE::CHAR(8) >= kshb
                            AND CURRENT_DATE::CHAR(8) <= shryb
                        ORDER BY kshb DESC
                        LIMIT 1
                    ) gyt_kanri ON TRUE
           
                    -- 業態名情報を取得
                    LEFT JOIN M_GYT_MEI gyt_mei ON gyt_mei.gyt_shk_no = gyt_kanri.gyt_shk_no
            
                    -- 採算管理単位付属項目情報を取得（変更前取組区分）
                    LEFT JOIN LATERAL (
                        SELECT jiyu_shyk_mei_1
                        FROM M_SAISANKANRITANIFZKKMKMST
                        WHERE ssnkn_tncd = jk.ssnkn_tncd
                            AND CURRENT_DATE::CHAR(8) >= kshb
                            AND CURRENT_DATE::CHAR(8) <= shryb
                        ORDER BY kshb DESC
                        LIMIT 1
                    ) fzk ON TRUE
            
                    -- 移管先エリア情報を取得
                    LEFT JOIN LATERAL (
                        SELECT area_mei_kanji
                        FROM M_SOSHIKIAREAMST
                        WHERE area_code = jk.iknsk_area_code
                            AND CURRENT_DATE::CHAR(8) >= kshb
                            AND CURRENT_DATE::CHAR(8) <= shryb
                            AND shiyo_knsh_kubun = '0'
                        ORDER BY sub_area_code
                        LIMIT 1
                    ) va2 ON TRUE
            
                    -- 当年度計画累計を計算
                    LEFT JOIN LATERAL (
                        SELECT SUM(kkk_gk) AS keikaku_sum
                        FROM SSNKN_TN_C_CHKST_KKK
                        WHERE kanri_kk_nendo = ?
                            AND ssnkn_tncd = jk.ssnkn_tncd
                            AND togo__kubun IN ('01', '02')
                            AND (jk.ssnkn_tncd <> '1118888' OR group_code = target_units.group_code)
                    ) keikaku ON TRUE
          
                    -- 当年度実績累計を計算
                    LEFT JOIN LATERAL (
                        SELECT SUM(jssk_gk) AS jisseki_sum
                        FROM SSNKN_TN_C_CHKST_JSSK
                        WHERE kanri_kk_nendo = ?
                            AND ssnkn_tncd = jk.ssnkn_tncd
                            AND togo__kubun IN ('01', '02')
                            AND (jk.ssnkn_tncd <> '1118888' OR group_code = target_units.group_code)
                    ) jisseki ON TRUE
            
                    -- エリア表示順を取得（ソート用）
                    LEFT JOIN LATERAL (
                        SELECT area_order
                        FROM M_SK_KKK_AREA
                        WHERE nendo = ?
                            AND area_code = CASE
                                WHEN jk.tky_area_code = target_units.area_code THEN jk.jk_area_code
                                ELSE target_units.area_code
                            END
                    ) area_sort ON TRUE
           
                    -- ソート条件
                    ORDER BY
                        area_sort.area_order ASC NULLS LAST,
                        jk.ctgry_code ASC NULLS LAST,
                        target_units.group_code ASC,
                        jk.ssnkn_tncd ASC
            """, areaCondition);
    }
}
