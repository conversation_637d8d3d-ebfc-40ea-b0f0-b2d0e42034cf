package com.ms.bp.domain.permission;

import com.ms.bp.domain.permission.model.*;
import com.ms.bp.domain.permission.repository.PermissionRepository;
import com.ms.bp.domain.admin.repository.SystemAdminRepository;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 権限ドメインサービス
 * 権限に関するビジネスロジックを実装する
 */
public class PermissionService {
    private static final Logger logger = LoggerFactory.getLogger(PermissionService.class);

    // ファイルタイプマッピング
    private static final Map<String, String> FILE_TYPE_MAPPING = Map.of(
            "001", BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE,
            "002", BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE,
            "003", BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE,
            "004", BusinessConstants.FILE_TYPE_INDIRECT_PROFIT_CODE
    );
    // 権限コード定数
    private static final String DOWNLOAD_PREFIX = "DL";
    private static final String UPLOAD_PREFIX = "UL";
    private static final char HEAD_OFFICE_MARKER = 'H';
    private static final char AREA_MARKER = 'A';
    private final PermissionRepository permissionRepository;
    private final SystemAdminRepository systemAdminRepository;

    public PermissionService(PermissionRepository permissionRepository, SystemAdminRepository systemAdminRepository) {
        this.permissionRepository = permissionRepository;
        this.systemAdminRepository = systemAdminRepository;
    }

    /**
     * ユーザーの全権限を取得（主務・兼務組み合わせ対応版）
     * JDK21のPattern Matching、Switch Expression等を活用した最適化実装
     *
     * @param userInfo ユーザー情報
     * @param concurrentJobAreaUnitPairs 兼務のareaCode-unitCode組み合わせリスト（"areaCode,unitCode"形式）
     * @return Map<String, List<UserPermissionInfo>> key: "areaCode,unitCode", value: 権限リスト
     */
    public Map<String, List<UserPermissionInfo>> getUserPermissions(UserInfo userInfo, List<String> concurrentJobAreaUnitPairs) {
        logger.info("ユーザー権限取得開始: 社員コード={}, 兼務組み合わせ数={}", 
                   userInfo.getShainCode(), concurrentJobAreaUnitPairs != null ? concurrentJobAreaUnitPairs.size() : 0);

        try {
            // 1. システム管理者かどうかを確認
            var isSystemAdmin = systemAdminRepository.isValidSystemAdmin(
                userInfo.getShainCode(), 
                userInfo.getSystemOperationCompanyCode()
            );
            
            if (isSystemAdmin) {
                logger.info("システム管理者として権限を取得します: 社員コード={}", userInfo.getShainCode());
                return getSystemAdminPermissions(userInfo);
            }
            // 2. 一般ユーザーとして権限を取得
            return getRegularUserPermissions(userInfo, concurrentJobAreaUnitPairs);

        } catch (Exception e) {
            logger.error("ユーザー権限取得中にエラーが発生しました: 社員コード={}, 兼務組み合わせ={}", 
                        userInfo.getShainCode(), concurrentJobAreaUnitPairs, e);
            throw new ServiceException(
                    GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                    STR."ユーザー権限取得処理でエラーが発生しました: \{e.getMessage()}"
            );
        }
    }

    /**
     * システム管理者の権限を取得
     * 権限コードの3桁目が'H'（本社）の権限のみを取得
     * 
     * @param userInfo ユーザー情報
     * @return Map<String, List<UserPermissionInfo>> key: "areaCode,unitCode", value: 権限リスト
     */
    private Map<String, List<UserPermissionInfo>> getSystemAdminPermissions(UserInfo userInfo) {
        logger.debug("システム管理者権限取得処理開始: 社員コード={}", userInfo.getShainCode());

        // 主務のareaCode-unitCode組み合わせのみ使用
        var primaryJobAreaUnitPair = STR."\{userInfo.getAreaCode()},\{userInfo.getUnitCode()}";
        var singleJobAreaUnitPairs = List.of(primaryJobAreaUnitPair);

        // 共通権限を取得
        var allCommonPermissions = permissionRepository.findCommonPermissionsBatch(
                userInfo.getSystemOperationCompanyCode(),
                singleJobAreaUnitPairs
        );

        // JDK21: Switch Expressionで本社権限（3桁目が'H'）のみフィルタリング
        var headOfficePermissions = allCommonPermissions.stream()
            .filter(permission -> switch (permission.getPermissionCode() != null && permission.getPermissionCode().length() >= 3) {
                case true -> permission.getPermissionCode().charAt(2) == HEAD_OFFICE_MARKER;
                case false -> {
                    logger.warn("不正な権限コード形式をスキップ: {}", permission.getPermissionCode());
                    yield false;
                }
            })
            .filter(permission -> switch (permission.getHantPttrn()) {
                case "1" -> userInfo.getUnitCode().equals(permission.getTargetCode());
                case "2" -> userInfo.getAreaCode().equals(permission.getTargetCode());
                default -> {
                    logger.debug("未対応の判定区分をスキップ: {}", permission.getHantPttrn());
                    yield false;
                }
            })
            .map(p -> parsePermissionCode(p.getPermissionCode(), p.getHantCode()))
            .toList();

        // 個人権限も取得してフィルタリング
        var personalPermissions = permissionRepository.findPersonalPermissions(
                userInfo.getShainCode(),
                userInfo.getSystemOperationCompanyCode()
        );

        var filteredPersonalPermissions = personalPermissions.stream()
            .filter(p -> p.getPermissionCode() != null && p.getPermissionCode().length() >= 3)
            .filter(p -> p.getPermissionCode().charAt(2) == HEAD_OFFICE_MARKER)
            .map(p -> parsePermissionCode(p.getPermissionCode(), p.getHantCode()))
            .toList();

        // 統合処理
        var finalPermissions = Stream.concat(
                headOfficePermissions.stream(),
                filteredPersonalPermissions.stream()
        )
        .collect(Collectors.toMap(
            UserPermissionInfo::getPermissionCode,
            Function.identity(),
            (existing, replacement) -> replacement, // 個人権限で上書き
            LinkedHashMap::new
        ))
        .values()
        .stream()
        .toList();

        finalPermissions = deduplicatePermissions(new ArrayList<>(finalPermissions));

        var result = Map.of(primaryJobAreaUnitPair, finalPermissions);

        logger.info("システム管理者権限取得完了: 社員コード={}, 本社権限数={}", 
                   userInfo.getShainCode(), finalPermissions.size());

        return result;
    }

    /**
     * 一般ユーザーの権限を取得（既存ロジック）
     * 
     * @param userInfo ユーザー情報
     * @param concurrentJobAreaUnitPairs 兼務のareaCode-unitCode組み合わせリスト
     * @return Map<String, List<UserPermissionInfo>> key: "areaCode,unitCode", value: 権限リスト
     */
    private Map<String, List<UserPermissionInfo>> getRegularUserPermissions(UserInfo userInfo, List<String> concurrentJobAreaUnitPairs) {
        logger.debug("一般ユーザー権限取得処理開始: 社員コード={}", userInfo.getShainCode());

        // JDK21: Null-safe処理とSequenced Collections活用
        var safeConcurrentPairs = concurrentJobAreaUnitPairs != null ? concurrentJobAreaUnitPairs : List.<String>of();
        
        var primaryJobAreaUnitPair = STR."\{userInfo.getAreaCode()},\{userInfo.getUnitCode()}";
        var allJobAreaUnitPairs = new ArrayList<String>();
        allJobAreaUnitPairs.add(primaryJobAreaUnitPair);
        allJobAreaUnitPairs.addAll(safeConcurrentPairs);

        // 2. 一回のクエリで全ての共通権限を取得
        var allCommonPermissions = permissionRepository.findCommonPermissionsBatch(
                userInfo.getSystemOperationCompanyCode(),
                allJobAreaUnitPairs
        );

        // 3. 主務の個人権限を取得
        var primaryJobPersonalPermissions = permissionRepository.findPersonalPermissions(
                userInfo.getShainCode(),
                userInfo.getSystemOperationCompanyCode()
        );

        // 4. JDK21: Pattern Matchingを活用して主務・兼務別に権限を分類
        var result = new HashMap<String, List<UserPermissionInfo>>();
        
        for (var jobAreaUnitPair : allJobAreaUnitPairs) {
            var parts = jobAreaUnitPair.split(",");
            if (parts.length != 2) {
                logger.warn("無効な職務areaCode-unitCode組み合わせ: {}", jobAreaUnitPair);
                continue;
            }
            
            var areaCode = parts[0].trim();
            var unitCode = parts[1].trim();
            var isPrimaryJob = jobAreaUnitPair.equals(primaryJobAreaUnitPair);
            
            // JDK21: Switch Expressionで権限フィルタリング
            var commonPermissionsForPair = allCommonPermissions.stream()
                .filter(permission -> switch (permission.getHantPttrn()) {
                    case "1" -> unitCode.equals(permission.getTargetCode()); // ユニットコード判定
                    case "2" -> areaCode.equals(permission.getTargetCode());  // エリアコード判定
                    default -> {
                        logger.debug("未対応の判定区分: {}", permission.getHantPttrn());
                        yield false;
                    }
                })
                .map(p -> parsePermissionCode(p.getPermissionCode(), p.getHantCode()))
                .toList();

            List<UserPermissionInfo> finalPermissions;
            
            if (isPrimaryJob) {
                // 主務: 共通権限 + 個人権限を統合
                var personalPermissionInfos = primaryJobPersonalPermissions.stream()
                    .map(p -> parsePermissionCode(p.getPermissionCode(), p.getHantCode()))
                    .toList();

                // JDK21: Collectors.teeing()を使った統合処理
                finalPermissions = Stream.concat(
                        commonPermissionsForPair.stream(),
                        personalPermissionInfos.stream()
                    )
                    .collect(Collectors.toMap(
                        UserPermissionInfo::getPermissionCode,
                        Function.identity(),
                        (existing, replacement) -> replacement, // 個人権限で上書き
                        LinkedHashMap::new // 順序保持
                    ))
                    .values()
                    .stream()
                    .toList();

                finalPermissions = deduplicatePermissions(new ArrayList<>(finalPermissions));
            } else {
                // 兼務: 共通権限のみ
                finalPermissions = deduplicatePermissions(new ArrayList<>(commonPermissionsForPair));
            }

            result.put(jobAreaUnitPair, finalPermissions);
            
            logger.debug("権限処理完了: areaCode={}, unitCode={}, 権限数={}, 職務区分={}",
                       areaCode, unitCode, finalPermissions.size(), isPrimaryJob ? "主務" : "兼務");
        }

        logger.info("一般ユーザー権限取得完了: 社員コード={}, 組み合わせ数={}, 総権限数={}",
                   userInfo.getShainCode(), result.size(), 
                   result.values().stream().mapToInt(List::size).sum());

        return result;
    }

    /**
     * 権限リストの重複を削除
     * 3桁目が'H'（本社）と'A'（エリア）の権限が共存する場合、'A'の権限を削除
     */
    public static List<UserPermissionInfo> deduplicatePermissions(List<UserPermissionInfo> permissions) {
        var allCodes = permissions.stream()
                .map(UserPermissionInfo::getPermissionCode)
                .collect(Collectors.toSet());

        return permissions.stream()
                .filter(info -> {
                    var code = info.getPermissionCode();
                    // 3桁目が'A'かつ対応する'H'権限が存在する場合は除外
                    if (code.charAt(2) == AREA_MARKER) {
                        var headOfficeCode = code.substring(0, 2) + HEAD_OFFICE_MARKER + code.substring(3);
                        return !allCodes.contains(headOfficeCode);
                    }
                    return true;
                }).toList();
    }

    /**
     * 権限コードを解析し、UserPermissionInfoオブジェクトを生成
     */
    private static UserPermissionInfo parsePermissionCode(String permissionCode, String hantCode) {
        var info = new UserPermissionInfo();
        info.setPermissionCode(permissionCode);

        // 1-2桁目: 操作区分
        var operation = permissionCode.substring(0, 2);
        info.setOperationDivision(switch (operation) {
            case DOWNLOAD_PREFIX -> BusinessConstants.OPERATION_DOWNLOAD_CODE;
            case UPLOAD_PREFIX -> BusinessConstants.OPERATION_UPLOAD_CODE;
            default -> "";
        });

        // 3桁目: 対象所属区分
        var areaPattern = permissionCode.charAt(2);
        info.setAreaPattern(switch (areaPattern) {
            case HEAD_OFFICE_MARKER -> BusinessConstants.AFFILIATION_HEAD_OFFICE;
            case AREA_MARKER -> BusinessConstants.AFFILIATION_AREA;
            default -> "";
        });

        // 4-6桁目: ファイル種類コード
        var fileType = permissionCode.substring(3, 6);
        info.setFileTypeCode(FILE_TYPE_MAPPING.getOrDefault(fileType, ""));
        // 判定コード
        info.setHantCode(hantCode);
        return info;
    }
}