package com.ms.bp.interfaces.rest.exception;

import com.ms.bp.shared.common.CommonResult;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.exception.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * グローバル例外処理ハンドラ
 * 全ての例外をキャッチし、共通レスポンス形式に変換する
 */
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 例外をキャッチし、共通レスポンス形式に変換する
     * @param e 例外
     * @return 共通レスポンス
     */
    public static <T> CommonResult<T> handleException(Throwable e) {
        logger.error("[GlobalExceptionHandler] 例外処理: ", e);

        // ServiceException (ビジネスロジック例外) の場合
        if (e instanceof ServiceException serviceException) {
            return CommonResult.error(serviceException);
        }

        // 未処理の例外は内部サーバーエラーとして処理
        return CommonResult.error(GlobalMessageConstants.INTERNAL_SERVER_ERROR);
    }

    /**
     * 特定のエラーコードで例外をキャッチし、共通レスポンス形式に変換する
     * @param message エラーコード
     * @return 共通レスポンス
     */
    public static <T> CommonResult<T> handleError(Message message) {
        logger.error("[GlobalExceptionHandler] エラー処理: {}", message.getMsg());
        return CommonResult.error(message);
    }

    /**
     * カスタムエラーメッセージで例外をキャッチし、共通レスポンス形式に変換する
     * @param code エラーコード
     * @param message エラーメッセージ
     * @return 共通レスポンス
     */
    public static <T> CommonResult<T> handleError(Integer code, String message) {
        logger.error("[GlobalExceptionHandler] エラー処理: {}:{}", code, message);
        return CommonResult.error(code, message);
    }
}