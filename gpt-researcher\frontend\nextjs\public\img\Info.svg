<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Info" clip-path="url(#clip0_14_6243)">
<path id="Vector" d="M12 0C15.1826 0 18.2348 1.26428 20.4853 3.51472C22.7357 5.76516 24 8.8174 24 12C24 15.1826 22.7357 18.2348 20.4853 20.4853C18.2348 22.7357 15.1826 24 12 24C8.8174 24 5.76516 22.7357 3.51472 20.4853C1.26428 18.2348 0 15.1826 0 12C0 8.8174 1.26428 5.76516 3.51472 3.51472C5.76516 1.26428 8.8174 0 12 0ZM14.25 11.25H9.75V19.5H14.25V11.25ZM12 4.5C11.4033 4.5 10.831 4.73705 10.409 5.15901C9.98705 5.58097 9.75 6.15326 9.75 6.75C9.75 7.34674 9.98705 7.91903 10.409 8.34099C10.831 8.76295 11.4033 9 12 9C12.5967 9 13.169 8.76295 13.591 8.34099C14.0129 7.91903 14.25 7.34674 14.25 6.75C14.25 6.15326 14.0129 5.58097 13.591 5.15901C13.169 4.73705 12.5967 4.5 12 4.5Z" fill="url(#paint0_linear_14_6243)"/>
</g>
<defs>
<linearGradient id="paint0_linear_14_6243" x1="0.827587" y1="-2.27913e-07" x2="15.7188" y2="25.3405" gradientUnits="userSpaceOnUse">
<stop stop-color="#1B1B16"/>
<stop offset="1" stop-color="#565646"/>
</linearGradient>
<clipPath id="clip0_14_6243">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>
