@tailwind base;
@tailwind components;
@tailwind utilities;

@keyframes gradientBG {
  0% {background-position: 0% 50%;}
  50% {background-position: 100% 50%;}
  100% {background-position: 0% 50%;}
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-20px) translateX(10px);
  }
  50% {
    transform: translateY(-10px) translateX(-15px);
  }
  75% {
    transform: translateY(-25px) translateX(5px);
  }
}

html {
  scroll-behavior: smooth;
  height: 100%;
}

textarea {
  max-height: 300px; /* Set an appropriate max height */
  overflow-y: auto;  /* Enable internal scrolling */
  /* transition: height 0.2s ease-in-out; */
}

.log-message {
  word-wrap: break-word; /* For handling long URLs or text */
  overflow-wrap: break-word; /* For handling overflow in modern browsers */
  overflow-x: hidden; /* Hide horizontal overflow */
  word-break: break-word; /* Break long words if needed */
}

body {
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
  line-height: 1.6;
  margin: 0px !important;
  min-height: 100%;
  position: relative;
  background: #0C111F;
  overflow-x: hidden;
}

/* Background gradient orbs - static version */
body::before {
  content: "";
  position: fixed;
  top: -25%;
  left: -10%;
  width: 60%;
  height: 60%;
  border-radius: 9999px;
  background-color: rgba(13, 148, 136, 0.12);
  filter: blur(120px);
  z-index: -10;
}

body::after {
  content: "";
  position: fixed;
  bottom: -25%;
  right: -10%;
  width: 60%;
  height: 60%;
  border-radius: 9999px;
  background-color: rgba(8, 145, 178, 0.12);
  filter: blur(120px);
  z-index: -10;
}

/* Additional orb */
.app-container::before {
  content: "";
  position: fixed;
  top: 40%;
  right: 20%;
  width: 35%;
  height: 35%;
  border-radius: 9999px;
  background-color: rgba(37, 99, 235, 0.06);
  filter: blur(80px);
  z-index: -10;
}

.landing {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 30vh;
  text-align: center;
  color: white;
}

.landing h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
  .loader {
    text-align: left;
    display: flex;
    gap: 3px;
  }

  .loader span {
    display: inline-block;
    vertical-align: middle;
    width: 7px;
    height: 7px;
    /* background: #4b4b4b; */
    background: white;
    border-radius: 50%;
    animation: loader 0.6s infinite alternate;
  }

  .loader span:nth-of-type(2) {
    animation-delay: 0.2s;
  }

  .loader span:nth-of-type(3) {
    animation-delay: 0.6s;
  }

  @keyframes loader {
    0% {
      opacity: 1;
      transform: scale(0.6);
    }

    100% {
      opacity: 0.3;
      transform: scale(1);
    }
  }
}

/* Add these styles for the scrollbar */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
  background-color: #4B5563;
  border-radius: 6px;
}

.scrollbar-track-gray-300::-webkit-scrollbar-track {
  background-color: #D1D5DB;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

/* Sidebar styles */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 40;
  transition: opacity 0.3s ease;
}

/* Sidebar backdrop blur */
.sidebar-backdrop {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Scrollbar styling for the sidebar */
.sidebar-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.sidebar-scrollbar::-webkit-scrollbar-track {
  background: #1f2937;
}

.sidebar-scrollbar::-webkit-scrollbar-thumb {
  background-color: #4b5563;
  border-radius: 3px;
}

.sidebar-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}

/* Ensure sidebar is above other content */
.sidebar-z-index {
  z-index: 50;
}

