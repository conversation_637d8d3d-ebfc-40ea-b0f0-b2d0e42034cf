package com.ms.bp.shared.common.io.mapper;

import java.lang.invoke.MethodHandle;
import java.lang.invoke.MethodHandles;
import java.lang.reflect.Field;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Map と DTO 間のマッピングを行うユーティリティクラス
 * パフォーマンス最適化：MethodHandle とフィールドメタデータのキャッシュ機能を実装
 * JDK 21 最適化バージョン
 */
public class DTOMapper {

    private static final Logger logger = LoggerFactory.getLogger(DTOMapper.class);

    /** MethodHandles.Lookup インスタンス（MethodHandle 作成用） */
    private static final MethodHandles.Lookup LOOKUP = MethodHandles.lookup();

    /** フィールドメタデータのキャッシュ（スレッドセーフ） */
    private static final Map<Class<?>, FieldMetadata[]> FIELD_METADATA_CACHE = new ConcurrentHashMap<>();

    /**
     * フィールドメタデータクラス
     * フィールド情報と対応する MethodHandle を保持
     */
    static class FieldMetadata {
        // 後方互換性のため、古いインターフェースも保持
        @Getter
        private final Field field;
        @Getter
        private final String fieldName;
        @Getter
        private final String snakeCaseName;
        @Getter
        private final Class<?> fieldType;
        private final MethodHandle setter;

        public FieldMetadata(Field field) {
            this.field = field;
            this.fieldName = field.getName();
            this.snakeCaseName = toSnakeCase(field.getName());
            this.fieldType = field.getType();

            try {
                field.setAccessible(true);
                // setter の MethodHandle を作成
                this.setter = LOOKUP.unreflectSetter(field);
            } catch (IllegalAccessException e) {
                throw new RuntimeException("フィールドの MethodHandle 作成エラー: " + field.getName(), e);
            }
        }

        /**
         * MethodHandle を使用してフィールド値を設定
         *
         * @param target 対象オブジェクト
         * @param value 設定する値
         * @throws Throwable MethodHandle 呼び出し時の例外
         */
        public void setValue(Object target, Object value) throws Throwable {
            setter.invoke(target, value);
        }

        /**
         * キャメルケースをスネークケースに変換
         * 例: fullName -> full_name
         */
        private static String toSnakeCase(String camelCase) {
            return camelCase.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
        }
    }

    /**
     * Map から DTO オブジェクトにマッピング
     * パフォーマンス最適化：MethodHandle を使用した高速フィールド設定
     *
     * @param data マップデータ
     * @param dtoClass DTOクラス
     * @param <T> DTOの型
     * @return DTOオブジェクト
     */
    public static <T> T mapToDTO(Map<String, Object> data, Class<T> dtoClass) {
        try {
            T dto = dtoClass.getDeclaredConstructor().newInstance();

            // キャッシュされたフィールドメタデータを取得（初回は自動生成）
            FieldMetadata[] fieldMetadataArray = getFieldMetadata(dtoClass);

            for (FieldMetadata metadata : fieldMetadataArray) {
                // キャッシュされたスネークケース名を使用してマップから値を取得
                Object value = data.get(metadata.getFieldName());

                if (value != null) {
                    try {
                        // 型変換を行ってフィールドに設定
                        Object convertedValue = convertValue(value, metadata.getFieldType());
                        if (convertedValue != null) {
                            // MethodHandle を使用して値を設定（反射より高速）
                            metadata.setValue(dto, convertedValue);
                        }
                    } catch (Throwable e) {
                        logger.error("フィールド {} の設定エラー: {}", metadata.getFieldName(), e.getMessage());
                        // ビジネス要件に応じて例外をスローするか、処理を継続するか決定
                        throw new RuntimeException("フィールド設定エラー: " + metadata.getFieldName(), e);
                    }
                }
            }

            return dto;
        } catch (Exception e) {
            throw new RuntimeException("DTOマッピングエラー: " + e.getMessage(), e);
        }
    }

    /**
     * 複数の Map を DTO リストにバッチマッピング
     * パフォーマンス最適化：メタデータを事前ロードして処理を高速化
     *
     * @param dataList マップデータのリスト
     * @param dtoClass DTOクラス
     * @param <T> DTOの型
     * @return DTOオブジェクトのリスト
     */
    public static <T> java.util.List<T> mapToDTOBatch(java.util.List<Map<String, Object>> dataList, Class<T> dtoClass) {
        if (dataList == null || dataList.isEmpty()) {
            return new java.util.ArrayList<>();
        }

        // バッチ処理前にメタデータを事前ロード（キャッシュ効率化）
        getFieldMetadata(dtoClass);

        // ストリーム処理でバッチ変換
        return dataList.stream()
                .map(data -> mapToDTO(data, dtoClass))
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * フィールドメタデータを取得（キャッシュ機能付き）
     * 初回アクセス時にメタデータを生成してキャッシュし、以降はキャッシュから取得
     *
     * @param clazz DTOクラス
     * @return フィールドメタデータの配列
     */
    private static FieldMetadata[] getFieldMetadata(Class<?> clazz) {
        return FIELD_METADATA_CACHE.computeIfAbsent(clazz, k -> {
            logger.debug("フィールドメタデータを生成中: {}", k.getSimpleName());

            Field[] fields = k.getDeclaredFields();
            FieldMetadata[] metadataArray = new FieldMetadata[fields.length];

            for (int i = 0; i < fields.length; i++) {
                metadataArray[i] = new FieldMetadata(fields[i]);
            }

            logger.debug("フィールドメタデータ生成完了: {} ({}個のフィールド)",
                    k.getSimpleName(), metadataArray.length);

            return metadataArray;
        });
    }

    /**
     * 指定されたクラスのフィールドメタデータを事前にキャッシュに読み込み
     * アプリケーション起動時やLambda冷起動時の性能向上のため
     *
     * @param clazz 事前読み込みするDTOクラス
     */
    public static void preloadMetadata(Class<?> clazz) {
        logger.info("フィールドメタデータを事前読み込み中: {}", clazz.getSimpleName());
        getFieldMetadata(clazz);
    }

    /**
     * キャッシュの統計情報を取得（デバッグ用）
     *
     * @return キャッシュされているクラス数
     */
    public static int getCacheSize() {
        return FIELD_METADATA_CACHE.size();
    }

    /**
     * キャッシュをクリア（テスト用）
     * 本番環境では使用しないでください
     */
    public static void clearCache() {
        logger.warn("フィールドメタデータキャッシュをクリアします");
        FIELD_METADATA_CACHE.clear();
    }


    /**
     * 値を指定された型に変換
     * 共通的な型変換をサポート
     *
     * @param value 元の値
     * @param targetType 変換先の型
     * @return 変換後の値
     */
    private static Object convertValue(Object value, Class<?> targetType) {
        if (value == null) {
            return null;
        }

        // 既に正しい型の場合はそのまま返す
        if (targetType.isAssignableFrom(value.getClass())) {
            return value;
        }

        String stringValue = value.toString().trim();
        if (stringValue.isEmpty()) {
            return null;
        }

        // String型の場合
        if (targetType == String.class) {
            return stringValue;
        }

        // 数値型の変換処理
        try {
            // Long型の場合
            if (targetType == Long.class || targetType == long.class) {
                return Long.parseLong(stringValue);
            }

            // Integer型の場合
            if (targetType == Integer.class || targetType == int.class) {
                return Integer.parseInt(stringValue);
            }

            // Double型の場合
            if (targetType == Double.class || targetType == double.class) {
                return Double.parseDouble(stringValue);
            }

            // Float型の場合
            if (targetType == Float.class || targetType == float.class) {
                return Float.parseFloat(stringValue);
            }

            // Short型の場合
            if (targetType == Short.class || targetType == short.class) {
                return Short.parseShort(stringValue);
            }

            // Byte型の場合
            if (targetType == Byte.class || targetType == byte.class) {
                return Byte.parseByte(stringValue);
            }
        } catch (NumberFormatException e) {
            // 無効な数値の場合はnullを返す（バリデーションでエラーになる）
            logger.debug("数値変換エラー、nullを返します: {} -> {}", stringValue, targetType.getSimpleName());
            return null;
        }

        // Boolean型の場合
        if (targetType == Boolean.class || targetType == boolean.class) {
            return Boolean.parseBoolean(stringValue);
        }

        // その他の型は文字列として返す
        logger.debug("サポートされていない型変換: {}, 文字列を返します", targetType.getSimpleName());
        return stringValue;
    }
}