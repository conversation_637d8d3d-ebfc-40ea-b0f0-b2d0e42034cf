package com.ms.bp.infrastructure.repository.impl;

import com.ms.bp.domain.master.model.UserBasicInfo;
import com.ms.bp.domain.master.repository.UserMasterRepository;
import com.ms.bp.infrastructure.repository.dao.UserMasterDataAccess;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.Optional;

/**
 * ユーザーマスタリポジトリ実装
 * M_SHAINMST（社員マスタ）へのデータアクセスを実装
 * UserXXX命名規則に従い、英文術語との一貫性を保持
 */
public class UserMasterRepositoryImpl implements UserMasterRepository {
    private static final Logger logger = LoggerFactory.getLogger(UserMasterRepositoryImpl.class);

    private final UserMasterDataAccess dataAccess;

    public UserMasterRepositoryImpl(JdbcTemplate jdbcTemplate) {
        this.dataAccess = new UserMasterDataAccess(jdbcTemplate);
    }

    public Optional<UserBasicInfo> findUserBasicInfo(String systemOperationCompanyCode, String shainCode) {
        try {
            Optional<UserBasicInfo> result = dataAccess.findUserBasicInfo(systemOperationCompanyCode, shainCode);
            if (result.isPresent()) {
                logger.debug("ユーザー基本情報を取得しました: 企業コード={}, 社員コード={}, ユニットコード={}, エリアコード={}, エリア名称={}",
                           systemOperationCompanyCode, shainCode, result.get().getUnitCode(), result.get().getAreaCode(), result.get().getAreaName());
            } else {
                logger.debug("ユーザー基本情報が見つかりませんでした: 企業コード={}, 社員コード={}",
                           systemOperationCompanyCode, shainCode);
            }
            return result;
        } catch (SQLException e) {
            logger.error("ユーザー基本情報取得中にエラーが発生しました: 企業コード={}, 社員コード={}",
                        systemOperationCompanyCode, shainCode, e);
            throw new RuntimeException("ユーザー基本情報取得に失敗しました", e);
        }
    }
}
