package com.ms.bp.application.data;


import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.application.ImportJobStatusService;
import com.ms.bp.domain.file.model.ImportJobStatus;
import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.infrastructure.external.lambda.AsyncLambdaInvoker;
import com.ms.bp.interfaces.dto.request.ImportRequest;
import com.ms.bp.interfaces.dto.request.WorkerPayload;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.db.AuroraConnectionManager;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.when;

class OutlookPlanImportServiceTest {

    // テストデータ
    private UserInfo testUserInfo;
    private ImportRequest testImportRequest;
    //private ExportRequest testExportRequest;

    private String fileType;

    private String rirekiNo;

    //private AreaOutlookPlanImportService areaOutlookPlanImportService;

    private DataApplicationService dataApplicationService;
    /**
     * AsyncLambdaInvokerのモックオブジェクト
     * テスト環境でAWS Lambda呼び出しを回避するために使用
     */
    @Mock
    private AsyncLambdaInvoker mockAsyncLambdaInvoker;

    @Mock
    private Context mockLambdaContext;

    @BeforeEach
    void init() {

        // テスト用データを初期化
        initializeTestData();
    }

    @AfterEach
    void afterExe(){
        // 登録したデータ削除、ファイル元位置に移動など
        //delUploadRrk();
    }


    @Test
    void testArea(){
        // ファイル種類設定
        fileType = BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE;
        //areaOutlookPlanImportService = new AreaOutlookPlanImportService(fileType);

        // 履歴番号取得
        //rirekiNo = getRirekiNo();

        // 各テストケースを呼び出し
        testFileCheckErr_headItem();
        testFileCheckErr_1件();
        testFileCheckErr_99件();
        testFileCheckErr_100件();
        testFileCheckErr_101件();
        testHisuCheckErr_1();
        testHisuCheckErr_2();
        testHisuCheckErr_3();
        testLengthCheckErr_1();
        testLengthCheckErr_2();
        testLengthCheckErr_3();
        testShosikiCheckErr_1();
        testShosikiCheckErr_2();
        testShosikiCheckErr_3();
        testKengenCheckErr_1();
        testKengenCheckErr_2();
        testKengenCheckErr_3();
        testSuccess_Ins();
        testSuccess_Upd();

        assertNotNull(fileType);
    }

    @Test
    void testHeadOffice(){
        // ファイル種類設定
        fileType = BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE;
        //areaOutlookPlanImportService = new AreaOutlookPlanImportService(fileType);

        // 履歴番号取得
        //rirekiNo = getRirekiNo();
        // 各テストケースを呼び出し
        testFileCheckErr_headItem();
        testFileCheckErr_1件();
        testFileCheckErr_99件();
        testFileCheckErr_100件();
        testFileCheckErr_101件();
        testHisuCheckErr_1();
        testHisuCheckErr_2();
        testHisuCheckErr_3();
        testLengthCheckErr_1();
        testLengthCheckErr_2();
        testLengthCheckErr_3();
        testShosikiCheckErr_1();
        testShosikiCheckErr_2();
        testShosikiCheckErr_3();
        testKengenCheckErr_1();
        testKengenCheckErr_2();
        testKengenCheckErr_3();
        testSuccess_Ins();
        testSuccess_Upd();

    }

    // --------------各テスト処理-------------------------


    @Test
    void testFileCheckErr_headItem(){

        //fileType = BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE;
        String filePath = "in/mtshKkkHnsh/2025/07/30/testFileCheckErr_headItem.csv";

        testImportRequest.setS3Key(filePath);
        testImportRequest.setDataType(fileType);

        testExecute();
    }

    @Test
    void testFileCheckErr_1件(){

        //fileType = BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE;
        String filePath = "in/mtshKkkHnsh/2025/07/30/testFileCheckErr_1件.csv";

        testImportRequest.setS3Key(filePath);
        testImportRequest.setDataType(fileType);

        testExecute();

    }

    @Test
    void testFileCheckErr_99件(){

        //fileType = BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE;
        String filePath = "in/mtshKkkHnsh/2025/07/30/testFileCheckErr_99件.csv";

        testImportRequest.setS3Key(filePath);
        testImportRequest.setDataType(fileType);

        testExecute();

    }

    @Test
    void testFileCheckErr_100件(){

        //fileType = BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE;
        String filePath = "in/mtshKkkHnsh/2025/07/30/testFileCheckErr_100件.csv";

        testImportRequest.setS3Key(filePath);
        testImportRequest.setDataType(fileType);

        testExecute();

    }

    @Test
    void testFileCheckErr_101件(){

        //fileType = BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE;
        String filePath = "in/mtshKkkHnsh/2025/07/30/testFileCheckErr_101件.csv";

        testImportRequest.setS3Key(filePath);
        testImportRequest.setDataType(fileType);

        testExecute();

    }

    @Test
    void testHisuCheckErr_1(){
        //  100個まで
        //fileType = BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE;
        String filePath = "in/mtshKkkHnsh/2025/07/30/testHisuCheckErr_1.csv";

        testImportRequest.setS3Key(filePath);
        testImportRequest.setDataType(fileType);

        testExecute();
    }

    @Test
    void testHisuCheckErr_2(){
        //  100-200個まで
        //fileType = BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE;
        String filePath = "in/mtshKkkHnsh/2025/07/30/testHisuCheckErr_2.csv";

        testImportRequest.setS3Key(filePath);
        testImportRequest.setDataType(fileType);

        testExecute();
    }

    @Test
    void testHisuCheckErr_3(){
        //  200から
        //fileType = BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE;
        String filePath = "in/mtshKkkHnsh/2025/07/30/testHisuCheckErr_3.csv";

        testImportRequest.setS3Key(filePath);
        testImportRequest.setDataType(fileType);

        testExecute();
    }

    @Test
    void testLengthCheckErr_1(){
        //  100個まで
        //fileType = BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE;
        String filePath = "in/mtshKkkHnsh/2025/07/30/testLengthCheckErr_1.csv";

        testImportRequest.setS3Key(filePath);
        testImportRequest.setDataType(fileType);

        testExecute();
    }

    @Test
    void testLengthCheckErr_2(){
        //  100-200個まで
        //fileType = BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE;
        String filePath = "in/mtshKkkHnsh/2025/07/30/testLengthCheckErr_2.csv";

        testImportRequest.setS3Key(filePath);
        testImportRequest.setDataType(fileType);

        testExecute();
    }

    @Test
    void testLengthCheckErr_3(){
        //  200から
        //fileType = BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE;
        String filePath = "in/mtshKkkHnsh/2025/07/30/testLengthCheckErr_3.csv";

        testImportRequest.setS3Key(filePath);
        testImportRequest.setDataType(fileType);

        testExecute();
    }

    @Test
    void testShosikiCheckErr_1(){
        //  100個まで
        //fileType = BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE;
        String filePath = "in/mtshKkkHnsh/2025/07/30/testShosikiCheckErr_1.csv";

        testImportRequest.setS3Key(filePath);
        testImportRequest.setDataType(fileType);

        testExecute();
    }

    @Test
    void testShosikiCheckErr_2(){
        //  100-200個まで
        //fileType = BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE;
        String filePath = "in/mtshKkkHnsh/2025/07/30/testShosikiCheckErr_2.csv";

        testImportRequest.setS3Key(filePath);
        testImportRequest.setDataType(fileType);

        testExecute();
    }

    @Test
    void testShosikiCheckErr_3(){
        //  200から
        //fileType = BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE;
        String filePath = "in/mtshKkkHnsh/2025/07/30/testShosikiCheckErr_3.csv";

        testImportRequest.setS3Key(filePath);
        testImportRequest.setDataType(fileType);

        testExecute();
    }

    @Test
    void testKengenCheckErr_1(){
        //fileType = BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE;
        //  パラメータ.エリアコードが、レスポンス.エリアリスト.エリアコードに含まれない
        String filePath = "in/mtshKkkHnsh/2025/07/30/testKengenCheckErr_1.csv";

        testImportRequest.setS3Key(filePath);
        testImportRequest.setDataType(fileType);
        testUserInfo.setAreaCode("8888");

        testExecute();
    }

    @Test
    void testKengenCheckErr_2(){
        //fileType = BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE;
        //  レスポンス.役職区分判定要否 = 1:要　の場合　AND
        //  レスポンス.役職区分 = 51（ＵＬ、ＤＣ長） または 61（非役職者）の場合
        String filePath = "in/mtshKkkHnsh/2025/07/30/testKengenCheckErr_2.csv";

        testImportRequest.setS3Key(filePath);
        testImportRequest.setDataType(fileType);
        testUserInfo.setPositionCode("51");

        testExecute();
    }

    @Test
    void testKengenCheckErr_3(){
        // 下記条件以外
        //  レスポンス.役職区分判定要否 = 1:要　の場合　AND
        //  レスポンス.役職区分 = 51（ＵＬ、ＤＣ長） または 61（非役職者）の場合
        //fileType = BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE;
        //fileType = BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE;
        String filePath = "in/mtshKkkHnsh/2025/07/30/testKengenCheckErr_3.csv";

        testImportRequest.setS3Key(filePath);
        testImportRequest.setDataType(fileType);
        testUserInfo.setPositionCode("02");

        testExecute();
    }

    @Test
    void testSuccess_Ins(){
        // 正常系、データなし（登録）

        //fileType = BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE;
        String filePath = "in/mtshKkkHnsh/2025/07/30/outlookplanins.csv";

        testImportRequest.setS3Key(filePath);
        testImportRequest.setDataType(fileType);

        testExecute();
    }

    @Test
    void testSuccess_Upd(){
        // 正常系、データあり（更新）
        //fileType = BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE;
        String filePath = "in/mtshKkkHnsh/2025/07/30/outlookplanins.csv";

        testImportRequest.setS3Key(filePath);
        testImportRequest.setDataType(fileType);

        testExecute();
    }

    private void testExecute()
    {
        try{
            //ImportResult rs = outlookPlanImportService.importData(new FileInputStream(filePath));

            // データベースに保存し、自動生成された履歴番号を取得
            String rrkBango = getRirekiNo();
            //String rrkBango = "218";
            // Worker Lambda用ペイロードを構築
            WorkerPayload payload = WorkerPayload.builder()
                    .jobId(rrkBango)
                    .operationType(BusinessConstants.OPERATION_UPLOAD_CODE)
                    .request(testImportRequest)
                    .userInfo(testUserInfo)
                    .build();


            // executeExportTask を実行
            assertDoesNotThrow(() -> {
                dataApplicationService.executeImportTask(payload, mockLambdaContext);
            });
        }catch(Exception e){

            System.err.println("testFileCheckErr_headItemでエラーが発生: " + e.getMessage());
        }
    }


    private String getRirekiNo()
    {
        String rireki = "";
        ImportJobStatusService importJobStatusService = new ImportJobStatusService();

        String functionId = "BAT_006";
        //UserInfo userInfo = createTestUserInfo();

        // ジョブステータスを初期化（データベースに保存）
        ImportJobStatus jobStatus = ImportJobStatus.builder()
                .systmUnyoKigyoCode(testUserInfo.getSystemOperationCompanyCode())
                .shainCode(testUserInfo.getShainCode())
                .fileShbts(fileType)
                .area(testUserInfo.getAreaCode())
                .build().init(functionId);

        // データベースに保存し、自動生成された履歴番号を取得
        rireki = importJobStatusService.createJob(jobStatus).toString();

        return rireki;
    }


    private String delUploadRrk()
    {
        String sql = " delete from T_UPLOAD_RRK where rrk_bango ='" + rirekiNo + "'";

        try (Connection connection = AuroraConnectionManager.getConnection();
             JdbcTemplate jdbcTemplate = new JdbcTemplate(connection)) {

            jdbcTemplate.update(sql, null);

        } catch (SQLException e) {
            System.err.println("PostgreSQL アップロード履歴削除エラー: " + e.getMessage());
            e.printStackTrace();

            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }

        return sql;
    }
    /**
     * テスト用データを初期化するメソッド
     * 各テストで使用する共通のテストデータを準備
     */
    private void initializeTestData() {
        try {
            // Mockito初期化
            MockitoAnnotations.openMocks(this);
            // AWS設定を環境変数で設定してAsyncLambdaInvokerの初期化エラーを回避
            System.setProperty("aws.region", "ap-northeast-1");
            System.setProperty("WORKER_FUNCTION_NAME", "test-worker-function");

            // 実際のDataApplicationServiceインスタンスを作成
            // 注意：これによりデータベース接続等の実際の初期化が実行される
            dataApplicationService = new DataApplicationService();

            // DataApplicationService内のAsyncLambdaInvokerをモックに置き換え
            // リフレクションを使用してプライベートフィールドにアクセス
            Field asyncLambdaInvokerField = DataApplicationService.class.getDeclaredField("asyncLambdaInvoker");
            asyncLambdaInvokerField.setAccessible(true);
            asyncLambdaInvokerField.set(dataApplicationService, mockAsyncLambdaInvoker);

            // モック動作を設定：非同期呼び出しは何もしない（例外を投げない）
            // lenientを使用してUnnecessaryStubbingExceptionを回避
            lenient().doNothing().when(mockAsyncLambdaInvoker).invokeAsync(any());

            setupMockBehaviors();

            System.out.println("DataApplicationService初期化完了（AsyncLambdaInvokerモック設定済み）");

        } catch (Exception e) {
            System.err.println("DataApplicationService初期化エラー: " + e.getMessage());
            e.printStackTrace();
            // 初期化に失敗した場合でもテストを継続するため、nullのままにする
            dataApplicationService = null;
        }
        // テスト用ユーザー情報を作成
        testUserInfo = createTestUserInfo();

        // テスト用インポートリクエストを作成
        testImportRequest = createTestImportRequest();
    }

    // ==================== プライベートヘルパーメソッド ====================

    // ==================== プライベートメソッド ====================
    /**
     * Mock オブジェクトの基本動作を設定
     */
    private void setupMockBehaviors() {
        try {
            // AsyncLambdaInvoker の Mock 設定
            doNothing().when(mockAsyncLambdaInvoker).invokeAsync(any(WorkerPayload.class));

            /*
            // S3Service の Mock 設定
            when(mockS3Service.uploadFileFromStream(any(), anyString(), anyString(), anyLong(), any()))
                    .thenReturn(Map.of("success", true, "key", "out/mtshKkkHnsh/次年度計画マスタ_20241201120000.zip"));

            when(mockS3Service.getSignedDownloadUrl(anyString(), anyLong()))
                    .thenReturn("https://test-s3-url.com/download");

            when(mockS3Service.getObjectMetadata(anyString()))
                    .thenReturn(Map.of("fileSize", 1024L));
             */

            // Lambda Context の Mock 設定
            when(mockLambdaContext.getRemainingTimeInMillis()).thenReturn(300000); // 5分
            when(mockLambdaContext.getAwsRequestId()).thenReturn("test-request-id");

        } catch (Exception e) {
            throw new RuntimeException("Mock 設定に失敗しました", e);
        }
    }

    /**
     * テスト用UserInfoオブジェクトを作成
     * 実際のユーザー情報に近い形でテストデータを準備
     */
    private UserInfo createTestUserInfo() {
        UserInfo userInfo = new UserInfo();
        userInfo.setShainCode("KK3113");
        userInfo.setSystemOperationCompanyCode(BusinessConstants.SYSTEM_OPERATION_COMPANY_CODE);
        userInfo.setUnitCode("13401");
        AreaInfo area = new AreaInfo("0000", "見通し計画登録テストエリア0000");
        AreaInfo area1 = new AreaInfo("0200", "見通し計画登録テストエリア0200");
        AreaInfo area2 = new AreaInfo("0401", "見通し計画登録テストエリア0401");
        userInfo.setAreaInfos(List.of(area, area1, area2));
        userInfo.setAreaCode("0900");
        userInfo.setAreaName("見通し計画登録テストエリア");
        userInfo.setPositionCode("51");
        userInfo.setGroupCode("0134");
        userInfo.setPositionSpecialCheck("1");
        return userInfo;
    }

    /**
     * テスト用ImportRequestオブジェクトを作成
     * 次年度計画マスタのインポートリクエストを模擬
     */
    private ImportRequest createTestImportRequest() {
        ImportRequest request = new ImportRequest();
        //request.setS3Key("in/jinendoKkkMst/見通し計画登録本社.csv");
        request.setS3Key("in/jinendoKkkMst/見通し計画登録本社.csv");
        request.setDataType(BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE);
        request.setArea(Arrays.asList("0001"));
        return request;
    }
}