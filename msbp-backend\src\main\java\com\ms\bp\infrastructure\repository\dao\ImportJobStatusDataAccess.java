package com.ms.bp.infrastructure.repository.dao;


import com.ms.bp.shared.common.db.JdbcTemplate;
import com.ms.bp.domain.file.model.ImportJobStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * インポートジョブステータスデータアクセス
 * T_IMPRT_RRK（インポート履歴テーブル）へのデータアクセスを管理
 */
public class ImportJobStatusDataAccess {
    private static final Logger logger = LoggerFactory.getLogger(ImportJobStatusDataAccess.class);

    private final JdbcTemplate jdbcTemplate;

    public ImportJobStatusDataAccess(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * インポートジョブステータスを挿入し、自動生成された履歴番号を返す
     * RRK_BANGOフィールドは自動生成されるため、INSERT文から除外
     * @param jobStatus インポートジョブステータス
     * @return 自動生成された履歴番号
     * @throws SQLException SQL実行エラー
     */
    public Long insert(ImportJobStatus jobStatus) throws SQLException {
        String sql = """
            INSERT INTO t_upload_rrk (
                SYSTM_UNYO_KIGYO_CODE, SHAIN_CODE, FILE_SHBTS, AREA,
                FILE_MEI, ERROR_FILE_MEI, UPLOAD_KSH_NCHJ, UPLOAD_KNR_NCHJ, STTS,
                TRK_PRGRM_ID, TRK_SYSTM_UNYO_KIGYO_CODE, TRK_SHAIN_CODE,
                KSHN_PRGRM_ID, KSHN_SYSTM_UNYO_KIGYO_CODE, KSHN_SHAIN_CODE,
                VRSN, RCRD_TRK_NCHJ, RCRD_KSHN_NCHJ
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;

        Object[] params = {
            jobStatus.getSystmUnyoKigyoCode(),
            jobStatus.getShainCode(),
            jobStatus.getFileShbts(),
            jobStatus.getArea(),
            jobStatus.getFileMei(),
            jobStatus.getErrorFileMei(),
            jobStatus.getUploadKshNchj(),
            jobStatus.getUploadKnrNchj(),
            jobStatus.getStts(),
            jobStatus.getTrkPrgrmId(),
            jobStatus.getTrkSystmUnyoKigyoCode(),
            jobStatus.getTrkShainCode(),
            jobStatus.getKshnPrgrmId(),
            jobStatus.getKshnSystmUnyoKigyoCode(),
            jobStatus.getKshnShainCode(),
            jobStatus.getVrsn(),
            jobStatus.getRcrdTrkNchj(),
            jobStatus.getRcrdKshnNchj()
        };

        // 自動生成された履歴番号を取得
        Long generatedRrkBango = jdbcTemplate.insertWithGeneratedKey(sql, params, "rrk_bango");

        logger.debug("インポートジョブステータスを挿入しました: rrkBango={}", generatedRrkBango);
        return generatedRrkBango;
    }

    /**
     * インポートジョブステータスを更新
     */
    public void update(ImportJobStatus jobStatus) throws SQLException {
        String sql = """
            UPDATE t_upload_rrk SET
                FILE_MEI = ?, ERROR_FILE_MEI = ?, UPLOAD_KNR_NCHJ = ?, STTS = ?,
                KSHN_PRGRM_ID = ?, KSHN_SYSTM_UNYO_KIGYO_CODE = ?, KSHN_SHAIN_CODE = ?,
                VRSN = ?, RCRD_KSHN_NCHJ = ?
            WHERE RRK_BANGO = ?
            """;

        Object[] params = {
            jobStatus.getFileMei(),
            jobStatus.getErrorFileMei(),
            jobStatus.getUploadKnrNchj(),
            jobStatus.getStts(),
            jobStatus.getKshnPrgrmId(),
            jobStatus.getKshnSystmUnyoKigyoCode(),
            jobStatus.getKshnShainCode(),
            jobStatus.getVrsn(),
            jobStatus.getRcrdKshnNchj(),
            jobStatus.getRrkBango()
        };

        int result = jdbcTemplate.update(sql, params);
        if (result != 1) {
            throw new SQLException("インポートジョブステータスの更新に失敗しました: " + jobStatus.getRrkBango());
        }
        
        logger.debug("インポートジョブステータスを更新しました: rrkBango={}", jobStatus.getRrkBango());
    }

    /**
     * 履歴番号でインポートジョブステータスを取得
     */
    public ImportJobStatus findByRrkBango(Long rrkBango) throws SQLException {
        String sql = """
            SELECT RRK_BANGO, SYSTM_UNYO_KIGYO_CODE, SHAIN_CODE, FILE_SHBTS, AREA,
                   FILE_MEI, ERROR_FILE_MEI, UPLOAD_KSH_NCHJ, UPLOAD_KNR_NCHJ, STTS,
                   TRK_PRGRM_ID, TRK_SYSTM_UNYO_KIGYO_CODE, TRK_SHAIN_CODE,
                   KSHN_PRGRM_ID, KSHN_SYSTM_UNYO_KIGYO_CODE, KSHN_SHAIN_CODE,
                   VRSN, RCRD_TRK_NCHJ, RCRD_KSHN_NCHJ
            FROM t_upload_rrk
            WHERE RRK_BANGO = ?
            """;

        return jdbcTemplate.queryForObject(sql, new Object[]{rrkBango}, this::mapRowToImportJobStatusCommon);
    }

    /**
     * 社員コードでインポートジョブステータスリストを取得
     * M_SOSHIKIAREAMSTテーブルと連表查询してエリア名も一緒に取得し、
     * SQL内でCASE WHEN文によりファイル種別・ステータス変換を実行して返却する
     */
    public List<ImportJobStatus> findByShainCode(String shainCode,String systemOperationCompanyCode, int limit, int offset) throws SQLException {
        String sql = """
            SELECT RRK_BANGO, t.SYSTM_UNYO_KIGYO_CODE, SHAIN_CODE, FILE_SHBTS, AREA,
                   FILE_MEI, ERROR_FILE_MEI, UPLOAD_KSH_NCHJ, UPLOAD_KNR_NCHJ, STTS,
                   TRK_PRGRM_ID, TRK_SYSTM_UNYO_KIGYO_CODE, TRK_SHAIN_CODE,
                   KSHN_PRGRM_ID, KSHN_SYSTM_UNYO_KIGYO_CODE, KSHN_SHAIN_CODE,
                   t.VRSN, t.RCRD_TRK_NCHJ, t.RCRD_KSHN_NCHJ,
                   -- ファイル種別をSQL内でCASE WHEN文により変換
                   CASE
                       WHEN FILE_SHBTS = '1' THEN '次年度計画マスタ'
                       WHEN FILE_SHBTS = '2' THEN '見通し・計画_採算管理単位C別＜本社＞'
                       WHEN FILE_SHBTS = '3' THEN '見通し・計画_採算管理単位C別＜エリア＞'
                       WHEN FILE_SHBTS = '4' THEN '間接利益計画_メーカー別'
                       ELSE COALESCE(FILE_SHBTS, '')
                   END AS FILE_SHBTS_NAME,
                   t.AREA AS AREA_NAME,
                   -- ステータスをSQL内でCASE WHEN文により変換
                   CASE
                       WHEN STTS = '0' THEN '処理中'
                       WHEN STTS = '1' THEN '完了'
                       WHEN STTS = '2' THEN '一部失敗'
                       WHEN STTS = '3' THEN '失敗'
                       WHEN STTS = '4' THEN '失敗'
                       ELSE COALESCE(STTS, '')
                   END AS STTS_NAME
            FROM t_upload_rrk t
            WHERE t.SHAIN_CODE = ?
             AND t.RCRD_TRK_NCHJ >= TO_CHAR(CURRENT_DATE - INTERVAL '10 days', 'YYYYMMDD') || '000000'
             AND t.SYSTM_UNYO_KIGYO_CODE = ?
            ORDER BY t.RCRD_TRK_NCHJ DESC
            LIMIT ? OFFSET ?
            """;

        return jdbcTemplate.query(sql, new Object[]{shainCode,systemOperationCompanyCode, limit, offset}, this::mapRowToImportJobStatus);
    }

    /**
     * ResultSetからImportJobStatusオブジェクトにマッピング（完全版）
     * findByShainCode用：変換された名称フィールドを含む完全なマッピング
     */
    private ImportJobStatus mapRowToImportJobStatus(ResultSet rs) throws SQLException {
        ImportJobStatus jobStatus = mapRowToImportJobStatusCommon(rs);
        // 名称フィールド（完全版のみ）
        jobStatus.setFileShbtsName(rs.getString("FILE_SHBTS_NAME")); // SQL内でCASE WHEN変換されたファイル種別名
        jobStatus.setAreaName(rs.getString("AREA_NAME")); // SQL内で取得されたエリア名
        jobStatus.setSttsName(rs.getString("STTS_NAME")); // SQL内でCASE WHEN変換されたステータス名
        return jobStatus;
    }

    /**
     * ResultSetからImportJobStatusオブジェクトにマッピング（共通処理）
     * @param rs ResultSet
     * @return ImportJobStatus
     * @throws SQLException SQL例外
     */
    private ImportJobStatus mapRowToImportJobStatusCommon(ResultSet rs) throws SQLException {
        ImportJobStatus jobStatus = new ImportJobStatus();

        // 主キー
        jobStatus.setRrkBango(rs.getLong("RRK_BANGO"));

        // 基本情報
        jobStatus.setSystmUnyoKigyoCode(rs.getString("SYSTM_UNYO_KIGYO_CODE"));
        jobStatus.setShainCode(rs.getString("SHAIN_CODE"));
        jobStatus.setFileShbts(rs.getString("FILE_SHBTS"));
        jobStatus.setArea(rs.getString("AREA"));
        jobStatus.setFileMei(rs.getString("FILE_MEI"));
        jobStatus.setErrorFileMei(rs.getString("ERROR_FILE_MEI"));

        // 処理日時
        jobStatus.setUploadKshNchj(rs.getString("UPLOAD_KSH_NCHJ"));
        jobStatus.setUploadKnrNchj(rs.getString("UPLOAD_KNR_NCHJ"));

        // ステータス
        jobStatus.setStts(rs.getString("STTS"));
        // 監査フィールド
        jobStatus.setTrkPrgrmId(rs.getString("TRK_PRGRM_ID"));
        jobStatus.setTrkSystmUnyoKigyoCode(rs.getString("TRK_SYSTM_UNYO_KIGYO_CODE"));
        jobStatus.setTrkShainCode(rs.getString("TRK_SHAIN_CODE"));
        jobStatus.setKshnPrgrmId(rs.getString("KSHN_PRGRM_ID"));
        jobStatus.setKshnSystmUnyoKigyoCode(rs.getString("KSHN_SYSTM_UNYO_KIGYO_CODE"));
        jobStatus.setKshnShainCode(rs.getString("KSHN_SHAIN_CODE"));

        // システム情報
        Integer vrsn = rs.getInt("VRSN");
        jobStatus.setVrsn(rs.wasNull() ? null : vrsn);
        jobStatus.setRcrdTrkNchj(rs.getString("RCRD_TRK_NCHJ"));
        jobStatus.setRcrdKshnNchj(rs.getString("RCRD_KSHN_NCHJ"));

        return jobStatus;
    }
}
