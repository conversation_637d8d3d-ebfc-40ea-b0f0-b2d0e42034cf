package com.ms.bp.application.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * インポートジョブ結果データ
 * updateJobメソッドのパラメータ簡素化のためのDTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ImportJobResult {

    
    // エラーファイル情報
    private String errorFileS3Key;
    
    /**
     * 完了時の結果データを作成
     */
    public static ImportJobResult completed(
                                          String errorFileS3Key) {
        return ImportJobResult.builder()
                .errorFileS3Key(errorFileS3Key)
                .build();
    }

    /**
     * 失敗時の結果データを作成
     */
    public static ImportJobResult failed(String errorFileS3Key) {
        return ImportJobResult.builder()
                .errorFileS3Key(errorFileS3Key)
                .build();
    }

}
