package com.ms.bp.application.data;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.infrastructure.external.lambda.AsyncLambdaInvoker;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.interfaces.dto.request.ImportRequest;
import com.ms.bp.interfaces.dto.request.WorkerPayload;
import com.ms.bp.shared.common.constants.BusinessConstants;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * DataApplicationServiceのexecuteImportTaskとexecuteExportTaskメソッドの単元テストクラス
 * 完整流程の集成テスト方式を採用し、Service層とRepository層はmock処理しない
 * AWS Lambda環境依存を回避するためAsyncLambdaInvokerのみmock処理を行う
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("DataApplicationService executeTask メソッド単元テスト")
public class DataApplicationServiceExecuteAreaTaskTest {

    // テスト対象（実際のインスタンス）
    private DataApplicationService dataApplicationService;

    /**
     * AsyncLambdaInvokerのモックオブジェクト
     * テスト環境でAWS Lambda呼び出しを回避するために使用
     */
    @Mock
    private AsyncLambdaInvoker mockAsyncLambdaInvoker;

    /**
     * Lambda実行コンテキストのモックオブジェクト
     */
    @Mock
    private Context mockContext;

    // テストデータ
    private UserInfo testUserInfo;
    private ImportRequest testImportRequest;
    private ExportRequest testExportRequest;

    @BeforeEach
    void setUp() {
        try {
            // AWS設定を環境変数で設定してAsyncLambdaInvokerの初期化エラーを回避
            System.setProperty("aws.region", "ap-northeast-1");

            // 実際のDataApplicationServiceインスタンスを作成
            // 注意：これによりデータベース接続等の実際の初期化が実行される
            dataApplicationService = new DataApplicationService();

            // DataApplicationService内のAsyncLambdaInvokerをモックに置き換え
            // リフレクションを使用してプライベートフィールドにアクセス
            Field asyncLambdaInvokerField = DataApplicationService.class.getDeclaredField("asyncLambdaInvoker");
            asyncLambdaInvokerField.setAccessible(true);
            asyncLambdaInvokerField.set(dataApplicationService, mockAsyncLambdaInvoker);

            // モック動作を設定：非同期呼び出しは何もしない（例外を投げない）
            // lenientを使用してUnnecessaryStubbingExceptionを回避
            lenient().doNothing().when(mockAsyncLambdaInvoker).invokeAsync(any());

            System.out.println("DataApplicationService初期化完了（AsyncLambdaInvokerモック設定済み）");

        } catch (Exception e) {
            System.err.println("DataApplicationService初期化エラー: " + e.getMessage());
            e.printStackTrace();
            // 初期化に失敗した場合でもテストを継続するため、nullのままにする
            dataApplicationService = null;
        }

        // テスト用データを初期化
        initializeTestData();
    }


    /**
     * テスト用データを初期化するメソッド
     * 各テストで使用する共通のテストデータを準備
     */
    private void initializeTestData() {
        testUserInfo=createTestUserInfo_area();
        testExportRequest = createTestExportRequest_area();
    }

    /**
     * executeExportTask正常実行フローテスト
     * Worker Lambda用エクスポート処理の完整流程を検証する
     * TaskOrchestrationServiceを通じて実際のエクスポート処理が実行されることを確認
     */
    @Test
    @DisplayName("executeExportTask_正常実行フロー_成功")
    void executeExportTask_正常実行フロー_成功() {
        System.out.println("=== executeExportTask正常実行フローテスト開始 ===");

        // Given: DataApplicationServiceが正常に初期化されていることを確認
        if (dataApplicationService == null) {
            System.out.println("DataApplicationService初期化失敗のため、テストをスキップします");
            return;
        }

        // Given: テスト用WorkerPayloadを作成（エクスポート用）
        WorkerPayload exportPayload = WorkerPayload.builder()
                .jobId("455")
                .operationType(BusinessConstants.OPERATION_DOWNLOAD_CODE)
                .request(testExportRequest)
                .userInfo(testUserInfo)
                .build();

        // Given: Lambda実行コンテキストのモック設定
        lenient().when(mockContext.getFunctionName()).thenReturn("test-export-function");
        lenient().when(mockContext.getAwsRequestId()).thenReturn("test-export-request-id");

        try {
            // When: executeExportTaskメソッドを実行
            System.out.println("executeExportTask実行開始...");
            dataApplicationService.executeExportTask(exportPayload, mockContext);

            // Then: 正常に実行完了することを確認（例外が発生しないこと）
            System.out.println("executeExportTaskが正常に実行完了しました");

        } catch (Exception e) {
            System.err.println("executeExportTaskでエラーが発生: " + e.getMessage());
            e.printStackTrace();

            // エラーが発生した場合でも、適切な例外処理が行われていることを確認
            // データベース接続エラーなどの場合は、RuntimeExceptionまたはServiceExceptionが期待される
            assertTrue(e instanceof RuntimeException || e instanceof com.ms.bp.shared.common.exception.ServiceException,
                    "適切な例外タイプであること");
        }
    }

    /**
     * テスト用UserInfoオブジェクトを作成
     * 実際のユーザー情報に近い形でテストデータを準備
     */
    private UserInfo createTestUserInfo_area() {
        UserInfo userInfo = new UserInfo();
        // userInfo.setShainCode("KK3113");
        userInfo.setShainCode("XXXXXX");
        // userInfo.setSystemOperationCompanyCode(BusinessConstants.SYSTEM_OPERATION_COMPANY_CODE);
        userInfo.setSystemOperationCompanyCode("300310");
        userInfo.setUnitCode("12345");
        AreaInfo area1 = new AreaInfo("8001", "単元テストエリア");
        AreaInfo area2 = new AreaInfo("8002", "単元テストエリア");
        userInfo.setAreaInfos(List.of(area1, area2));
        userInfo.setAreaCode("0300");
        userInfo.setAreaName("単元テストエリア");
        userInfo.setPositionCode("01");

        userInfo.setPositionSpecialCheck("0");
//        userInfo.setPositionCode("51");
//        userInfo.setGroupCode("0301");

        userInfo.setPositionCode("00");
        userInfo.setGroupCode("0302");
        return userInfo;
    }

    /**
     * テスト用ExportRequestオブジェクトを作成
     * 次年度計画マスタのエクスポートリクエストを模擬
     */
    private ExportRequest createTestExportRequest_area() {
        ExportRequest request = new ExportRequest();
        request.setDataType(BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE);
        request.setArea(Arrays.asList("1800","8002"));
        request.setHnshBashoKubun("0");
        request.setDataKubun(Arrays.asList(BusinessConstants.DATAKUBUN_IKO_BEFORE,BusinessConstants.DATAKUBUN_IKO_AFTER));
        return request;
    }
}
