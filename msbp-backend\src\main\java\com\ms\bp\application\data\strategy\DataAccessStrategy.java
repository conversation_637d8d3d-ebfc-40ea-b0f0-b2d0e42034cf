package com.ms.bp.application.data.strategy;

import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.request.ExportRequest;

import java.util.Map;

/**
 * SQL構築戦略インターフェース
 * 異なるエクスポートタスクに対して異なるSQLを構築するための戦略パターン
 */
public interface DataAccessStrategy {

    /**
     * SQLクエリとパラメータを保持する内部クラス
     */
    record SqlWithParams(String sql, Map<String, Object> params) {}

    /**
     * エクスポートリクエストとユーザー情報に基づいてSQLクエリを構築
     * @param exportRequest エクスポートリクエスト
     * @param userInfo ユーザー情報
     * @return SQLクエリとパラメータ
     */
    SqlWithParams buildQuery(ExportRequest exportRequest, UserInfo userInfo);

    /**
     * 戦略名を取得
     * @return 戦略名
     */
    String getStrategyName();

}
