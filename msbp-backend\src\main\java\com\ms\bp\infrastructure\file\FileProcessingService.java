package com.ms.bp.infrastructure.file;

import com.ms.bp.domain.file.base.AbstractImportService;
import com.ms.bp.infrastructure.external.s3.S3Service;
import com.ms.bp.interfaces.dto.request.ImportRequest;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.io.model.ImportResult;
import com.ms.bp.shared.util.ExportFileNameUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;

/**
 * ファイル操作サービス
 * ファイル操作の純粋な技術的処理のみを担当
 */
public class FileProcessingService {
    private static final Logger logger = LoggerFactory.getLogger(FileProcessingService.class);

    private final S3Service s3Service;

    public FileProcessingService() {
        this.s3Service = new S3Service();
    }

    /**
     * インポート実行（技術的処理のみ）
     * @param importRequest インポートリクエスト
     * @param importService インポートサービス
     * @return インポート結果
     */
    public ImportResult executeImport(ImportRequest importRequest, AbstractImportService<?> importService) {
        logger.info("ファイルインポート実行開始: S3Key={}", importRequest.getS3Key());
        try (ResponseInputStream<GetObjectResponse> inputStream =
                     s3Service.getInputStreamFromS3Url(importRequest.getS3Key())) {
            // S3からファイル取得してインポート実行
            ImportResult result = importService.importData(inputStream);
            logger.info("ファイルインポート実行完了");
            return result;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            logger.error("ファイルインポート実行エラー", e);
            throw new ServiceException(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                    "ファイルインポート実行中にエラーが発生しました: " + e.getMessage());
        }
    }

    /**
     * S3にファイルをアップロード（技術的処理のみ）
     * データタイプに応じた専用ディレクトリにアップロード
     * @param zipFile ZIPファイル
     * @param jobId ジョブID
     * @param dataType データタイプ（機能識別用）
     * @return ダウンロードURL
     */
    public String uploadToS3(Path zipFile, String jobId, String dataType) {
        try {
            long fileSize = Files.size(zipFile);

            // データタイプに応じたS3ディレクトリパスを取得
            String s3Directory = ExportFileNameUtil.getS3DirectoryByDataType(dataType);
            String s3Key = String.format("%s/%s", s3Directory, zipFile.getFileName().toString());

            try (InputStream fileStream = Files.newInputStream(zipFile)) {
                Map<String, Object> metadata = new HashMap<>();
                metadata.put("format", "zip");
                metadata.put("exportedAt", new Date().toString());
                metadata.put("fileSize", String.valueOf(fileSize));
                metadata.put("jobId", jobId);

                Map<String, Object> uploadResult = s3Service.uploadFileFromStream(
                        fileStream, s3Key, "application/zip", fileSize, metadata);

                if ((Boolean) uploadResult.get("success")) {
                    return s3Key;
                } else {
                    throw new ServiceException(GlobalMessageConstants.S3_OPERATION_FAILED.getCode(),
                            "S3へのアップロードに失敗しました");
                }
            }
        } catch (Exception e) {
            logger.error("S3アップロードエラー: jobId={}", jobId, e);
            throw new ServiceException(GlobalMessageConstants.S3_OPERATION_FAILED.getCode(),
                    "S3アップロードに失敗しました: " + e.getMessage());
        }
    }
}