package com.ms.bp.interfaces.rest.controller;

import com.ms.bp.shared.common.CommonResult;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.interfaces.dto.response.UploadUrlResponse;
import com.ms.bp.interfaces.dto.response.DownloadUrlResponse;
import com.ms.bp.interfaces.dto.request.DownloadUrlRequest;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.infrastructure.external.s3.S3Service;
import com.ms.bp.infrastructure.external.s3.UploadUrlInfo;
import com.ms.bp.application.data.DataApplicationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;

/**
 * ファイルコントローラー
 * ファイルアップロード、ダウンロード、管理の処理を行う
 */
public class FileController {
    private static final Logger logger = LoggerFactory.getLogger(FileController.class);

    private final S3Service s3Service;
    private final DataApplicationService dataApplicationService;
    private final ObjectMapper objectMapper;

    public FileController() {
        this.s3Service = new S3Service();
        this.dataApplicationService = new DataApplicationService();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * アップロード用の署名付きURLを生成
     * ファイル種別に応じて動的にS3パスを生成し、
     * 署名付きURLとS3キーパスの両方を含むレスポンスを返却する
     *
     * @param request APIGatewayリクエスト
     * @param userInfo 認証されたユーザー情報
     * @return 署名付きURLとS3キーパスを含む共通レスポンス
     */
    public CommonResult<?> generateUploadUrl(APIGatewayProxyRequestEvent request, UserInfo userInfo) {
        try {
            logger.info("アップロードURL生成リクエスト: ユーザー={}", userInfo.getShainCode());

            // リクエストボディからファイル種別パラメータを取得
            String requestBody = request.getBody();
            if (requestBody == null || requestBody.trim().isEmpty()) {
                throw new ServiceException(GlobalMessageConstants.BAD_REQUEST.getCode(),
                        "リクエストボディが必要です");
            }

            // JSONからパラメータを解析
            var requestData = objectMapper.readValue(requestBody, java.util.Map.class);
            String fileType = (String) requestData.get("fileType");

            if (fileType == null || fileType.trim().isEmpty()) {
                throw new ServiceException(GlobalMessageConstants.BAD_REQUEST.getCode(),
                        "fileTypeパラメータが必要です");
            }

            // ファイル種別に基づいて署名付きURL情報を生成（URLとS3キーの両方を取得）
            UploadUrlInfo uploadUrlInfo = s3Service.getSignedUploadUrl(fileType);

            // レスポンスの作成
            UploadUrlResponse response = new UploadUrlResponse();
            response.setUploadUrl(uploadUrlInfo.getUploadUrl());
            response.setS3Key(uploadUrlInfo.getS3Key()); // S3キーパスを設定
            response.setExpiresIn(300); // 5分

            logger.info("アップロードURL生成成功: fileType={}, uploadUrl={}, s3Key={}",
                    fileType, uploadUrlInfo.getUploadUrl(), uploadUrlInfo.getS3Key());
            return CommonResult.success(response);

        } catch (ServiceException e) {
            // ServiceExceptionはそのまま再スロー
            throw e;
        } catch (Exception e) {
            logger.error("予期しないエラー: {}", e.getMessage(), e);
            throw new ServiceException(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                    "アップロードURL生成中にエラーが発生しました");
        }
    }

    /**
     * ダウンロード用の署名付きURLを生成
     * 履歴番号と操作タイプに基づいて対応する履歴テーブルから情報を取得し、
     * ファイル情報に基づいてS3ダウンロードURLを生成する
     *
     * @param request APIGatewayリクエスト
     * @param userInfo 認証されたユーザー情報
     * @return 署名付きURLを含む共通レスポンス
     */
    public CommonResult<?> generateDownloadUrl(APIGatewayProxyRequestEvent request, UserInfo userInfo) {
        try {
            logger.info("ダウンロードURL生成リクエスト: ユーザー={}", userInfo.getShainCode());

            // リクエストボディからパラメータを取得
            String requestBody = request.getBody();
            if (requestBody == null || requestBody.trim().isEmpty()) {
                throw new ServiceException(GlobalMessageConstants.BAD_REQUEST.getCode(),
                        "リクエストボディが必要です");
            }

            // JSONからDownloadUrlRequestオブジェクトに変換
            DownloadUrlRequest downloadRequest = objectMapper.readValue(requestBody, DownloadUrlRequest.class);

            // デフォルト値を適用
            downloadRequest.setExpiresInSeconds(3600);

            // リクエストパラメータの妥当性を検証
            downloadRequest.validate();

            logger.debug("ダウンロードURL生成リクエスト詳細: {}", downloadRequest);

            // DataApplicationServiceに処理を委譲
            DownloadUrlResponse response = dataApplicationService.generateDownloadUrlByHistory(downloadRequest, userInfo);

            logger.info("ダウンロードURL生成成功: {}, ダウンロード用署名付きURL={}", downloadRequest, response.getDownloadUrl());
            return CommonResult.success(response);

        } catch (ServiceException e) {
            // ServiceExceptionはそのまま再スロー
            throw e;
        } catch (Exception e) {
            logger.error("予期しないエラー: {}", e.getMessage(), e);
            throw new ServiceException(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                    "ダウンロードURL生成中にエラーが発生しました");
        }
    }

}