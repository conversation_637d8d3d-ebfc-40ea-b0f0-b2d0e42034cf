package com.ms.bp.infrastructure.repository.dao;

import com.ms.bp.domain.master.model.UserBasicInfo;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Optional;

/**
 * ユーザーマスタデータアクセス実装
 * M_SHAINMST（社員マスタ）への具体的なデータアクセスを実装
 * UserXXX命名規則に従い、英文術語との一貫性を保持
 */
public class UserMasterDataAccess {
    private static final Logger logger = LoggerFactory.getLogger(UserMasterDataAccess.class);

    // SQL定数
    private static final String FIND_USER_BASIC_INFO_SQL = """
        SELECT
            s.UNIT_CODE,
            s.YKSHK_KUBUN,
            g.AREA_CODE,
            a.area_mei_tnshk_kanji,
            s.SHAIN_CODE,
            s.SYSTM_UNYO_KIGYO_CODE,
            g.GROUP_CODE
        FROM M_SHAINMST s
        INNER JOIN M_UNITMST u ON s.UNIT_CODE = u.UNIT_CODE
        INNER JOIN M_GROUPMST g ON u.GROUP_CODE = g.GROUP_CODE
        INNER JOIN M_SOSHIKIAREAMST a ON g.AREA_CODE = a.AREA_CODE
        WHERE s.SYSTM_UNYO_KIGYO_CODE = ?
        AND s.SHAIN_CODE = ?
        AND s.SHIYO_KNSH_KUBUN  = '0'
        AND CURRENT_DATE::CHAR(8) >= u.KSHB AND u.SHRYB >= CURRENT_DATE::CHAR(8) AND u.SHIYO_KNSH_KUBUN  = '0'
        AND CURRENT_DATE::CHAR(8) >= g.KSHB AND g.SHRYB >= CURRENT_DATE::CHAR(8) AND g.SHIYO_KNSH_KUBUN  = '0'
        ORDER BY g.KSHB ASC
        LIMIT 1
        """;

    private final JdbcTemplate jdbcTemplate;

    public UserMasterDataAccess(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * ユーザーコードとシステム運用企業コードでユーザー基本情報を取得
     * 社員マスタ、ユニットマスタ、グループマスタ、組織エリアマスタを連携して一括取得
     * 使用禁止区分が'1'でない有効なレコードのみを対象とする
     * 複数のサブエリアコードが存在する場合は、開始日が最も古いレコードを取得
     *
     * @param systemOperationCompanyCode システム運用企業コード
     * @param shainCode 社員コード
     * @return ユーザー基本情報（ユニットコード、役職区分コード、エリアコード、エリア名称を含む）
     * @throws SQLException データベースアクセスエラー
     */
    public Optional<UserBasicInfo> findUserBasicInfo(String systemOperationCompanyCode, String shainCode) throws SQLException {
        logger.debug("ユーザー基本情報検索開始: 企業コード={}, 社員コード={}", systemOperationCompanyCode, shainCode);
        
        List<UserBasicInfo> results = jdbcTemplate.query(
            FIND_USER_BASIC_INFO_SQL,
            new Object[]{systemOperationCompanyCode, shainCode},
            this::mapRowToEmployeeBasicInfo
        );

        if (results.isEmpty()) {
            logger.debug("ユーザー基本情報が見つかりませんでした: 企業コード={}, 社員コード={}",
                        systemOperationCompanyCode, shainCode);
            return Optional.empty();
        }

        UserBasicInfo result = results.getFirst();
        logger.debug("ユーザー基本情報を取得しました: 企業コード={}, 社員コード={}, ユニットコード={}, 役職区分={}, エリアコード={}, エリア名称={}",
                    systemOperationCompanyCode, shainCode, result.getUnitCode(), result.getPositionCode(), result.getAreaCode(), result.getAreaName());
        
        return Optional.of(result);
    }

    /**
     * ResultSetからユーザー基本情報オブジェクトにマッピング
     * 連携クエリの結果をEmployeeBasicInfoオブジェクトにマッピング
     * 社員マスタ、ユニットマスタ、グループマスタ、組織エリアマスタの結合結果を処理
     *
     * @param rs ResultSet
     * @return ユーザー基本情報（ユニットコード、役職区分コード、エリアコード、エリア名称を含む）
     * @throws SQLException マッピングエラー
     */
    private UserBasicInfo mapRowToEmployeeBasicInfo(ResultSet rs) throws SQLException {
        return new UserBasicInfo(
            rs.getString("UNIT_CODE"),
            rs.getString("YKSHK_KUBUN"),
            rs.getString("AREA_CODE"),
            rs.getString("area_mei_tnshk_kanji"),
            rs.getString("SHAIN_CODE"),
            rs.getString("SYSTM_UNYO_KIGYO_CODE"),
            rs.getString("GROUP_CODE")
        );
    }
}
