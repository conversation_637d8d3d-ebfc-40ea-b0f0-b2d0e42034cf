package com.ms.bp.shared.common.exception;

import com.ms.bp.shared.util.MessageFormatter;
import lombok.Getter;

/**
 * パラメータ化されたエラーコードクラス
 * メッセージテンプレートとパラメータを使用して動的にエラーメッセージを生成
 */
@Getter
public class ParameterizedErrorCode {

    /**
     * エラーコード
     */
    private final Integer code;
    
    /**
     * メッセージテンプレート（例：「パラメータ：{0}、エラー内容：{1}」）
     */
    private final String messageTemplate;
    
    /**
     * メッセージパラメータ
     */
    private final Object[] parameters;
    
    /**
     * フォーマット済みメッセージ（キャッシュ用）
     */
    private String formattedMessage;

    /**
     * コンストラクタ
     * 
     * @param code エラーコード
     * @param messageTemplate メッセージテンプレート
     * @param parameters メッセージパラメータ
     */
    public ParameterizedErrorCode(Integer code, String messageTemplate, Object... parameters) {
        this.code = code;
        this.messageTemplate = messageTemplate;
        this.parameters = parameters != null ? parameters.clone() : new Object[0];
    }

    /**
     * 既存のErrorCodeからParameterizedErrorCodeを作成
     * 
     * @param message 既存のエラーコード
     * @param parameters メッセージパラメータ
     */
    public ParameterizedErrorCode(Message message, Object... parameters) {
        this(message.getCode(), message.getMsg(), parameters);
    }

    /**
     * フォーマット済みメッセージを取得
     * 初回呼び出し時にフォーマットを実行し、結果をキャッシュ
     * 
     * @return フォーマット済みメッセージ
     */
    public String getFormattedMessage() {
        if (formattedMessage == null) {
            formattedMessage = MessageFormatter.format(messageTemplate, parameters);
        }
        return formattedMessage;
    }


    /**
     * ErrorCodeオブジェクトとして変換
     * 
     * @return ErrorCodeオブジェクト
     */
    public Message toErrorCode() {
        return new Message(code, getFormattedMessage());
    }

    @Override
    public String toString() {
        return "ParameterizedErrorCode{" +
                "code=" + code +
                ", messageTemplate='" + messageTemplate + '\'' +
                ", parameters=" + java.util.Arrays.toString(parameters) +
                ", formattedMessage='" + getFormattedMessage() + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        ParameterizedErrorCode that = (ParameterizedErrorCode) o;
        
        if (!code.equals(that.code)) return false;
        if (!messageTemplate.equals(that.messageTemplate)) return false;
        return java.util.Arrays.equals(parameters, that.parameters);
    }

    @Override
    public int hashCode() {
        int result = code.hashCode();
        result = 31 * result + messageTemplate.hashCode();
        result = 31 * result + java.util.Arrays.hashCode(parameters);
        return result;
    }
}
