package com.ms.bp.application.data;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.application.ExportJobStatusService;
import com.ms.bp.domain.file.model.ExportJobStatus;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.infrastructure.external.lambda.AsyncLambdaInvoker;
import com.ms.bp.infrastructure.external.s3.S3Service;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.interfaces.dto.request.WorkerPayload;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.util.DateUtil;
import com.ms.bp.util.CsvContentComparator;
import com.ms.bp.util.TestDataManager;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.*;
import org.mockito.Mock;
import java.io.IOException;
import java.time.LocalDateTime;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.when;

/**
 * DataApplicationService.executeExportTask メソッド集成テスト
 * 見通し・計画_本社_移管エクスポート機能の完全な業務フローを検証
 * <p>
 * テスト対象コンポーネント：
 * - HeadOfficeOutlookPlanExportService: 見通し・計画_本社_移管取得エクスポートサービス
 * - HeadOfficeOutlookPlanCompanyDataStrategy: 見通し・計画_本社_移管_企業別データ取得戦略
 * - HeadOfficeOutlookPlanUnitDataStrategy: 見通し・計画_本社_移管_C別データ取得戦略
 * - HeadOfficeOutlookPlanFileSplitStrategy: 見通し・計画_本社_移管_取得ファイル分割戦略
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class HeadOfficeOutlookPlanExportServiceTest {

    private static final Logger logger = LoggerFactory.getLogger(HeadOfficeOutlookPlanExportServiceTest.class);

    private DataApplicationService dataApplicationService;

    // エクスポート用Excel テストデータ管理器
    private TestDataManager testDataManager;

    // 挿入されたテストデータの追跡情報
    private Map<String, List<Map<String, Object>>> insertedDataTracker;

    // Mock オブジェクト
    @Mock
    private AsyncLambdaInvoker mockAsyncLambdaInvoker;

    @Mock
    private S3Service mockS3Service;

    @Mock
    private Context mockLambdaContext;

    /**
     * ケース毎実施前作業
     */
    @BeforeEach
    void setUp() {
        logger.info("=== executeExportTask集成テストセットアップ開始 ===");

        try {
            // Mockito初期化
            MockitoAnnotations.openMocks(this);

            // AWS設定を環境変数で設定してAsyncLambdaInvokerの初期化エラーを回避
            System.setProperty("aws.region", "ap-northeast-1");
            System.setProperty("WORKER_FUNCTION_NAME", "test-worker-function");

            // テスト対象
            dataApplicationService = new DataApplicationService();

            // DataApplicationService内のAsyncLambdaInvokerをモックに置き換え
            // リフレクションを使用してプライベートフィールドにアクセス
            Field asyncLambdaInvokerField = DataApplicationService.class.getDeclaredField("asyncLambdaInvoker");
            asyncLambdaInvokerField.setAccessible(true);
            asyncLambdaInvokerField.set(dataApplicationService, mockAsyncLambdaInvoker);

            // S3Serviceもモックに置き換え（FileProcessingServiceとTaskOrchestrationService内のS3Service）
            // DataApplicationService内のTaskOrchestrationServiceのS3Serviceを置き換える必要がある
            Field taskOrchestrationServiceField = DataApplicationService.class.getDeclaredField("taskOrchestrationService");
            taskOrchestrationServiceField.setAccessible(true);
            Object taskOrchestrationService = taskOrchestrationServiceField.get(dataApplicationService);

            // TaskOrchestrationService内のFileExportOrchestratorを取得
            Field fileExportOrchestratorField = taskOrchestrationService.getClass().getDeclaredField("fileExportOrchestrator");
            fileExportOrchestratorField.setAccessible(true);
            Object fileExportOrchestrator = fileExportOrchestratorField.get(taskOrchestrationService);

            // FileExportOrchestrator内のFileProcessingServiceを取得
            Field fileProcessingServiceField = fileExportOrchestrator.getClass().getDeclaredField("fileProcessingService");
            fileProcessingServiceField.setAccessible(true);
            Object fileProcessingService = fileProcessingServiceField.get(fileExportOrchestrator);

            // FileProcessingService内のS3Serviceを置き換え
            Field s3ServiceField = fileProcessingService.getClass().getDeclaredField("s3Service");
            s3ServiceField.setAccessible(true);
            s3ServiceField.set(fileProcessingService, mockS3Service);

            // Mock の基本設定
            setupMockBehaviors();

            logger.info("=== executeExportTask集成テストセットアップ完了 ===");

        } catch (Exception e) {
            logger.error("テストセットアップエラー: {}", e.getMessage(), e);
            throw new RuntimeException("テストセットアップに失敗しました", e);
        }
    }

    /**
     * ケース毎実施後作業
     */
    @AfterEach
    void tearDown() {
        logger.info("=== executeExportTask集成テストクリーンアップ開始 ===");

        // 挿入したテストデータを削除
        if (insertedDataTracker != null) {
            testDataManager.deleteAllTestData(insertedDataTracker);
        }

        logger.info("=== executeExportTask集成テストクリーンアップ完了 ===");
    }

    /**
     * 試験対象：見通し・計画_本社_移管前_エクスポート
     * 試験条件：採算管理単位コード:"1118888"と"1118888"以外が混在
     *         次年度計画マスタ: 移管先エリアコードがNULLとNULL以外が設定有
     *         採算管理単位マスタ: 採算管理単位名漢字が空白で存在、次年度計画マスタに存在する採算管理単位コードが設定されない
     *         本社＿見通し・計画_採算管理単位C別:
     *         採算管理単位C別_直接_実績:
     *            -採算管理単位A:全項目が四捨五入を満たしなくて設定
     *            -採算管理単位A以外:全項目が四捨五入を満たして設定
     * 確認内容：エクスポートは複数件があり、ファイル名が日付以外は想定と一致
     *         ソート:カテゴリコード 、ｸﾞﾙｰﾌﾟCD 、 ﾕﾆｯﾄCD 、 担当者コード 、企業CD 、業態コード 、採算CD7桁
     *         移管先部署:
     *            -次年度計画マスタ.移管先エリアコードを取得できない場合は、""（空）を設定
     *            -次年度計画マスタ.移管先エリアコードを取得できる場合は、移管先エリアコードを設定
     *         採算名:
     *            -採算管理単位名漢字が空白:次年度計画マスタ.採算管理単位名漢字で設定
     *            -次年度計画マスタに存在する採算管理単位コードが設定されない:次年度計画マスタ.採算管理単位名漢字で設定
     *            -上記以外:採算管理単位マスタ.採算管理単位名漢字で設定
     *         計画項目:
     *            -採算管理単位A: 4月~3月_全項目が千円へ換算し_四捨五入しない
     *            -採算管理単位A以外: 4月~3月_全項目が千円へ換算し_四捨五入
     *         実績項目:
     *            -採算管理単位A: 4月~12月_全項目が元の値のまま、1月~3月_全項目が千円へ換算し_四捨五入しない
     *            -採算管理単位A以外: 4月~12月_全項目が元の値のまま、1月~3月_全項目が千円へ換算し_四捨五入
     *         ダウンロードステータスが完了になる
     */
    @Test
    @Order(1)
    @DisplayName("本社_移管前_エクスポート_正常系検証")
    void testExecuteExportTask_本社_移管前_エクスポート_正常系() {
        logger.info("=== 本社_移管前_エクスポート_正常系テスト開始 ===");

        String insertDataPath = "headofficeoutlookplan/insertdata/headoffice_outlookplan_export_test_data_1.xlsx";
        String expectDataPath = "headofficeoutlookplan/expectdata/本社_移管前_エクスポート_正常系.xlsx";
        List<String> areas=List.of("9901");
        List<String> dataKubunList=List.of("0");//移管前
        DateUtil.setTestFixedDateTime("2099");
        DateUtil.setTestSystemDateTime(LocalDateTime.parse("2028-10-26T10:30:00"));
        testDataManager = new TestDataManager(insertDataPath);
        insertedDataTracker = testDataManager.insertAllTestData();

        // 実行と実行前後の履歴確認
        doExecute(areas,dataKubunList,BusinessConstants.BATCH_STATUS_COMPLETED_CODE);

        // CSV内容詳細検証を追加
        verifyCsvFileContent(areas,expectDataPath,dataKubunList);

        logger.info("=== 本社_移管前_エクスポート_正常系テスト完了 ===");
    }

    /**
     * 試験対象：見通し・計画_本社_移管後_エクスポート
     * 試験条件：採算管理単位コード:"1118888"と"1118888"以外が混在
     *         移管後情報については、下記二つパラメータが設定
     *            -A:次年度計画マスタ.エリアコード={パラメータ.エリアコード} AND 次年度計画マスタ.移管先エリアコード<>""
     * 	          -B:次年度計画マスタ.移管先エリアコード = {パラメータ.エリアコード}
     *         採算管理単位マスタ: 採算管理単位名漢字が空白で存在、次年度計画マスタに存在する採算管理単位コードが設定されない
     *         本社＿見通し・計画_採算管理単位C別:
     *         採算管理単位C別_直接_実績:
     *            -採算管理単位A:全項目が四捨五入を満たしなくて設定
     *            -採算管理単位A以外:全項目が四捨五入を満たして設定
     * 確認内容：エクスポートは複数件がある、ファイル名が日付以外は想定と一致
     *         ソート:カテゴリコード 、ｸﾞﾙｰﾌﾟCD 、 ﾕﾆｯﾄCD 、 担当者コード 、企業CD 、業態コード 、採算CD7桁
     *         移管元部署:
     *            -B の場合　次年度計画マスタ.エリアコードを設定
     *            -上記以外は""（空）
     *         採算名:
     *            -採算管理単位名漢字が空白:次年度計画マスタ.採算管理単位名漢字で設定
     *            -次年度計画マスタに存在する採算管理単位コードが設定されない:次年度計画マスタ.採算管理単位名漢字で設定
     *            -上記以外:採算管理単位マスタ.採算管理単位名漢字で設定
     *         計画項目:
     *            -採算管理単位A: 4月~3月_全項目が千円へ換算し_四捨五入しない
     *            -採算管理単位A以外: 4月~3月_全項目が千円へ換算し_四捨五入
     *         実績項目:
     *            -採算管理単位A: 4月~12月_全項目が元の値のまま、1月~3月_全項目が千円へ換算し_四捨五入しない
     *            -採算管理単位A以外: 4月~12月_全項目が元の値のまま、1月~3月_全項目が千円へ換算し_四捨五入
     *         ダウンロードステータスが完了になる
     */
    @Test
    @Order(2)
    @DisplayName("本社_移管後_エクスポート_正常系検証")
    void testExecuteExportTask_本社_移管後_エクスポート_正常系() {
        logger.info("=== 本社_移管後_エクスポート_正常系テスト開始 ===");

        String insertDataPath = "headofficeoutlookplan/insertdata/headoffice_outlookplan_export_test_data_2.xlsx";
        String expectDataPath = "headofficeoutlookplan/expectdata/本社_移管後_エクスポート_正常系.xlsx";
        List<String> areas=List.of("9901");
        List<String> dataKubunList=List.of("1");//移管後
        DateUtil.setTestFixedDateTime("2099");
        DateUtil.setTestSystemDateTime(LocalDateTime.parse("2028-11-26T10:30:00"));
        testDataManager = new TestDataManager(insertDataPath);
        insertedDataTracker = testDataManager.insertAllTestData();

        // 実行と実行前後の履歴確認
        doExecute(areas,dataKubunList,BusinessConstants.BATCH_STATUS_COMPLETED_CODE);

        // CSV内容詳細検証を追加
        verifyCsvFileContent(areas,expectDataPath,dataKubunList);

        logger.info("=== 本社_移管後_エクスポート_正常系テスト完了 ===");
    }

    /**
     * 試験対象：見通し・計画_本社_移管前と移管後_エクスポート
     * 試験条件：エリア:「SKSA」と単独エリアコードを選択される(複数エリア検証)
     *         本社＿見通し・計画_採算管理単位C別:計画項目、実績項目に金額が各項目いずれかが1で設定
     *         本採算管理単位C別_直接_実績:実績項目に金額が各項目いずれかが1で設定
     *         次年度計画マスタ:下記配置で設定
     *            エリア１:No1-No120
     *            エリア２:No121-No240
     *            カテゴリー１:No1-No50
     *            カテゴリー２:No51-No100
     *            カテゴリー３:No101-No150
     *            カテゴリー４:No151-No200
     *            カテゴリー５:No201-No240
     * 確認内容：エクスポートは複数件がある、ファイル名が日付以外は想定と一致
     *         ソート:カテゴリコード 、ｸﾞﾙｰﾌﾟCD 、 ﾕﾆｯﾄCD 、 担当者コード 、企業CD 、業態コード 、採算CD7桁
     *         計画項目または、実績項目に金額が各項目いずれかが1以上:出力対象
     *         ダウンロードステータスが完了になる
     */
    @Test
    @Order(3)
    @DisplayName("本社_移管前後_エクスポート_正常系検証")
    void testExecuteExportTask_本社_移管前後_エクスポート_正常系() {
        logger.info("=== 本社_移管前後_エクスポート_正常系テスト開始 ===");

        String insertDataPath = "headofficeoutlookplan/insertdata/headoffice_outlookplan_export_test_data_3.xlsx";
        String expectDataPath = "headofficeoutlookplan/expectdata/本社_移管前後_エクスポート_正常系.xlsx";
        List<String> areas=List.of("9901","SKSA");
        List<String> dataKubunList= List.of("0","1");//移管前、移管後
        DateUtil.setTestFixedDateTime("2098");
        DateUtil.setTestSystemDateTime(LocalDateTime.parse("2028-12-26T10:30:00"));
        testDataManager = new TestDataManager(insertDataPath);
        insertedDataTracker = testDataManager.insertAllTestData();

        // 実行と実行前後の履歴確認
        doExecute(areas,dataKubunList,BusinessConstants.BATCH_STATUS_COMPLETED_CODE);

        // CSV内容詳細検証を追加
        verifyCsvFileContent(List.of("9901","9902"),expectDataPath,dataKubunList);

        logger.info("=== 本社_移管前後_エクスポート_正常系テスト完了 ===");
    }

    /**
     * 試験対象：見通し・計画_本社_移管前と移管後_エクスポート
     * 試験条件：エリア:「SKSA」と単独エリアコードを選択される(複数エリア検証)
     *         本社＿見通し・計画_採算管理単位C別:計画項目、実績項目に金額が各項目いずれかが1未満で設定
     *         本採算管理単位C別_直接_実績:実績項目に金額が各項目いずれかが1未満で設定
     *         次年度計画マスタ:下記配置で設定
     *            エリア１:No1-No120
     *            エリア２:No121-No240
     *            カテゴリー１:No1-No50
     *            カテゴリー２:No51-No100
     *            カテゴリー３:No101-No150
     *            カテゴリー４:No151-No200
     *            カテゴリー５:No201-No240
     * 確認内容：エクスポートは0件があり(エラーファイルで出力)、ファイル名が日付以外は想定と一致
     *         計画項目または、実績項目に金額が各項目いずれかが1未満:出力対象外
     *         ダウンロードステータスが異常になる
     */
    @Test
    @Order(4)
    @DisplayName("本社_移管前後_エクスポート_データ0件_異常系検証")
    void testExecuteExportTask_本社_移管前後_エクスポート_データ0件_異常系() {
        logger.info("=== 本社_移管前後_エクスポート_データ0件_異常系テスト開始 ===");

        String insertDataPath = "headofficeoutlookplan/insertdata/headoffice_outlookplan_export_test_data_4.xlsx";
        List<String> areas=List.of("9901","SKSA");
        List<String> dataKubunList= List.of("0","1");//移管前、移管後
        DateUtil.setTestFixedDateTime("2098");
        DateUtil.setTestSystemDateTime(LocalDateTime.parse("2028-12-26T10:30:00"));
        testDataManager = new TestDataManager(insertDataPath);
        insertedDataTracker = testDataManager.insertAllTestData();

        // 実行と実行前後の履歴確認
        doExecute(areas,dataKubunList,BusinessConstants.BATCH_STATUS_FAILED_CODE);

        // CSV内容詳細検証を追加
        // 実のファイル名と内容を取得
        Map<String, String> resultMap = CsvContentComparator.getOutputCsvData(mockS3Service, "事業計画");
        assertThat(resultMap).hasSize(8);

        // 期待ファイル名
        List<String> expectFiles = List.of(
                "error_移管前_見通し計画SK別_9901_name",
                "error_移管前_見通し計画企業別_9901_name",
                "error_移管後_見通し計画SK別_9901_name",
                "error_移管後_見通し計画企業別_9901_name",
                "error_移管前_見通し計画SK別_9902_name",
                "error_移管前_見通し計画企業別_9902_name",
                "error_移管後_見通し計画SK別_9902_name",
                "error_移管後_見通し計画企業別_9902_name"
                );

        // ファイル名(日付除く)が一致
        boolean allPrefixesExist = expectFiles.stream()
                .allMatch(prefix ->
                        resultMap.keySet().stream()
                                .anyMatch(key -> key.startsWith(prefix + "_"))
                );
        assertTrue(allPrefixesExist);

        // ファイル内容が一致
        resultMap.forEach((key,value)->{
            assertTrue(value.contains("条件に一致するデータが取得できませんでした。"));
        });

        logger.info("=== 本社_移管前後_エクスポート_データ0件_異常系テスト完了 ===");
    }

    /**
     * 試験対象：見通し・計画_本社_移管前_エクスポート
     * 試験条件：エリア:「SKSA」を選択される(複数エリア検証)
     *         次年度計画マスタ:エリアが下記配置で設定
     * 　　　　     ・エリア１:実績と計画があり
     * 　　　　     ・エリア２:エリア情報なし
     * 　　　　     ・エリア３:エリア名短縮漢字空白設定
     * 　　　　     ・エリア４:実績と計画がなし
     * 確認内容：下記のように、ファイル名が日付以外は想定と一致
     * 　　　　     ・エリア１:正常ファイル出力
     * 　　　　     ・エリア２:エラーログ出力、ファイルなし
     * 　　　　     ・エリア３:エラーログ出力、ファイルなし
     * 　　　　     ・エリア４:異常ファイル出力
     *          ダウンロードステータスが一部異常になる
     */
    @Test
    @Order(5)
    @DisplayName("本社_移管前_エクスポート_エリア名短縮漢字無_一部異常系検証")
    void testExecuteExportTask_本社_移管前_エクスポート_エリア名短縮漢字無_一部異常系() {
        logger.info("=== 本社_移管前_エクスポート_エリア名短縮漢字無_一部異常系テスト開始 ===");

        String insertDataPath = "headofficeoutlookplan/insertdata/headoffice_outlookplan_export_test_data_5.xlsx";
        List<String> areas=List.of("SKSA");
        List<String> dataKubunList= List.of("0");//移管前
        DateUtil.setTestFixedDateTime("2099");
        DateUtil.setTestSystemDateTime(LocalDateTime.parse("2028-12-26T10:30:00"));
        testDataManager = new TestDataManager(insertDataPath);
        insertedDataTracker = testDataManager.insertAllTestData();

        // 実行と実行前後の履歴確認
        doExecute(areas,dataKubunList,BusinessConstants.BATCH_STATUS_PARTIALLY_FAILED_CODE);

        // CSV内容詳細検証を追加
        // 実のファイル名と内容を取得
        Map<String, String> resultMap = CsvContentComparator.getOutputCsvData(mockS3Service, "事業計画");
        assertThat(resultMap).hasSize(4);

        // 期待ファイル名
        List<String> expectFiles = List.of(
                "移管前_見通し計画SK別_9901_name",
                "移管前_見通し計画企業別_9901_name",
                "error_移管前_見通し計画SK別_9907_name",
                "error_移管前_見通し計画企業別_9907_name"
        );

        // ファイル名(日付除く)が一致
        boolean allPrefixesExist = expectFiles.stream()
                .allMatch(prefix ->
                        resultMap.keySet().stream()
                                .anyMatch(key -> key.startsWith(prefix + "_"))
                );
        assertTrue(allPrefixesExist);

        logger.info("=== 本社_移管前_エクスポート_エリア名短縮漢字無_一部異常系テスト完了 ===");
    }

    /**
     * 試験対象：見通し・計画_本社_移管後_エクスポート
     * 試験条件：エリア:「SKSA」を選択される(複数エリア検証)
     *         採算管理単位計画策定エリアマスタ:年度に対するデータがなし
     * 確認内容：ダウンロードステータスがシステム異常になる
     */
    @Test
    @Order(6)
    @DisplayName("本社_移管後_エクスポート_年度エリアデータ無_システム異常系検証")
    void testExecuteExportTask_本社_移管後_エクスポート_年度エリアデータ無_システム異常系() {
        logger.info("=== 本社_移管後_エクスポート_年度エリアデータ無_システム異常系テスト開始 ===");

        List<String> areas=List.of("SKSA");
        List<String> dataKubunList= List.of("1");//移管後
        DateUtil.setTestFixedDateTime("9999");
        DateUtil.setTestSystemDateTime(LocalDateTime.parse("2028-12-26T10:30:00"));

        // 実行と実行前後の履歴確認
        doExecute(areas,dataKubunList,BusinessConstants.BATCH_STATUS_SYSTEM_ERROR_CODE);

        logger.info("=== 本社_移管後_エクスポート_年度エリアデータ無_システム異常系テスト完了 ===");
    }

    // ==================== プライベートメソッド ====================
    /**
     * 実行と実行前後の履歴確認
     *
     * @param areas エリアコードリスト
     * @param dataKubunList データ区分リスト
     * @param status 履歴ステータス
     */
    private void doExecute( List<String> areas, List<String> dataKubunList,String status) {
        // 実施前ダウンロード履歴jobを手動追加と内容検証
        var initialStatus = addAndCheckAndGetExportJobInfo(areas,dataKubunList);

        // テスト用WorkerPayloadを作成
        WorkerPayload payload = createHeadOfficeOutlookPlanExportPayload(initialStatus.getLeft().toString(), areas,dataKubunList);

        // executeExportTask を実行
        assertDoesNotThrow(() -> {
            dataApplicationService.executeExportTask(payload, mockLambdaContext);
        });

        // 実施後ダウンロード履歴jobの状態を検討
        checkExportJobInfo(initialStatus.getLeft(),initialStatus.getRight(),status);
    }

    /**
     * Mock オブジェクトの基本動作を設定
     */
    private void setupMockBehaviors() {
        try {
            // AsyncLambdaInvoker の Mock 設定
            doNothing().when(mockAsyncLambdaInvoker).invokeAsync(any(WorkerPayload.class));

            // S3Service の Mock 設定
            when(mockS3Service.uploadFileFromStream(any(), anyString(), anyString(), anyLong(), any()))
                    .thenReturn(Map.of("success", true, "key", "out/mtshKkkHnsh/事業計画_20241201120000.zip"));

            when(mockS3Service.getSignedDownloadUrl(anyString(), anyLong()))
                    .thenReturn("https://test-s3-url.com/download");

            when(mockS3Service.getObjectMetadata(anyString()))
                    .thenReturn(Map.of("fileSize", 1024L));

            // Lambda Context の Mock 設定
            when(mockLambdaContext.getRemainingTimeInMillis()).thenReturn(900000); // 15分（BackgroundTimeoutMonitorの緩衝時間10分より大きく設定）
            when(mockLambdaContext.getAwsRequestId()).thenReturn("test-request-id");

            logger.debug("Mock オブジェクトの基本動作設定完了");
        } catch (Exception e) {
            logger.error("Mock 設定エラー: {}", e.getMessage(), e);
            throw new RuntimeException("Mock 設定に失敗しました", e);
        }
    }

    /**
     * CSV文件内容詳細検証
     * DateUtil mockにより正常データが生成されるため、詳細なデータ内容比較を実行
     * 検証項目：
     * 1. S3Service mock呼び出し確認
     * 2. ZIP文件からCSV文件抽出
     * 3. CSV header行の名称と順序検証
     * 4. CSV データ行の詳細内容比較（期待値との完全一致）
     * 5. 重要フィールドの選択的比較検証
     *
     * @param expectedAreaCodes 期待されるエリアコードリスト
     * @param expectDataPath 期待されるファイルパース
     * @param dataKubunList データ区分リスト
     */
    private void verifyCsvFileContent(List<String> expectedAreaCodes, String expectDataPath, List<String> dataKubunList) {
        logger.debug("=== CSV文件内容詳細検証開始（データ内容比較版） ===");
        List<CsvContentComparator.CsvValidationConfig> fileConfigs = new ArrayList<>();
        try {
            for (String areaCode : expectedAreaCodes) {
                if (dataKubunList.contains("0")) {
                    String type = "移管前";
                    fileConfigs.add(getSKConfig(type, expectDataPath, areaCode));  // 本社_移管前_SK別
                    fileConfigs.add(getCompanyConfig(type, expectDataPath, areaCode));   // 本社_移管前_企業別
                }

                if (dataKubunList.contains("1")) {
                    String type = "移管後";
                    fileConfigs.add(getSKConfig(type, expectDataPath, areaCode)); // 本社_移管後_SK別
                    fileConfigs.add(getCompanyConfig(type, expectDataPath, areaCode)); // 本社_移管後_企業別
                }
            }

            // 複数ファイル検証設定を作成
            CsvContentComparator.MultiFileValidationConfig multiConfig = new CsvContentComparator.MultiFileValidationConfig("事業計画", fileConfigs);

            // 複数ファイル一括検証を実行
            CsvContentComparator.validateMultipleCsvFiles(mockS3Service, multiConfig);
            logger.debug("=== CSV文件内容詳細検証完了：全ての検証項目が正常 ===");

        } catch (Exception e) {
            logger.error("CSV文件内容検証エラー: {}", e.getMessage(), e);
            throw new RuntimeException("CSV内容検証に失敗しました", e);
        }
    }

    /**
     * 見通し・計画_本社_エクスポート用WorkerPayloadを作成
     *
     * @param jobId ジョブID
     * @param areaList エリアコードリスト
     * @param dataKubunList データ区分リスト
     */
    private WorkerPayload createHeadOfficeOutlookPlanExportPayload(String jobId, List<String> areaList, List<String> dataKubunList) {
        ExportRequest exportRequest = createTestExportRequest(areaList,dataKubunList);
        UserInfo userInfo = createTestUserInfo("TEST01", "100001");

        return WorkerPayload.builder()
                .jobId(jobId)
                .operationType(BusinessConstants.OPERATION_DOWNLOAD_CODE)
                .request(exportRequest)
                .userInfo(userInfo)
                .build();
    }

    /**
     * テスト用エクスポートリクエストを作成
     *
     * @param areaList エリアコードリスト
     * @param dataKubunList データ区分リスト
     */
    private ExportRequest createTestExportRequest(List<String> areaList, List<String> dataKubunList) {
        ExportRequest exportRequest = new ExportRequest();
        exportRequest.setDataType(BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE);
        exportRequest.setArea(areaList);
        exportRequest.setHnshBashoKubun("0"); // 本社
        exportRequest.setDataKubun(dataKubunList); // 移管前
        exportRequest.setCtgryKubun("1"); // 加食
        return exportRequest;
    }

    /**
     * テスト用ユーザー情報を作成
     *
     * @param shainCode 社員コード
     * @param systemOperationCompanyCode システム運用企業コード
     */
    private UserInfo createTestUserInfo(String shainCode, String systemOperationCompanyCode) {
        UserInfo userInfo = new UserInfo();
        userInfo.setShainCode(shainCode);
        userInfo.setSystemOperationCompanyCode(systemOperationCompanyCode);
        return userInfo;
    }

    /**
     * データベースからExportJobStatusを取得
     * 状態表データの検証用
     *
     * @param rrkBango 履歴番号
     */
    private ExportJobStatus getExportJobStatusFromDatabase(Long rrkBango) {
        try {
            // 真実のExportJobStatusServiceインスタンスを使用してデータベースから取得
            ExportJobStatusService exportJobStatusService = new ExportJobStatusService();
            return exportJobStatusService.getJobStatus(rrkBango);
        } catch (Exception e) {
            logger.error("ExportJobStatus取得エラー: rrkBango={}, error={}", rrkBango, e.getMessage(), e);
            return null;
        }
    }

    /**
     * ExportJobStatusを追加と内容検証と情報を取得
     * 状態表データの検証用
     *
     * @param areaList エリアコードリスト
     * @param dataKubunList データ区分リスト
     */
    private Pair<Long,Integer> addAndCheckAndGetExportJobInfo(List<String> areaList, List<String> dataKubunList) {
        // 事前にjobを手動追加する
        var exportJobResponse = dataApplicationService.startExport(createTestExportRequest(areaList,dataKubunList),
                createTestUserInfo("TEST01", "100001"), mockLambdaContext);
        Long rrkBango = Long.parseLong(exportJobResponse.getJobId());
        // ジョブが正常に作成されたことを確認
        assertNotNull(exportJobResponse.getJobId());
        assertEquals("ACCEPTED", exportJobResponse.getStatus());

        // 初期状態の確認
        ExportJobStatus initialStatus = getExportJobStatusFromDatabase(rrkBango);
        assertNotNull(initialStatus);
        assertEquals(BusinessConstants.BATCH_STATUS_PROCESSING_CODE, initialStatus.getStts());

        return  Pair.of(rrkBango,initialStatus.getVrsn());
    }

    /**
     * 指定番号でステータスを比較する
     * 状態表データの検証用
     *
     * @param rrkBango 履歴番号
     * @param oldVrsn 実施前バージョン
     * @param status ステータス
     */
    private void checkExportJobInfo(Long rrkBango,Integer oldVrsn,String status) {
        ExportJobStatus finalStatus = getExportJobStatusFromDatabase(rrkBango);
        assertNotNull(finalStatus);
        assertEquals(status, finalStatus.getStts());

        // バージョンが更新されていることを確認
        assertTrue(finalStatus.getVrsn() > oldVrsn);
        TestDataManager.deleteTableData("T_DWNLD_RRK", List.of(Map.of( "RRK_BANGO", rrkBango)));
    }

    /**
     * 見通し計画SK別_config
     *
     * @param dataKubun データ区分
     * @param path ファイルパース
     * @param expectedAreaCode 期待されるエリアコード
     */
    private CsvContentComparator.CsvValidationConfig getSKConfig( String dataKubun,String path,String expectedAreaCode) throws IOException {
        // 想定結果取得
        List<List<String>> expectData = CsvContentComparator.readExcelFileWithPadding(path, String.format("%s_見通し計画SK別_%s_name",dataKubun,expectedAreaCode));
        List<List<String>> expectDataRowsOnly = expectData.stream().skip(1).collect(Collectors.toList());

        // 実の結果取得
        CsvContentComparator.CsvValidationConfig before_sk_config =
                CsvContentComparator.createSpecificFileConfig(
                        String.format("%s_見通し計画SK別",dataKubun), expectData.getFirst(), "事業計画", String.format("%s_見通し計画SK別_%s_name",dataKubun,expectedAreaCode),
                        null, expectDataRowsOnly, null);
        return before_sk_config;
    }

    /**
     * 見通し計画企業別_config
     *
     * @param dataKubun データ区分
     * @param path ファイルパース
     * @param expectedAreaCode 期待されるエリアコード
     */
    private CsvContentComparator.CsvValidationConfig getCompanyConfig(String dataKubun, String path,String expectedAreaCode) throws IOException {
        // 想定結果取得
        List<List<String>> expectData = CsvContentComparator.readExcelFileWithPadding(path, String.format("%s_見通し計画企業別_%s_name",dataKubun,expectedAreaCode));
        List<List<String>> expectDataRowsOnly = expectData.stream().skip(1).collect(Collectors.toList());

        // 実の結果取得
        CsvContentComparator.CsvValidationConfig before_sk_config =
                CsvContentComparator.createSpecificFileConfig(
                        String.format("%s_見通し計画企業別",dataKubun), expectData.getFirst(), "事業計画", String.format("%s_見通し計画企業別_%s_name",dataKubun,expectedAreaCode),
                        null, expectDataRowsOnly, null);
        return before_sk_config;
    }
}
