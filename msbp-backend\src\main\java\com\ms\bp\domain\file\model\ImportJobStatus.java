package com.ms.bp.domain.file.model;

import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.util.DateUtil;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * インポートジョブステータスエンティティクラス
 * ファイル処理領域のインポート作業状態管理モデル
 * PostgreSQLデータベースのT_IMPRT_RRK（インポート履歴テーブル）に対応
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ImportJobStatus {

    // 主キー
    /** 履歴番号 */
    private Long rrkBango;

    // 基本情報
    /** システム運用企業コード */
    private String systmUnyoKigyoCode;

    /** 社員コード */
    private String shainCode;

    /** ファイル種別 */
    private String fileShbts;

    /** エリア */
    private String area;

    /** エリア名（連表查询で取得、SQL内でCASE WHEN変換済み） */
    private String areaName;

    /** ファイル種別名（SQL内でCASE WHEN変換済み） */
    private String fileShbtsName;

    /** ファイル名 */
    private String fileMei;

    /** エラーファイル名 */
    private String errorFileMei;

    // 処理日時
    /** アップロード開始日時 */
    private String uploadKshNchj;

    /** アップロード完了日時 */
    private String uploadKnrNchj;

    // ステータス・結果
    /** ステータス (0:処理中、1:完了、2:一部失敗、3:失敗、4:システムエラー) */
    private String stts;

    /** ステータス名（SQL内でCASE WHEN変換済み） */
    private String sttsName;

    // 監査フィールド
    /** 登録プログラムＩＤ */
    private String trkPrgrmId;

    /** 登録システム運用企業コード */
    private String trkSystmUnyoKigyoCode;

    /** 登録社員コード */
    private String trkShainCode;

    /** 更新プログラムＩＤ */
    private String kshnPrgrmId;

    /** 更新システム運用企業コード */
    private String kshnSystmUnyoKigyoCode;

    /** 更新社員コード */
    private String kshnShainCode;

    /** バージョン */
    private Integer vrsn;

    /** レコード登録日時 */
    private String rcrdTrkNchj;

    /** レコード更新日時 */
    private String rcrdKshnNchj;

    /**
     * 初期化処理 - 監査フィールドを自動設定
     * @param functionId 機能ID（nullの場合はデフォルト値を使用）
     * @return 初期化済みのインスタンス
     */
    public ImportJobStatus init(String functionId) {
        String currentDateTime = DateUtil.getCurrentDateTimeString();
        String programId = (functionId != null && !functionId.trim().isEmpty())
                ? functionId : "SYSTEM";

        // アップロード開始日時
        this.uploadKshNchj = currentDateTime;

        // プログラムID
        this.trkPrgrmId = programId;
        this.kshnPrgrmId = programId;

        // 監査用企業コード・社員コード
        this.trkSystmUnyoKigyoCode = this.systmUnyoKigyoCode;
        this.trkShainCode = this.shainCode;
        this.kshnSystmUnyoKigyoCode = this.systmUnyoKigyoCode;
        this.kshnShainCode = this.shainCode;

        // タイムスタンプ
        this.rcrdTrkNchj = currentDateTime;
        this.rcrdKshnNchj = currentDateTime;
        this.stts = BusinessConstants.BATCH_STATUS_PROCESSING_CODE; // 処理中
        this.vrsn = 1;
        return this;
    }

    /**
     * 処理完了時のステータス更新（ステータス=1）
     * 要件：アップロード完了日時=システム日時、エラーファイル名=null
     */
    public void updateCompletionStatus() {
        String currentDateTime = DateUtil.getCurrentDateTimeString();

        this.stts = BusinessConstants.BATCH_STATUS_COMPLETED_CODE; // 完了
        this.uploadKnrNchj = currentDateTime; // アップロード完了日時をシステム日時に設定
        this.errorFileMei = null; // エラーファイル名をnullに設定
        this.rcrdKshnNchj = currentDateTime; // レコード更新日時をシステム日時に設定
        this.vrsn = this.vrsn + 1;
    }

    /**
     * 処理失敗時のステータス更新（ステータス=3）
     * 要件：アップロード完了日時=null、エラーファイル名=エラーファイル名、レコード更新日時=システム日時
     * @param errorFileName エラーファイル名
     */
    public void updateFailureStatus(String errorFileName) {
        String currentDateTime = DateUtil.getCurrentDateTimeString();

        this.stts = BusinessConstants.BATCH_STATUS_FAILED_CODE; // 失敗
        this.uploadKnrNchj = null; // アップロード完了日時をnullに設定
        this.errorFileMei = errorFileName; // エラーファイル名を設定
        this.rcrdKshnNchj = currentDateTime; // レコード更新日時をシステム日時に設定
        this.vrsn = this.vrsn + 1;
    }

    /**
     * システムエラー時のステータス更新（ステータス=4）
     * 要件：アップロード完了日時=null、エラーファイル名=条件付き設定、レコード更新日時=システム日時
     * @param errorFileName エラーファイル名（エラーファイルが作成できた場合のみ設定、作成できなかった場合はnull）
     */
    public void updateSystemErrorStatus(String errorFileName) {
        String currentDateTime = DateUtil.getCurrentDateTimeString();

        this.stts = BusinessConstants.BATCH_STATUS_SYSTEM_ERROR_CODE; // システムエラー
        this.uploadKnrNchj = null; // アップロード完了日時をnullに設定
        this.errorFileMei = errorFileName; // エラーファイル名を条件付きで設定（nullの場合もあり）
        this.rcrdKshnNchj = currentDateTime; // レコード更新日時をシステム日時に設定
        this.vrsn = this.vrsn + 1;
    }

}
