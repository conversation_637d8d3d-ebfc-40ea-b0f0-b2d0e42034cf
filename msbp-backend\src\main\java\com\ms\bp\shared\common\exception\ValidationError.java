package com.ms.bp.shared.common.exception;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * フィールド単位の検証エラーを表すモデル
 * GlobalCodeConstants統合対応版
 */
@Data
@Builder
@NoArgsConstructor
public class ValidationError {
    /**
     * エラーが発生したJavaフィールド名
     */
    private String fieldName;

    /**
     * 表示用フィールド名（日本語）
     * カスタムフィールド名が指定された場合に使用
     */
    private String displayName;

    /**
     * エラーが発生したフィールドの値
     */
    private Object fieldValue;

    /**
     * エラーメッセージ
     */
    private String message;

    /**
     * エラーコード（GlobalCodeConstantsで定義）
     */
    private String errorCode;

    /**
     * 便利なコンストラクタ（エラーコードなし、後方互換性）
     */
    public ValidationError(String fieldName, Object fieldValue, String message) {
        this.fieldName = fieldName;
        this.fieldValue = fieldValue;
        this.message = message;
    }

    /**
     * 便利なコンストラクタ（エラーコード付き）
     */
    public ValidationError(String fieldName, Object fieldValue, String message, String errorCode) {
        this.fieldName = fieldName;
        this.fieldValue = fieldValue;
        this.message = message;
        this.errorCode = errorCode;
    }

    /**
     * 完全なコンストラクタ（表示名付き）
     */
    public ValidationError(String fieldName, String displayName, Object fieldValue, String message, String errorCode) {
        this.fieldName = fieldName;
        this.displayName = displayName;
        this.fieldValue = fieldValue;
        this.message = message;
        this.errorCode = errorCode;
    }

    /**
     * 表示用フィールド名を取得
     * displayNameが設定されている場合はそれを、そうでなければfieldNameを返す
     *
     * @return 表示用フィールド名
     */
    public String getEffectiveDisplayName() {
        return (displayName != null && !displayName.trim().isEmpty()) ? displayName : fieldName;
    }
}