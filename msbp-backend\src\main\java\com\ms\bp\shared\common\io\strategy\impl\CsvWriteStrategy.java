package com.ms.bp.shared.common.io.strategy.impl;

import com.ms.bp.shared.common.io.options.ExportOptions;
import com.ms.bp.shared.common.io.strategy.DataWriteStrategy;
import com.opencsv.CSVWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * CSV書き込み戦略実装
 */
public class CsvWriteStrategy<T> implements DataWriteStrategy<T> {
    private static final Logger logger = LoggerFactory.getLogger(CsvWriteStrategy.class);
    private CSVWriter csvWriter;

    @Override
    public void writeHeader(List<String> columns, OutputStream outputStream, ExportOptions options) throws IOException {
        if (csvWriter == null) {
            csvWriter = new CSVWriter(new OutputStreamWriter(outputStream, StandardCharsets.UTF_8),
                    options.getDelimiter().charAt(0),
                    CSVWriter.DEFAULT_QUOTE_CHARACTER,
                    CSVWriter.DEFAULT_ESCAPE_CHARACTER,
                    CSVWriter.DEFAULT_LINE_END);
        }

        if (columns != null && !columns.isEmpty()) {
            // フィールドマッピングが有効な場合、ヘッダーを変換
            String[] headerArray;
            if (options.isEnableFieldMapping() && options.getFieldHeaderMapping() != null) {
                headerArray = new String[columns.size()];
                for (int i = 0; i < columns.size(); i++) {
                    String fieldName = columns.get(i);
                    String mappedHeader = options.getFieldHeaderMapping().get(fieldName);
                    headerArray[i] = mappedHeader != null ? mappedHeader : fieldName;
                }
            } else {
                headerArray = columns.toArray(new String[0]);
            }

            csvWriter.writeNext(headerArray);
        }
    }

    @Override
    public void writeRecord(Map<String, Object> data, List<String> columns, OutputStream outputStream, ExportOptions options) throws IOException {
        if (csvWriter == null) {
            csvWriter = new CSVWriter(new OutputStreamWriter(outputStream, StandardCharsets.UTF_8),
                    options.getDelimiter().charAt(0),
                    CSVWriter.DEFAULT_QUOTE_CHARACTER,
                    CSVWriter.DEFAULT_ESCAPE_CHARACTER,
                    CSVWriter.DEFAULT_LINE_END);
        }

        String[] row = new String[columns.size()];
        for (int i = 0; i < columns.size(); i++) {
            Object value = data.get(columns.get(i));
            row[i] = value != null ? value.toString() : "";
        }

        csvWriter.writeNext(row);
    }

    @Override
    public void writeFooter(OutputStream outputStream, ExportOptions options) throws IOException {
        // CSVには特定のフッターはありません
    }

    @Override
    public void finish(OutputStream outputStream, ExportOptions options) throws IOException {
        if (csvWriter != null) {
            csvWriter.flush();
            csvWriter.close();
            csvWriter = null;
        }
    }
}