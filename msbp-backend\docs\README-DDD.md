# Business Plan Lambda Application - DDD アーキテクチャ設計書

## 概要

Business Plan Lambda Application のドメイン駆動設計（DDD）アーキテクチャについて詳細に説明します。

## DDD重構築の主要成果

### アーキテクチャ改善点
- **完全な層分離**: インターフェース、アプリケーション、ドメイン、インフラストラクチャ層の明確な分離
- **単一責任原則**: 各クラスが単一の責任を持つよう重構築
- **戦略パターン実装**: ファイル分割戦略の柔軟な実装（5種類の戦略）
- **非同期処理最適化**: 完全バックグラウンド処理とジョブ管理システム
- **パフォーマンス最適化**: DatabaseMappable による反射回避、SQL最適化
- **セキュリティ統合**: 権限チェックの統合とSSH隧道接続対応

### 技術的改善
- **Repository パターン**: repository.impl パッケージでの実装統一
- **DTO最適化**: ビジネスメソッド付きDTOとDatabaseMappable実装
- **エラーハンドリング**: 統一されたエラー処理とエラーファイル生成
- **設定管理**: Parameter Store統合と環境別設定管理


## DDD層構造

```
src/main/java/com/ms/bp/
├── interfaces/                    # インターフェース層（プレゼンテーション層）
│   ├── LambdaHandler.java        # AWS Lambda エントリーポイント
│   ├── dto/                      # データ転送オブジェクト
│   │   ├── request/              # リクエストDTO
│   │   │   ├── ExportRequest.java        # エクスポートリクエスト
│   │   │   ├── ImportRequest.java        # インポートリクエスト
│   │   │   └── PermissionCheckRequest.java # 権限チェックリクエスト
│   │   └── response/             # レスポンスDTO
│   │       ├── ExportJobResponse.java    # エクスポートジョブレスポンス
│   │       ├── ImportJobResponse.java    # インポートジョブレスポンス
│   │       └── PermissionCheckResponse.java # 権限チェックレスポンス
│   └── rest/                     # REST API コントローラー
│       ├── controller/           # コントローラークラス
│       │   ├── DataController.java       # データ管理API
│       │   ├── FileController.java       # ファイル管理API
│       │   ├── UserAuthController.java   # 認証API
│       │   └── PermissionController.java # 権限管理API
│       └── exception/            # 例外ハンドラー
│           └── GlobalExceptionHandler.java # グローバル例外処理
├── application/                  # アプリケーション層
│   ├── data/                     # データ処理アプリケーション
│   │   ├── DataApplicationService.java   # ビジネス協調（295行）
│   │   ├── TaskOrchestrationService.java # タスク協調（183行）
│   │   ├── FileExportOrchestrator.java   # エクスポート協調（245行）
│   │   ├── FileImportOrchestrator.java   # インポート協調（135行）
│   │   └── strategy/             # ファイル分割戦略
│   │       ├── FileSplitStrategy.java    # 戦略インターフェース
│   │       ├── FileSplitStrategyManager.java # 戦略管理器
│   │       └── impl/             # 戦略実装
│   │           ├── AreaBasedSplitStrategy.java      # エリア別分割
│   │           ├── CountBasedSplitStrategy.java     # 件数別分割
│   │           ├── DepartmentBasedSplitStrategy.java # 部門別分割
│   │           ├── ComplexConditionSplitStrategy.java # 複合条件分割
│   │           └── DefaultSingleFileStrategy.java   # 単一ファイル（デフォルト）
│   ├── ExportJobStatusService.java      # エクスポートジョブ管理
│   ├── ImportJobStatusService.java      # インポートジョブ管理
│   ├── UserAuthApplicationService.java  # ユーザー認証アプリケーション
│   ├── PermissionApplicationService.java # 権限管理アプリケーション
│   ├── factory/                  # ドメインサービスファクトリー
│   │   └── DomainServiceFactory.java    # サービス生成管理
│   └── dto/                      # アプリケーション層DTO
│       ├── ExportJobResult.java          # エクスポートジョブ結果
│       └── ImportJobResult.java          # インポートジョブ結果
├── domain/                       # ドメイン層
│   ├── file/                     # ファイル処理ドメイン
│   │   ├── base/                 # 抽象サービスクラス
│   │   │   ├── AbstractImportService.java  # インポート基底クラス
│   │   │   ├── AbstractExportService.java  # エクスポート基底クラス
│   │   │   ├── AbstractDataService.java    # データ処理基底クラス
│   │   │   └── DataExpander.java          # データ展開処理
│   │   ├── model/                # ファイル処理ドメインモデル
│   │   │   ├── ImportJobStatus.java       # インポートジョブ状態
│   │   │   ├── ExportJobStatus.java       # エクスポートジョブ状態
│   │   │   ├── UserImportData.java        # ユーザーインポートデータ
│   │   │   └── ProcessingResult.java      # 処理結果
│   │   ├── repository/           # リポジトリインターフェース
│   │   │   ├── ImportJobStatusRepository.java  # インポートジョブリポジトリ
│   │   │   └── ExportJobStatusRepository.java  # エクスポートジョブリポジトリ
│   │   └── user/                 # ユーザーファイル処理実装
│   │       ├── UserImportService.java     # ユーザーインポートサービス
│   │       └── UserExportService.java     # ユーザーエクスポートサービス
│   ├── user/                     # ユーザー認証ドメイン
│   └── permission/               # 権限管理ドメイン
│       ├── PermissionService.java         # 権限サービス
│       ├── model/                # 権限ドメインモデル
│       │   ├── Permission.java            # 権限マスタ
│       │   ├── PermissionRule.java        # 権限ルール
│       │   └── PermissionCheckResult.java # 権限チェック結果
│       └── repository/           # 権限リポジトリインターフェース
│           └── PermissionRepository.java  # 権限リポジトリ
├── infrastructure/              # インフラストラクチャ層
│   ├── file/                    # ファイル処理基盤
│   │   ├── FileProcessingService.java    # ファイル処理
│   ├── external/                # 外部サービス連携
│   │   ├── email/               # メールサービス
│   │   │   └── EmailService.java         # SESメール送信
│   │   └── s3/                  # S3サービス
│   │       └── S3Service.java            # S3ファイル操作
│   ├── async/                   # 非同期処理基盤
│   │   └── AsyncTaskExecutor.java        # 非同期実行
│   └── repository/              # リポジトリ実装
│       └── impl/                # データアクセス実装
│           ├── UserRepositoryDataAccess.java      # ユーザーデータアクセス
│           ├── UserTokenRepositoryDataAccess.java # トークンデータアクセス
│           ├── PermissionRepositoryDataAccess.java # 権限データアクセス
│           ├── ImportJobStatusDataAccess.java     # インポートジョブデータアクセス
│           └── ExportJobStatusDataAccess.java     # エクスポートジョブデータアクセス
└── shared/                      # 共有ユーティリティ
    ├── common/                  # 共通機能
    │   ├── CommonResult.java             # 共通レスポンス
    │   ├── constants/                    # 定数クラス
    │   ├── config/                       # 設定管理
    │   │   ├── ConfigurationManager.java        # 設定管理器
    │   │   └── ConfigurationInitializer.java    # 設定初期化
    │   ├── db/                           # データベース関連
    │   │   ├── AuroraConnectionManager.java     # Aurora接続管理
    │   │   ├── SSHTunnelManager.java            # SSH隧道管理
    │   │   └── LambdaResourceManager.java       # Lambda リソース管理
    │   ├── io/                           # I/O関連
    │   └── exception/                    # 例外クラス
    ├── security/                # 共有セキュリティコンポーネント
    │   ├── ApiAuthMiddleware.java        # API認証ミドルウェア
    │   ├── JwtTokenGenerator.java        # JWTトークン生成
    │   └── PasswordEncoder.java          # パスワード暗号化
    └── util/                    # ユーティリティクラス
        ├── AsyncExecutorUtil.java        # 非同期実行ユーティリティ
        ├── ZipUtil.java                  # ZIPファイルユーティリティ
        └── ResponseUtil.java             # レスポンスユーティリティ
```

## 各層の責務

### 1. インターフェース層（Interfaces Layer）

**責務**: 外部からのリクエストを受け取り、適切なアプリケーションサービスに処理を委譲する

**主要コンポーネント**:
- `LambdaHandler`: AWS Lambda関数のエントリーポイント
- `DataController`: データ管理APIエンドポイント（224行、簡素化済み）
- `FileController`: ファイル管理APIエンドポイント
- `UserAuthController`: 認証APIエンドポイント
- `PermissionController`: 権限管理APIエンドポイント（104行、改善済み）

**改善された特徴**:
- HTTP処理のみに責任を限定
- DTO変換をApplication Service層に移動
- 権限チェックの統合
- エラーハンドリングの統一

**実装例**:
```java
public class DataController {
    private final DataApplicationService dataApplicationService;

    public CommonResult<?> exportData(APIGatewayProxyRequestEvent request,
                                    UserInfo userInfo, Context context) {
        try {
            // リクエスト検証とDTO変換
            ExportRequest exportRequest = objectMapper.readValue(request.getBody(), ExportRequest.class);
            validateExportRequest(exportRequest);

            // アプリケーションサービスへの委譲（権限チェック含む）
            ExportJobResponse response = dataApplicationService.startExport(exportRequest, userInfo, context);

            return CommonResult.success(response);

        } catch (ServiceException e) {
            return CommonResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("エクスポート処理エラー", e);
            return CommonResult.error("INTERNAL_ERROR", "内部エラーが発生しました");
        }
    }
}
```


### 2. アプリケーション層（Application Layer）

**責務**: ビジネスユースケースを調整し、ドメインオブジェクトを組み合わせて処理を実行する

**主要コンポーネント**:
- `DataApplicationService`: ビジネス協調
- `TaskOrchestrationService`: タスク協調
- `FileExportOrchestrator`: エクスポート協調
- `FileImportOrchestrator`: インポート協調
- `PermissionApplicationService`: 権限管理アプリケーション
- `FileSplitStrategyManager`: ファイル分割戦略管理
- `ExportJobStatusService`: エクスポートジョブ管理
- `ImportJobStatusService`: インポートジョブ管理
- `UserAuthApplicationService`: ユーザー認証アプリケーション
- `DomainServiceFactory`: ドメインサービスの生成管理


**DataApplicationService実装例**:
```java
public class DataApplicationService {
    private final TaskOrchestrationService taskOrchestrationService;
    private final PermissionApplicationService permissionApplicationService;
    private final ExportJobStatusService exportJobStatusService;
    private final ImportJobStatusService importJobStatusService;

    public ExportJobResponse startExport(ExportRequest exportRequest, UserInfo userInfo, Context context) {
        try {
            logger.info("エクスポート処理開始: ユーザー={}, データタイプ={}",
                       userInfo.getName(), exportRequest.getDataType());

            // 権限チェック
            validateExportPermission(userInfo, exportRequest.getDataType());

            // ジョブIDを生成
            String jobId = generateJobId(exportRequest.getDataType());

            // ジョブステータスを初期化（データベースに保存）
            exportJobStatusService.createJob(jobId, exportRequest.getDataType(),
                userInfo.getOid(), userInfo.getName(), exportRequest.getFilters(),
                (String) exportRequest.getFilters().get("areas"), null);

            // タスク協調サービスに委譲
            taskOrchestrationService.orchestrateExportTask(jobId, exportRequest, userInfo, context);

            logger.info("エクスポートジョブを受け付けました: jobId={}", jobId);
            return ExportJobResponse.accepted(jobId);

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            logger.error("エクスポート処理開始エラー: {}", e.getMessage(), e);
            throw new ServiceException(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getCode(),
                    "エクスポート処理の開始中にエラーが発生しました");
        }
    }

    private void validateExportPermission(UserInfo userInfo, String dataType) {
        String fileTypeCode = getFileTypeCodeFromDataType(dataType);
        PermissionCheckResult result = permissionApplicationService.checkPermission(
            userInfo, fileTypeCode, "2"); // 2: ダウンロード

        if (!result.isAllowed()) {
            throw new ServiceException("PERMISSION_DENIED",
                "エクスポート権限がありません: " + result.getMessage());
        }
    }
}
```

**TaskOrchestrationService実装例**:
```java
public class TaskOrchestrationService {
    private final FileExportOrchestrator fileExportOrchestrator;
    private final FileImportOrchestrator fileImportOrchestrator;
    private final AsyncTaskExecutor asyncTaskExecutor;

    public void orchestrateExportTask(String jobId, ExportRequest exportRequest,
                                    UserInfo userInfo, Context context) {
        // 非同期でエクスポート処理を実行
        asyncTaskExecutor.executeAsync(() -> {
            try {
                ExportProcessResult result = fileExportOrchestrator.processExport(
                    jobId, exportRequest, userInfo, context);

                // ジョブ結果の作成と保存
                ExportJobResult jobResult = createExportJobResult(result);
                updateExportJobStatus(jobId, jobResult);

            } catch (Exception e) {
                logger.error("エクスポート処理エラー: jobId={}", jobId, e);
                updateExportJobError(jobId, e.getMessage());
            }
        });
    }
}
```

### 3. ドメイン層（Domain Layer）

**責務**: ビジネスロジックとビジネスルールを実装する

#### 3.1 ユーザー認証ドメイン

**ドメインサービス**:
- `UserAuthenticationService`: ユーザーログイン認証
- `UserActivationService`: アカウントアクティベーション
- `PasswordResetService`: パスワードリセット

**ドメインモデル**:
- `AuthUser`: 認証ユーザー（リッチドメインモデル）
- `UserToken`: ユーザートークン（値オブジェクト）


#### 3.2 ファイル処理ドメイン

**抽象サービスクラス**:
- `AbstractImportService`: インポート処理の共通ロジック
- `AbstractExportService`: エクスポート処理の共通ロジック
- `DataExpander`: データ展開処理

**ドメインモデル**:
- `ImportJobStatus`: インポートジョブ状態
- `ExportJobStatus`: エクスポートジョブ状態
- `UserImportData`: ユーザーインポートデータ（リッチドメインモデル）

**実装例**:
```java
public abstract class AbstractImportService<T extends DatabaseMappable> extends AbstractDataService<InputStream, ImportResult> {

    // テンプレートメソッドパターンによる共通処理フロー
    @Override
    protected ImportResult doExecute(InputStream input, Map<String, Object> params) throws Exception {
        // オプション構築
        ImportOptions options = buildImportOptions();

        // データ検証器を取得
        DataValidator validator = getDataValidator();

        // インポート実行（検証器は必須）
        return dataProcessingService.processImport(input, getDTOClass(), options, validator);
    }

    // 抽象メソッド（サブクラスで実装）
    protected abstract Class<T> getDTOClass();
    protected abstract DataValidator getDataValidator();
    protected abstract ImportOptions buildImportOptions();
}
```

#### 3.3 具体的なドメインサービス実装

**ユーザーインポートサービス**:
```java
public class UserImportService extends AbstractImportService<UserImportData> {

    @Override
    protected String getDataType() {
        return "USERS";
    }

    @Override
    protected DataValidator getDataValidator() {
        // 領域モデルベースのバリデーターを使用（アノテーション検証 + カスタム検証）
        return new DTODataValidator<>(UserImportData.class, this::validateCustomLogic);
    }

    @Override
    protected Class<UserImportData> getDTOClass() {
        return UserImportData.class;
    }

    @Override
    protected ImportOptions buildImportOptions() {
        return ImportOptions.builder()
                .format(FileFormat.CSV)
                .hasHeader(true)
                .batchSize(100)
                .targetTable("users")
                .keyColumns(Arrays.asList("id"))
                .upsertMode(true)
                .skipValidation(false)
                .continueOnError(true)
                .build();
    }

    // カスタムビジネスロジック検証
    private List<ValidationError> validateCustomLogic(UserImportData dto, int rowIndex) {
        List<ValidationError> errors = new ArrayList<>();

        // 部門IDの存在チェック
        if (dto.requiresDepartmentValidation()) {
            if (!departmentExists(dto.getDepartmentId())) {
                errors.add(new ValidationError("departmentId",
                    "指定された部門ID " + dto.getDepartmentId() + " は存在しません", rowIndex));
            }
        }

        return errors;
    }
}
```

**ユーザーエクスポートサービス**:
```java
public class UserExportService extends AbstractExportService<Map<String, Object>> {

    @Override
    protected String getDataType() {
        return "USERS";
    }

    @Override
    protected DataExpander getDataExpander() {
        // ユーザーロール展開処理器を返す
        return new UserRoleExpander();
    }

    @Override
    protected Map<String, Object> formatData(Map<String, Object> rawData) {
        Map<String, Object> formattedData = new HashMap<>(rawData);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 日付フィールドの格式化
        formatDateField(formattedData, "created_at", dateFormat);
        formatDateField(formattedData, "updated_at", dateFormat);

        return formattedData;
    }

    @Override
    protected ExportOptions buildExportOptionsWithStrategy(String sqlStrategyKey, String customFileName) {
        return ExportOptions.builder()
                .format(FileFormat.CSV)
                .includeHeader(true)
                .columns(Arrays.asList("id", "username", "email", "full_name",
                                     "department_name", "role_name", "status",
                                     "created_at", "updated_at"))
                .batchSize(1000)
                .countTotal(true)
                .build();
    }
}
```

### 4. インフラストラクチャ層（Infrastructure Layer）

**責務**: 外部システムとの連携とデータ永続化を担当する

#### 4.1 リポジトリ実装

**実装クラス**:
- `UserRepositoryImpl`: ユーザーリポジトリの実装
- `UserTokenRepositoryImpl`: ユーザートークンリポジトリの実装
- `ImportJobStatusRepositoryImpl`: インポートジョブリポジトリの実装
- `ExportJobStatusRepositoryImpl`: エクスポートジョブリポジトリの実装

**実装例**:
```java
public class UserRepositoryImpl implements UserRepository {
    private final UserRepositoryDataAccess dataAccess;

    public UserRepositoryImpl(UserRepositoryDataAccess dataAccess) {
        this.dataAccess = dataAccess;
    }

    @Override
    public AuthUser findByEmployeeCode(EmployeeCode employeeCode) {
        try {
            return dataAccess.findByEmployeeCode(employeeCode.getValue());
        } catch (SQLException e) {
            throw new RepositoryException("ユーザー検索に失敗しました: " + employeeCode.getValue(), e);
        }
    }

    @Override
    public void save(AuthUser user) {
        try {
            if (user.getId() == null) {
                dataAccess.insert(user);
            } else {
                dataAccess.update(user);
            }
        } catch (SQLException e) {
            throw new RepositoryException("ユーザー保存に失敗しました", e);
        }
    }
}
```

**データアクセス実装例**:
```java
public class UserRepositoryDataAccess {
    private final JdbcTemplate jdbcTemplate;

    public AuthUser findByEmployeeCode(String employeeCode) throws SQLException {
        String sql = """
            SELECT id, employee_code, username, email, password_hash,
                   status, created_at, updated_at
            FROM users
            WHERE employee_code = ?
            """;

        return jdbcTemplate.queryForObject(sql, new Object[]{employeeCode}, (rs, rowNum) -> {
            AuthUser user = new AuthUser();
            user.setId(rs.getLong("id"));
            user.setEmployeeCode(new EmployeeCode(rs.getString("employee_code")));
            user.setUsername(rs.getString("username"));
            user.setEmail(rs.getString("email"));
            user.setPasswordHash(rs.getString("password_hash"));
            user.setStatus(rs.getString("status"));
            user.setCreatedAt(rs.getTimestamp("created_at"));
            user.setUpdatedAt(rs.getTimestamp("updated_at"));
            return user;
        });
    }
}
```

#### 4.2 外部サービス連携

**S3サービス**:
```java
public class S3Service {
    private final S3Client s3Client;
    private final String bucketName;

    public ResponseInputStream<GetObjectResponse> getInputStreamFromS3Url(String key) throws IOException {
        try {
            GetObjectRequest request = GetObjectRequest.builder()
                    .bucket(this.bucketName)
                    .key(key)
                    .build();

            return s3Client.getObject(request);
        } catch (Exception e) {
            throw new IOException("S3からのInputStream取得に失敗しました: " + e.getMessage(), e);
        }
    }

    public String uploadFile(InputStream inputStream, String key, String contentType) throws IOException {
        try {
            PutObjectRequest request = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .contentType(contentType)
                    .build();

            s3Client.putObject(request, RequestBody.fromInputStream(inputStream, inputStream.available()));

            return generatePresignedUrl(key);
        } catch (Exception e) {
            throw new IOException("S3へのファイルアップロードに失敗しました: " + e.getMessage(), e);
        }
    }
}
```

## 依存関係の方向

DDDでは、依存関係の方向が重要です：

```
インターフェース層 → アプリケーション層 → ドメイン層 ← インフラストラクチャ層
```

- **ドメイン層**: 他の層に依存しない（純粋なビジネスロジック）
- **アプリケーション層**: ドメイン層とインフラストラクチャ層に依存
- **インフラストラクチャ層**: ドメイン層のインターフェースを実装（依存関係逆転）
- **インターフェース層**: アプリケーション層に依存