# 設定項目詳細

## 概要

Business Plan Lambda Application で使用される設定項目について詳細に説明します。

## 設定管理方式

### 1. AWS Parameter Store（本番・UAT環境）

本番環境とUAT環境では、AWS Systems Manager Parameter Store を使用して設定を管理します。

#### パラメータ命名規則
```
/ms-bp/{環境名}/standard/{カテゴリ}/{設定名}
```

**例：**
- `/ms-bp/prod/standard/db/host`
- `/ms-bp/uat/standard/db/DataBaseName`
- `/ms-bp/dev/standard/db/MasterUsername`

### 2. 設定ファイル（ローカル開発環境）

ローカル開発環境では、`application.properties` ファイルを使用します。

## データベース設定

### Parameter Store設定項目

| パラメータ名 | タイプ | 説明 | 例 |
|-------------|-------|------|-----|
| `/ms-bp/{環境}/standard/db/host` | String | データベースホスト名 | `aurora-cluster.cluster-xxxxx.ap-northeast-1.rds.amazonaws.com` |
| `/ms-bp/{環境}/standard/db/DataBaseName` | String | データベース名 | `business_plan_db` |
| `/ms-bp/{環境}/standard/db/MasterUsername` | String | マスターユーザー名 | `bp_admin` |
| `/ms-bp/{環境}/standard/db/MasterUserPassword` | SecureString | マスターユーザーパスワード | `(暗号化されたパスワード)` |
| `/ms-bp/{環境}/standard/db/port` | String | ポート番号（オプション） | `5432` |

### application.properties設定項目

```properties
# データベース設定制御
db.use.parameter.store=false
db.host=localhost
db.port=5432
db.name=business_plan_db
db.username=bp_user
db.password=bp_password


```

## AWS設定

### Parameter Store設定項目

| パラメータ名 | タイプ | 説明 |
|-------------|-------|------|
| `/ms-bp/{環境}/standard/aws/region` | String | AWSリージョン |
| `/ms-bp/{環境}/standard/aws/s3/bucket` | String | S3バケット名 |
| `/ms-bp/{環境}/standard/aws/ses/from-email` | String | SES送信元メールアドレス |

### application.properties設定項目



## セキュリティ設定

### JWT設定

```properties
# JWT設定
jwt.secret.key=your-secret-key-change-in-production
jwt.access.token.expiration=28800
jwt.refresh.token.expiration=604800
jwt.issuer=ms-bp-system
```


## ログ設定

```properties
# Lambda ログ設定
aws.lambda.log.format=JSON
aws.lambda.log.level=INFO

# アプリケーションログ設定
logging.level.com.ms.bp=INFO
logging.level.com.ms.bp.domain=DEBUG
logging.level.com.ms.bp.application=DEBUG
logging.level.com.ms.bp.infrastructure=INFO

# SQL ログ設定
logging.level.org.springframework.jdbc.core.JdbcTemplate=DEBUG
logging.level.org.springframework.jdbc.core.StatementCreatorUtils=TRACE
```



## メール設定

```properties
# メール基本設定
email.default.from=<EMAIL>
email.default.reply.to=<EMAIL>
email.template.base.path=/templates/email

# アクティベーションメール設定
email.activation.subject=アカウントアクティベーションのお知らせ
email.activation.template=activation.html
email.activation.token.expiration=86400

# パスワードリセットメール設定
email.password.reset.subject=パスワードリセットのお知らせ
email.password.reset.template=password-reset.html
email.password.reset.token.expiration=3600
```


## 開発・デバッグ設定

```properties
# 開発モード設定
app.development.mode=true
app.debug.enabled=true
app.performance.monitoring=true

# テスト設定
test.database.reset=true
test.email.mock=true
test.s3.mock=true

# SSH隧道設定（ローカル開発用）
ssh.tunnel.enabled=false
ssh.tunnel.ssh.host=bastion.example.com
ssh.tunnel.ssh.port=22
ssh.tunnel.ssh.username=ec2-user
ssh.tunnel.ssh.private.key.path=~/.ssh/aws-key.pem
ssh.tunnel.local.port=15432
ssh.tunnel.remote.host=aurora-cluster.cluster-xxxxx.ap-northeast-1.rds.amazonaws.com
ssh.tunnel.remote.port=5432
ssh.tunnel.connection.timeout=30000
ssh.tunnel.keep.alive.interval=60000
```

## 環境別設定例

### 開発環境（dev）
```properties
aws.lambda.log.level=DEBUG
db.use.parameter.store=true
```

### UAT環境（uat）
```properties
aws.lambda.log.level=INFO
db.use.parameter.store=true
```

### 本番環境（prod）
```properties
aws.lambda.log.level=WARN
db.use.parameter.store=true
```

## セキュリティ考慮事項

### 機密情報の管理
- パスワード、APIキー、秘密鍵は必ずParameter Store（SecureString）で管理
- ローカル開発環境でも本番用の機密情報は使用しない
- 設定ファイルにはダミー値やプレースホルダーを使用

### アクセス制御
- Parameter Store へのアクセスはIAMロールで制御
- 環境別にParameter Store のアクセス権限を分離
- 最小権限の原則を適用