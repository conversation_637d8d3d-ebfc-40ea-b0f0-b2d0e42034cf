"""
RAGFlow Retriever for GPT Researcher
Integrates with external RAGFlow RAG database for document retrieval
"""

import os
import requests
import json
from typing import List, Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class RAGFlowRetriever:
    """
    RAGFlow API Retriever for GPT Researcher
    Connects to external RAGFlow instance to retrieve relevant documents
    """

    def __init__(self, query: str, headers: Dict = None, query_domains: List[str] = None):
        """
        Initialize RAGFlow retriever
        
        Args:
            query: Search query string
            headers: Additional headers including ragflow_api_key
            query_domains: List of domains (not used in RAGFlow context)
        """
        self.query = query
        self.headers = headers or {}
        self.query_domains = query_domains
        
        # RAGFlow configuration
        self.base_url = self._get_base_url()
        self.api_key = self._get_api_key()
        self.dataset_ids = self._get_dataset_ids()
        self.document_ids = self._get_document_ids()
        
        # Search parameters
        self.similarity_threshold = float(os.getenv("RAGFLOW_SIMILARITY_THRESHOLD", "0.2"))
        self.vector_weight = float(os.getenv("RAGFLOW_VECTOR_WEIGHT", "0.3"))
        self.top_k = int(os.getenv("RAGFLOW_TOP_K", "20"))
        self.page_size = int(os.getenv("RAGFLOW_PAGE_SIZE", "30"))
        
        logger.info(f"RAGFlow Retriever initialized for query: {query[:50]}...")

    def _get_base_url(self) -> str:
        """Get RAGFlow base URL from environment or headers"""
        base_url = self.headers.get("ragflow_base_url") or os.getenv("RAGFLOW_BASE_URL")
        if not base_url:
            raise ValueError("RAGFlow base URL not found. Set RAGFLOW_BASE_URL environment variable or pass in headers")
        
        # Ensure URL ends with /api/v1
        if not base_url.endswith('/api/v1'):
            base_url = base_url.rstrip('/') + '/api/v1'
        
        return base_url

    def _get_api_key(self) -> str:
        """Get RAGFlow API key from environment or headers"""
        api_key = self.headers.get("ragflow_api_key") or os.getenv("RAGFLOW_API_KEY")
        if not api_key:
            raise ValueError("RAGFlow API key not found. Set RAGFLOW_API_KEY environment variable or pass in headers")
        return api_key

    def _get_dataset_ids(self) -> Optional[List[str]]:
        """Get dataset IDs to search in"""
        dataset_ids_str = self.headers.get("ragflow_dataset_ids") or os.getenv("RAGFLOW_DATASET_IDS")
        if dataset_ids_str:
            return [ds.strip() for ds in dataset_ids_str.split(',') if ds.strip()]
        return None

    def _get_document_ids(self) -> Optional[List[str]]:
        """Get specific document IDs to search in"""
        document_ids_str = self.headers.get("ragflow_document_ids") or os.getenv("RAGFLOW_DOCUMENT_IDS")
        if document_ids_str:
            return [doc.strip() for doc in document_ids_str.split(',') if doc.strip()]
        return None

    def search(self, max_results: int = 10) -> List[Dict[str, Any]]:
        """
        Search RAGFlow database for relevant documents
        
        Args:
            max_results: Maximum number of results to return
            
        Returns:
            List of search results in GPT Researcher format:
            [
                {
                    "url": "ragflow://document_id/chunk_id",
                    "raw_content": "Document content...",
                    "title": "Document title",
                    "snippet": "Relevant snippet",
                    "similarity": 0.85
                }
            ]
        """
        try:
            logger.info(f"Searching RAGFlow for: {self.query}")
            
            # Prepare request payload
            payload = {
                "question": self.query,
                "page_size": min(max_results, self.page_size),
                "similarity_threshold": self.similarity_threshold,
                "vector_similarity_weight": self.vector_weight,
                "top_k": self.top_k,
                "keyword": True,  # Enable keyword search
                "highlight": True  # Enable highlighting
            }
            
            # Add dataset or document IDs
            if self.dataset_ids:
                payload["dataset_ids"] = self.dataset_ids
            elif self.document_ids:
                payload["document_ids"] = self.document_ids
            else:
                logger.warning("No dataset_ids or document_ids specified. This may return no results.")
            
            # Make request to RAGFlow
            response = self._make_request(payload)
            
            if response and 'data' in response:
                chunks = response['data'].get('chunks', [])
                logger.info(f"Found {len(chunks)} chunks from RAGFlow")
                return self._format_results(chunks)
            else:
                logger.warning("No data returned from RAGFlow")
                return []
                
        except Exception as e:
            logger.error(f"Error searching RAGFlow: {str(e)}")
            return []

    def _make_request(self, payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Make HTTP request to RAGFlow API"""
        url = f"{self.base_url}/retrieval"
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        try:
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"RAGFlow API request failed: {str(e)}")
            if hasattr(e, 'response') and e.response is not None:
                logger.error(f"Response status: {e.response.status_code}")
                logger.error(f"Response body: {e.response.text}")
            raise

    def _format_results(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Format RAGFlow chunks into GPT Researcher format
        
        Args:
            chunks: List of RAGFlow chunk objects
            
        Returns:
            Formatted results for GPT Researcher
        """
        formatted_results = []
        
        for chunk in chunks:
            try:
                # Extract chunk information
                chunk_id = chunk.get('id', 'unknown')
                document_id = chunk.get('document_id', 'unknown')
                document_name = chunk.get('document_name', 'Unknown Document')
                content = chunk.get('content', '')
                similarity = chunk.get('similarity', 0.0)
                
                # Create GPT Researcher compatible result
                result = {
                    "url": f"ragflow://document/{document_id}/chunk/{chunk_id}",
                    "raw_content": content,
                    "title": document_name,
                    "snippet": content[:500] + "..." if len(content) > 500 else content,
                    "similarity": similarity,
                    "metadata": {
                        "source": "ragflow",
                        "document_id": document_id,
                        "chunk_id": chunk_id,
                        "document_name": document_name,
                        "vector_similarity": chunk.get('vector_similarity', 0.0),
                        "term_similarity": chunk.get('term_similarity', 0.0)
                    }
                }
                
                formatted_results.append(result)
                
            except Exception as e:
                logger.warning(f"Error formatting chunk {chunk.get('id', 'unknown')}: {str(e)}")
                continue
        
        # Sort by similarity score (highest first)
        formatted_results.sort(key=lambda x: x['similarity'], reverse=True)
        
        logger.info(f"Formatted {len(formatted_results)} results from RAGFlow")
        return formatted_results

    def get_source_url(self, result: Dict[str, Any]) -> str:
        """
        Generate a source URL for citation purposes
        
        Args:
            result: Formatted search result
            
        Returns:
            Source URL string
        """
        metadata = result.get('metadata', {})
        document_name = metadata.get('document_name', 'Unknown Document')
        similarity = result.get('similarity', 0.0)
        
        return f"RAGFlow: {document_name} (similarity: {similarity:.2f})"


# Convenience function for backward compatibility
def search_ragflow(query: str, max_results: int = 10, **kwargs) -> List[Dict[str, Any]]:
    """
    Convenience function to search RAGFlow
    
    Args:
        query: Search query
        max_results: Maximum number of results
        **kwargs: Additional parameters passed to RAGFlowRetriever
        
    Returns:
        List of search results
    """
    retriever = RAGFlowRetriever(query, **kwargs)
    return retriever.search(max_results)