# RAGFlow + GPT Researcher Integration Guide

本指南详细说明如何将 RAGFlow RAG 数据库与 GPT Researcher 集成，实现强大的文档检索和研究能力。

## 🎯 集成优势

- **专业知识库**: 利用 RAGFlow 的精准文档检索能力
- **混合搜索**: 结合内部文档和外部网络信息
- **语义理解**: 基于向量相似度的智能匹配
- **企业应用**: 支持内部知识库和业务文档分析

## 📋 前置要求

### 1. RAGFlow 环境
```bash
# 确保 RAGFlow 服务运行中
curl http://localhost:9380/api/v1/health

# 准备好数据集 ID 和 API Key
```

### 2. GPT Researcher 环境
```bash
# 安装依赖
pip install -r requirements.txt

# 设置 API Keys
export OPENAI_API_KEY="your-key"
export TAVILY_API_KEY="your-key"  # 可选，用于混合搜索
```

## 🔧 配置步骤

### 1. 环境变量配置
```bash
# 复制配置模板
cp .env.ragflow.example .env

# 编辑配置文件
nano .env
```

### 2. 关键配置项
```bash
# RAGFlow 连接信息
RAGFLOW_BASE_URL=http://localhost:9380
RAGFLOW_API_KEY=your_api_key_here
RAGFLOW_DATASET_IDS=dataset1,dataset2

# 搜索参数调优
RAGFLOW_SIMILARITY_THRESHOLD=0.2    # 相似度阈值
RAGFLOW_VECTOR_WEIGHT=0.3           # 向量权重
RAGFLOW_TOP_K=20                    # 最大检索数量
```

## 🚀 使用方式

### 基础使用
```python
from gpt_researcher import GPTResearcher
from config.ragflow_config import RAGFlowConfig

# 配置 RAGFlow
config = RAGFlowConfig()

# 创建研究器
researcher = GPTResearcher(
    query="人工智能在医疗领域的应用",
    report_source="ragflow",
    headers=config.get_ragflow_headers()
)

# 执行研究
result = await researcher.conduct_research()
report = await researcher.write_report()
```

### 混合搜索模式
```python
# 结合 RAGFlow 和网络搜索
researcher = GPTResearcher(
    query="区块链技术发展趋势",
    report_source="hybrid",  # 混合模式
    headers=config.get_ragflow_headers()
)
```

### 领域特定研究
```python
# 医学研究示例
os.environ["RAGFLOW_DATASET_IDS"] = "medical_papers,clinical_trials"
os.environ["RAGFLOW_SIMILARITY_THRESHOLD"] = "0.15"

researcher = GPTResearcher(
    query="新冠病毒最新治疗方法",
    report_type="medical_research",
    report_source="ragflow"
)
```

## 📊 高级功能

### 1. 自定义检索器
```python
from gpt_researcher.retrievers.ragflow import RAGFlowRetriever

# 直接使用 RAGFlow 检索器
retriever = RAGFlowRetriever(
    query="市场竞争分析",
    headers={"ragflow_dataset_ids": "business_reports,market_data"}
)

results = retriever.search(max_results=15)
```

### 2. 业务流程集成
```python
class BusinessAnalysisAgent:
    def __init__(self):
        self.config = RAGFlowConfig()
    
    async def analyze_competitor(self, company_name):
        # 使用 RAGFlow 中的竞争对手数据
        researcher = GPTResearcher(
            query=f"{company_name} 竞争分析",
            report_source="ragflow",
            headers=self.config.get_ragflow_headers()
        )
        return await researcher.write_report()
```

### 3. 多数据源融合
```python
# 配置多个数据集
datasets = {
    "technical": "tech_docs,api_references",
    "business": "market_reports,financial_data",
    "research": "academic_papers,patent_documents"
}

for domain, dataset_ids in datasets.items():
    os.environ["RAGFLOW_DATASET_IDS"] = dataset_ids
    # 执行特定领域研究
```

## 🔍 搜索优化

### 相似度调优
```python
# 高精度搜索 (更少但更相关的结果)
RAGFLOW_SIMILARITY_THRESHOLD=0.4
RAGFLOW_VECTOR_WEIGHT=0.5

# 广泛搜索 (更多结果，包含边缘相关内容)
RAGFLOW_SIMILARITY_THRESHOLD=0.1
RAGFLOW_VECTOR_WEIGHT=0.2
```

### 检索策略
```python
# 精确匹配策略
config = {
    "similarity_threshold": 0.35,
    "vector_weight": 0.4,
    "top_k": 15
}

# 探索性搜索策略
config = {
    "similarity_threshold": 0.15,
    "vector_weight": 0.25,
    "top_k": 30
}
```

## 📈 性能监控

### 日志分析
```python
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 启用详细日志
researcher = GPTResearcher(
    query="your query",
    verbose=True,
    log_handler=logger
)
```

### 结果质量评估
```python
def evaluate_ragflow_results(results):
    """评估 RAGFlow 检索结果质量"""
    avg_similarity = sum(r['similarity'] for r in results) / len(results)
    unique_sources = len(set(r['metadata']['document_id'] for r in results))
    
    return {
        "avg_similarity": avg_similarity,
        "unique_sources": unique_sources,
        "total_results": len(results)
    }
```

## 🛠️ 故障排除

### 常见问题

1. **连接失败**
   ```bash
   # 检查 RAGFlow 服务状态
   curl http://localhost:9380/api/v1/health
   
   # 检查 API Key
   echo $RAGFLOW_API_KEY
   ```

2. **无搜索结果**
   ```bash
   # 降低相似度阈值
   export RAGFLOW_SIMILARITY_THRESHOLD=0.1
   
   # 检查数据集 ID
   echo $RAGFLOW_DATASET_IDS
   ```

3. **搜索质量差**
   ```bash
   # 调整向量权重
   export RAGFLOW_VECTOR_WEIGHT=0.4
   
   # 增加检索数量
   export RAGFLOW_TOP_K=50
   ```

### 调试模式
```python
# 启用调试日志
os.environ["LOG_LEVEL"] = "DEBUG"

# 查看详细的 API 请求
researcher = GPTResearcher(
    query="debug query",
    verbose=True
)
```

## 🔄 部署建议

### 开发环境
```bash
# 本地开发
python -m uvicorn main:app --reload --host 127.0.0.1
```

### 生产环境
```bash
# 使用 Gunicorn
gunicorn main:app --workers 4 --worker-class uvicorn.workers.UvicornWorker

# 容器化部署
docker-compose -f docker-compose.ragflow.yml up
```

### 负载均衡
```yaml
# nginx 配置示例
upstream ragflow_backend {
    server ragflow1:9380;
    server ragflow2:9380;
}

upstream gptr_backend {
    server gptr1:8000;
    server gptr2:8000;
}
```

## 📚 最佳实践

1. **数据准备**: 确保 RAGFlow 中的文档质量和索引完整性
2. **参数调优**: 根据具体领域调整相似度阈值和权重
3. **缓存策略**: 对频繁查询的结果进行缓存
4. **错误处理**: 实现 RAGFlow 不可用时的降级机制
5. **监控告警**: 监控 RAGFlow 响应时间和成功率

通过这种集成方式，你可以充分利用 RAGFlow 的强大文档检索能力和 GPT Researcher 的智能研究功能，构建高效的企业级知识分析系统。