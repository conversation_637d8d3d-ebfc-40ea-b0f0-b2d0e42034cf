package com.ms.bp.domain.file.model;

import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.interfaces.dto.request.ImportRequest;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.io.converter.DatabaseMappable;
import com.ms.bp.shared.common.io.validation.annotation.*;
import com.ms.bp.shared.util.DateUtil;
import com.ms.bp.shared.util.FunctionUtil;
import com.ms.bp.shared.util.RequestContext;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ファイル処理結果値オブジェクト
 * インポート・エクスポート処理の結果を表現する
 */
@Data
public class OutlookPlanInfo implements DatabaseMappable {
	
	private String no;
    private String ikanmotoBusho;
    private String ikansakiBusho;

    /**
     * エリアコード
     * 権限チェック対象
     */
    @Required(fieldName = "ｴﾘｱｺｰﾄﾞ")
    @Range(min = 4, max = 4, fieldName = "ｴﾘｱｺｰﾄﾞ")
    @NumericHalfWidth(fieldName = "ｴﾘｱｺｰﾄﾞ")
    private String areaCode;

    private String areaName;

    /**
     * グループコード
     * 複合主キーの一部
     */
    @Required(fieldName = "ｸﾞﾙｰﾌﾟCD")
    @Range(min = 4, max = 4, fieldName = "ｸﾞﾙｰﾌﾟCD")
    @NumericHalfWidth(fieldName = "ｸﾞﾙｰﾌﾟCD")
    private String groupCode;

    /**
     * ユニットコード
     */
    @Required(fieldName = "ﾕﾆｯﾄCD")
    @Range(min = 5, max = 5, fieldName = "ﾕﾆｯﾄCD")
    @NumericHalfWidth(fieldName = "ﾕﾆｯﾄCD")
    private String unitCode;

    /**
     * 担当者名
     */
    @Range(min = 1, max = 25, fieldName = "担当者")
    private String tantoshaName;

    /**
     * 採算CD7桁
     * 複合主キーの一部
     * マスタにない採算管理単位コードも入力可能
     */
    @Required(fieldName = "採算CD7桁")
    @Range(min = 7, max = 7, fieldName = "採算CD7桁")
    @HalfWidthAlphanumeric(fieldName = "採算CD7桁")
    private String saisanCode;

    private String saisanName;

    /**
     * 企業コード
     */
    @Required(fieldName = "企業CD")
    @Range(min = 7, max = 7, fieldName = "企業CD")
    @NumericHalfWidth(fieldName = "企業CD")
    private String kigyoCode;

    private String kigyoName;

    /**
     * カテゴリ
     */
    @Range(min = 1, max = 15, fieldName = "ｶﾃｺﾞﾘ")
    private String kategori;

    /**
     * サブカテ
     */
    @Range(min = 1, max = 15, fieldName = "ｻﾌﾞｶﾃ")
    private String subKategori;

    /**
     * 業態(事業計画)
     */
    @Range(min = 1, max = 30, fieldName = "業態(事業計画)")
    private String gyotai;

    /**
     * 変更後取組区分
     */
    @Range(min = 1, max = 20, fieldName = "変更後取組区分")
    private String afterTorikumKbn;

    /**
     * 業態比率
     */
    @DecimalFormat(fieldName = "業態比率")
    private String gyotaiHiritsu;


    // 4月(計画)_総売上高(在庫)
    @Required(fieldName = "4月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "4月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "4月(計画)_総売上高(在庫)")
    private String planAprAllSalInven;
    // 4月(計画)_総売上高(直送)
    @Required(fieldName = "4月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "4月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "4月(計画)_総売上高(直送)")
    private String planAprAllSalChokusou;
    // 4月(計画)_総売上高計
    //@Required(fieldName = "4月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "4月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "4月(計画)_総売上高計")
    //private String planAprAllSalTotal;
    // 4月(計画)_返品(在庫)
    @Required(fieldName = "4月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "4月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "4月(計画)_返品(在庫)")
    private String planAprReturnInven;
    // 4月(計画)_返品(直送)
    @Required(fieldName = "4月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "4月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "4月(計画)_返品(直送)")
    private String planAprReturnChokusou;
    // 4月(計画)_返品計
    //@Required(fieldName = "4月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "4月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "4月(計画)_返品計")
    //private String planAprReturnTotal;
    // 4月(計画)_リベート(在庫)
    @Required(fieldName = "4月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "4月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "4月(計画)_リベート(在庫)")
    private String planAprRebateInven;
    // 4月(計画)_リベート(直送)
    @Required(fieldName = "4月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "4月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "4月(計画)_リベート(直送)")
    private String planAprRebateChokusou;
    // 4月(計画)_リベート計
    //@Required(fieldName = "4月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "4月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "4月(計画)_リベート計")
    //private String planAprRebateTotal;
    // 4月(計画)_センターフィ(在庫)
    @Required(fieldName = "4月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "4月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "4月(計画)_センターフィ(在庫)")
    private String planAprCenterFeeInven;
    // 4月(計画)_センターフィ(直送)
    @Required(fieldName = "4月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "4月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "4月(計画)_センターフィ(直送)")
    private String planAprCenterFeeChokusou;
    // 4月(計画)_センターフィ計
    //@Required(fieldName = "4月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "4月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "4月(計画)_センターフィ計")
    //private String planAprCenterFeeTotal;
    // 4月(計画)_直接利益(在庫)
    @Required(fieldName = "4月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "4月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "4月(計画)_直接利益(在庫)")
    private String planAprChokuRiekiInven;
    // 4月(計画)_直接利益(直送)
    @Required(fieldName = "4月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "4月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "4月(計画)_直接利益(直送)")
    private String planAprChokuRiekiChokusou;
    // 4月(計画)_直接利益計
    //@Required(fieldName = "4月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "4月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "4月(計画)_直接利益計")
    //private String planAprChokuRiekiTotal;
    // 4月(計画)_直利率(在庫)
    //@Required(fieldName = "4月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "4月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "4月(計画)_直利率(在庫)")
    //private String planAprChokuRateInven;
    // 4月(計画)_直利率(直送)
    //@Required(fieldName = "4月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "4月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "4月(計画)_直利率(直送)")
    //private String planAprChokuRateChokusou;
    // 4月(計画)_直利率計
    //@Required(fieldName = "4月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "4月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "4月(計画)_直利率計")
    //private String planAprChokuRateTotal;
    // 5月(計画)_総売上高(在庫)
    @Required(fieldName = "5月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "5月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "5月(計画)_総売上高(在庫)")
    private String planMayAllSalInven;
    // 5月(計画)_総売上高(直送)
    @Required(fieldName = "5月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "5月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "5月(計画)_総売上高(直送)")
    private String planMayAllSalChokusou;
    // 5月(計画)_総売上高計
    //@Required(fieldName = "5月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "5月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "5月(計画)_総売上高計")
    //private String planMayAllSalTotal;
    // 5月(計画)_返品(在庫)
    @Required(fieldName = "5月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "5月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "5月(計画)_返品(在庫)")
    private String planMayReturnInven;
    // 5月(計画)_返品(直送)
    @Required(fieldName = "5月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "5月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "5月(計画)_返品(直送)")
    private String planMayReturnChokusou;
    // 5月(計画)_返品計
    //@Required(fieldName = "5月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "5月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "5月(計画)_返品計")
    //private String planMayReturnTotal;
    // 5月(計画)_リベート(在庫)
    @Required(fieldName = "5月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "5月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "5月(計画)_リベート(在庫)")
    private String planMayRebateInven;
    // 5月(計画)_リベート(直送)
    @Required(fieldName = "5月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "5月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "5月(計画)_リベート(直送)")
    private String planMayRebateChokusou;
    // 5月(計画)_リベート計
    //@Required(fieldName = "5月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "5月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "5月(計画)_リベート計")
    //private String planMayRebateTotal;
    // 5月(計画)_センターフィ(在庫)
    @Required(fieldName = "5月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "5月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "5月(計画)_センターフィ(在庫)")
    private String planMayCenterFeeInven;
    // 5月(計画)_センターフィ(直送)
    @Required(fieldName = "5月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "5月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "5月(計画)_センターフィ(直送)")
    private String planMayCenterFeeChokusou;
    // 5月(計画)_センターフィ計
    //@Required(fieldName = "5月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "5月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "5月(計画)_センターフィ計")
    //private String planMayCenterFeeTotal;
    // 5月(計画)_直接利益(在庫)
    @Required(fieldName = "5月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "5月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "5月(計画)_直接利益(在庫)")
    private String planMayChokuRiekiInven;
    // 5月(計画)_直接利益(直送)
    @Required(fieldName = "5月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "5月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "5月(計画)_直接利益(直送)")
    private String planMayChokuRiekiChokusou;
    // 5月(計画)_直接利益計
    //@Required(fieldName = "5月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "5月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "5月(計画)_直接利益計")
    //private String planMayChokuRiekiTotal;
    // 5月(計画)_直利率(在庫)
    //@Required(fieldName = "5月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "5月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "5月(計画)_直利率(在庫)")
    //private String planMayChokuRateInven;
    // 5月(計画)_直利率(直送)
    //@Required(fieldName = "5月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "5月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "5月(計画)_直利率(直送)")
    //private String planMayChokuRateChokusou;
    // 5月(計画)_直利率計
    //@Required(fieldName = "5月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "5月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "5月(計画)_直利率計")
    //private String planMayChokuRateTotal;
    // 6月(計画)_総売上高(在庫)
    @Required(fieldName = "6月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "6月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "6月(計画)_総売上高(在庫)")
    private String planJunAllSalInven;
    // 6月(計画)_総売上高(直送)
    @Required(fieldName = "6月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "6月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "6月(計画)_総売上高(直送)")
    private String planJunAllSalChokusou;
    // 6月(計画)_総売上高計
    //@Required(fieldName = "6月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "6月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "6月(計画)_総売上高計")
    //private String planJunAllSalTotal;
    // 6月(計画)_返品(在庫)
    @Required(fieldName = "6月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "6月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "6月(計画)_返品(在庫)")
    private String planJunReturnInven;
    // 6月(計画)_返品(直送)
    @Required(fieldName = "6月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "6月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "6月(計画)_返品(直送)")
    private String planJunReturnChokusou;
    // 6月(計画)_返品計
    //@Required(fieldName = "6月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "6月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "6月(計画)_返品計")
    //private String planJunReturnTotal;
    // 6月(計画)_リベート(在庫)
    @Required(fieldName = "6月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "6月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "6月(計画)_リベート(在庫)")
    private String planJunRebateInven;
    // 6月(計画)_リベート(直送)
    @Required(fieldName = "6月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "6月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "6月(計画)_リベート(直送)")
    private String planJunRebateChokusou;
    // 6月(計画)_リベート計
    //@Required(fieldName = "6月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "6月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "6月(計画)_リベート計")
    //private String planJunRebateTotal;
    // 6月(計画)_センターフィ(在庫)
    @Required(fieldName = "6月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "6月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "6月(計画)_センターフィ(在庫)")
    private String planJunCenterFeeInven;
    // 6月(計画)_センターフィ(直送)
    @Required(fieldName = "6月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "6月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "6月(計画)_センターフィ(直送)")
    private String planJunCenterFeeChokusou;
    // 6月(計画)_センターフィ計
    //@Required(fieldName = "6月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "6月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "6月(計画)_センターフィ計")
    //private String planJunCenterFeeTotal;
    // 6月(計画)_直接利益(在庫)
    @Required(fieldName = "6月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "6月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "6月(計画)_直接利益(在庫)")
    private String planJunChokuRiekiInven;
    // 6月(計画)_直接利益(直送)
    @Required(fieldName = "6月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "6月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "6月(計画)_直接利益(直送)")
    private String planJunChokuRiekiChokusou;
    // 6月(計画)_直接利益計
    //@Required(fieldName = "6月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "6月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "6月(計画)_直接利益計")
    //private String planJunChokuRiekiTotal;
    // 6月(計画)_直利率(在庫)
    //@Required(fieldName = "6月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "6月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "6月(計画)_直利率(在庫)")
    //private String planJunChokuRateInven;
    // 6月(計画)_直利率(直送)
    //@Required(fieldName = "6月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "6月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "6月(計画)_直利率(直送)")
    //private String planJunChokuRateChokusou;
    // 6月(計画)_直利率計
    //@Required(fieldName = "6月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "6月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "6月(計画)_直利率計")
    //private String planJunChokuRateTotal;
    // 7月(計画)_総売上高(在庫)
    @Required(fieldName = "7月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "7月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "7月(計画)_総売上高(在庫)")
    private String planJulAllSalInven;
    // 7月(計画)_総売上高(直送)
    @Required(fieldName = "7月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "7月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "7月(計画)_総売上高(直送)")
    private String planJulAllSalChokusou;
    // 7月(計画)_総売上高計
    //@Required(fieldName = "7月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "7月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "7月(計画)_総売上高計")
    //private String planJulAllSalTotal;
    // 7月(計画)_返品(在庫)
    @Required(fieldName = "7月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "7月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "7月(計画)_返品(在庫)")
    private String planJulReturnInven;
    // 7月(計画)_返品(直送)
    @Required(fieldName = "7月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "7月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "7月(計画)_返品(直送)")
    private String planJulReturnChokusou;
    // 7月(計画)_返品計
    //@Required(fieldName = "7月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "7月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "7月(計画)_返品計")
    //private String planJulReturnTotal;
    // 7月(計画)_リベート(在庫)
    @Required(fieldName = "7月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "7月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "7月(計画)_リベート(在庫)")
    private String planJulRebateInven;
    // 7月(計画)_リベート(直送)
    @Required(fieldName = "7月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "7月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "7月(計画)_リベート(直送)")
    private String planJulRebateChokusou;
    // 7月(計画)_リベート計
    //@Required(fieldName = "7月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "7月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "7月(計画)_リベート計")
    //private String planJulRebateTotal;
    // 7月(計画)_センターフィ(在庫)
    @Required(fieldName = "7月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "7月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "7月(計画)_センターフィ(在庫)")
    private String planJulCenterFeeInven;
    // 7月(計画)_センターフィ(直送)
    @Required(fieldName = "7月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "7月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "7月(計画)_センターフィ(直送)")
    private String planJulCenterFeeChokusou;
    // 7月(計画)_センターフィ計
    //@Required(fieldName = "7月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "7月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "7月(計画)_センターフィ計")
    //private String planJulCenterFeeTotal;
    // 7月(計画)_直接利益(在庫)
    @Required(fieldName = "7月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "7月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "7月(計画)_直接利益(在庫)")
    private String planJulChokuRiekiInven;
    // 7月(計画)_直接利益(直送)
    @Required(fieldName = "7月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "7月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "7月(計画)_直接利益(直送)")
    private String planJulChokuRiekiChokusou;
    // 7月(計画)_直接利益計
    //@Required(fieldName = "7月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "7月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "7月(計画)_直接利益計")
    //private String planJulChokuRiekiTotal;
    // 7月(計画)_直利率(在庫)
    //@Required(fieldName = "7月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "7月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "7月(計画)_直利率(在庫)")
    //private String planJulChokuRateInven;
    // 7月(計画)_直利率(直送)
    //@Required(fieldName = "7月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "7月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "7月(計画)_直利率(直送)")
    //private String planJulChokuRateChokusou;
    // 7月(計画)_直利率計
    //@Required(fieldName = "7月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "7月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "7月(計画)_直利率計")
    //private String planJulChokuRateTotal;
    // 8月(計画)_総売上高(在庫)
    @Required(fieldName = "8月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "8月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "8月(計画)_総売上高(在庫)")
    private String planAugAllSalInven;
    // 8月(計画)_総売上高(直送)
    @Required(fieldName = "8月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "8月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "8月(計画)_総売上高(直送)")
    private String planAugAllSalChokusou;
    // 8月(計画)_総売上高計
    //@Required(fieldName = "8月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "8月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "8月(計画)_総売上高計")
    //private String planAugAllSalTotal;
    // 8月(計画)_返品(在庫)
    @Required(fieldName = "8月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "8月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "8月(計画)_返品(在庫)")
    private String planAugReturnInven;
    // 8月(計画)_返品(直送)
    @Required(fieldName = "8月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "8月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "8月(計画)_返品(直送)")
    private String planAugReturnChokusou;
    // 8月(計画)_返品計
    //@Required(fieldName = "8月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "8月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "8月(計画)_返品計")
    //private String planAugReturnTotal;
    // 8月(計画)_リベート(在庫)
    @Required(fieldName = "8月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "8月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "8月(計画)_リベート(在庫)")
    private String planAugRebateInven;
    // 8月(計画)_リベート(直送)
    @Required(fieldName = "8月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "8月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "8月(計画)_リベート(直送)")
    private String planAugRebateChokusou;
    // 8月(計画)_リベート計
    //@Required(fieldName = "8月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "8月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "8月(計画)_リベート計")
    //private String planAugRebateTotal;
    // 8月(計画)_センターフィ(在庫)
    @Required(fieldName = "8月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "8月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "8月(計画)_センターフィ(在庫)")
    private String planAugCenterFeeInven;
    // 8月(計画)_センターフィ(直送)
    @Required(fieldName = "8月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "8月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "8月(計画)_センターフィ(直送)")
    private String planAugCenterFeeChokusou;
    // 8月(計画)_センターフィ計
    //@Required(fieldName = "8月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "8月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "8月(計画)_センターフィ計")
    //private String planAugCenterFeeTotal;
    // 8月(計画)_直接利益(在庫)
    @Required(fieldName = "8月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "8月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "8月(計画)_直接利益(在庫)")
    private String planAugChokuRiekiInven;
    // 8月(計画)_直接利益(直送)
    @Required(fieldName = "8月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "8月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "8月(計画)_直接利益(直送)")
    private String planAugChokuRiekiChokusou;
    // 8月(計画)_直接利益計
    //@Required(fieldName = "8月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "8月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "8月(計画)_直接利益計")
    //private String planAugChokuRiekiTotal;
    // 8月(計画)_直利率(在庫)
    //@Required(fieldName = "8月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "8月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "8月(計画)_直利率(在庫)")
    //private String planAugChokuRateInven;
    // 8月(計画)_直利率(直送)
    //@Required(fieldName = "8月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "8月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "8月(計画)_直利率(直送)")
    //private String planAugChokuRateChokusou;
    // 8月(計画)_直利率計
    //@Required(fieldName = "8月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "8月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "8月(計画)_直利率計")
    //private String planAugChokuRateTotal;
    // 9月(計画)_総売上高(在庫)
    @Required(fieldName = "9月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "9月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "9月(計画)_総売上高(在庫)")
    private String planSepAllSalInven;
    // 9月(計画)_総売上高(直送)
    @Required(fieldName = "9月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "9月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "9月(計画)_総売上高(直送)")
    private String planSepAllSalChokusou;
    // 9月(計画)_総売上高計
    //@Required(fieldName = "9月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "9月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "9月(計画)_総売上高計")
    //private String planSepAllSalTotal;
    // 9月(計画)_返品(在庫)
    @Required(fieldName = "9月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "9月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "9月(計画)_返品(在庫)")
    private String planSepReturnInven;
    // 9月(計画)_返品(直送)
    @Required(fieldName = "9月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "9月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "9月(計画)_返品(直送)")
    private String planSepReturnChokusou;
    // 9月(計画)_返品計
    //@Required(fieldName = "9月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "9月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "9月(計画)_返品計")
    //private String planSepReturnTotal;
    // 9月(計画)_リベート(在庫)
    @Required(fieldName = "9月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "9月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "9月(計画)_リベート(在庫)")
    private String planSepRebateInven;
    // 9月(計画)_リベート(直送)
    @Required(fieldName = "9月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "9月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "9月(計画)_リベート(直送)")
    private String planSepRebateChokusou;
    // 9月(計画)_リベート計
    //@Required(fieldName = "9月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "9月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "9月(計画)_リベート計")
    //private String planSepRebateTotal;
    // 9月(計画)_センターフィ(在庫)
    @Required(fieldName = "9月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "9月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "9月(計画)_センターフィ(在庫)")
    private String planSepCenterFeeInven;
    // 9月(計画)_センターフィ(直送)
    @Required(fieldName = "9月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "9月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "9月(計画)_センターフィ(直送)")
    private String planSepCenterFeeChokusou;
    // 9月(計画)_センターフィ計
    //@Required(fieldName = "9月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "9月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "9月(計画)_センターフィ計")
    //private String planSepCenterFeeTotal;
    // 9月(計画)_直接利益(在庫)
    @Required(fieldName = "9月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "9月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "9月(計画)_直接利益(在庫)")
    private String planSepChokuRiekiInven;
    // 9月(計画)_直接利益(直送)
    @Required(fieldName = "9月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "9月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "9月(計画)_直接利益(直送)")
    private String planSepChokuRiekiChokusou;
    // 9月(計画)_直接利益計
    //@Required(fieldName = "9月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "9月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "9月(計画)_直接利益計")
    //private String planSepChokuRiekiTotal;
    // 9月(計画)_直利率(在庫)
    //@Required(fieldName = "9月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "9月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "9月(計画)_直利率(在庫)")
    //private String planSepChokuRateInven;
    // 9月(計画)_直利率(直送)
    //@Required(fieldName = "9月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "9月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "9月(計画)_直利率(直送)")
    //private String planSepChokuRateChokusou;
    // 9月(計画)_直利率計
    //@Required(fieldName = "9月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "9月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "9月(計画)_直利率計")
    //private String planSepChokuRateTotal;
    // 10月(計画)_総売上高(在庫)
    @Required(fieldName = "10月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "10月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "10月(計画)_総売上高(在庫)")
    private String planOctAllSalInven;
    // 10月(計画)_総売上高(直送)
    @Required(fieldName = "10月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "10月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "10月(計画)_総売上高(直送)")
    private String planOctAllSalChokusou;
    // 10月(計画)_総売上高計
    //@Required(fieldName = "10月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "10月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "10月(計画)_総売上高計")
    //private String planOctAllSalTotal;
    // 10月(計画)_返品(在庫)
    @Required(fieldName = "10月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "10月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "10月(計画)_返品(在庫)")
    private String planOctReturnInven;
    // 10月(計画)_返品(直送)
    @Required(fieldName = "10月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "10月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "10月(計画)_返品(直送)")
    private String planOctReturnChokusou;
    // 10月(計画)_返品計
    //@Required(fieldName = "10月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "10月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "10月(計画)_返品計")
    //private String planOctReturnTotal;
    // 10月(計画)_リベート(在庫)
    @Required(fieldName = "10月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "10月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "10月(計画)_リベート(在庫)")
    private String planOctRebateInven;
    // 10月(計画)_リベート(直送)
    @Required(fieldName = "10月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "10月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "10月(計画)_リベート(直送)")
    private String planOctRebateChokusou;
    // 10月(計画)_リベート計
    //@Required(fieldName = "10月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "10月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "10月(計画)_リベート計")
    //private String planOctRebateTotal;
    // 10月(計画)_センターフィ(在庫)
    @Required(fieldName = "10月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "10月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "10月(計画)_センターフィ(在庫)")
    private String planOctCenterFeeInven;
    // 10月(計画)_センターフィ(直送)
    @Required(fieldName = "10月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "10月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "10月(計画)_センターフィ(直送)")
    private String planOctCenterFeeChokusou;
    // 10月(計画)_センターフィ計
    //@Required(fieldName = "10月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "10月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "10月(計画)_センターフィ計")
    //private String planOctCenterFeeTotal;
    // 10月(計画)_直接利益(在庫)
    @Required(fieldName = "10月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "10月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "10月(計画)_直接利益(在庫)")
    private String planOctChokuRiekiInven;
    // 10月(計画)_直接利益(直送)
    @Required(fieldName = "10月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "10月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "10月(計画)_直接利益(直送)")
    private String planOctChokuRiekiChokusou;
    // 10月(計画)_直接利益計
    //@Required(fieldName = "10月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "10月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "10月(計画)_直接利益計")
    //private String planOctChokuRiekiTotal;
    // 10月(計画)_直利率(在庫)
    //@Required(fieldName = "10月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "10月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "10月(計画)_直利率(在庫)")
    //private String planOctChokuRateInven;
    // 10月(計画)_直利率(直送)
    //@Required(fieldName = "10月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "10月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "10月(計画)_直利率(直送)")
    //private String planOctChokuRateChokusou;
    // 10月(計画)_直利率計
    //@Required(fieldName = "10月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "10月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "10月(計画)_直利率計")
    //private String planOctChokuRateTotal;
    // 11月(計画)_総売上高(在庫)
    @Required(fieldName = "11月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "11月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "11月(計画)_総売上高(在庫)")
    private String planNovAllSalInven;
    // 11月(計画)_総売上高(直送)
    @Required(fieldName = "11月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "11月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "11月(計画)_総売上高(直送)")
    private String planNovAllSalChokusou;
    // 11月(計画)_総売上高計
    //@Required(fieldName = "11月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "11月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "11月(計画)_総売上高計")
    //private String planNovAllSalTotal;
    // 11月(計画)_返品(在庫)
    @Required(fieldName = "11月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "11月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "11月(計画)_返品(在庫)")
    private String planNovReturnInven;
    // 11月(計画)_返品(直送)
    @Required(fieldName = "11月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "11月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "11月(計画)_返品(直送)")
    private String planNovReturnChokusou;
    // 11月(計画)_返品計
    //@Required(fieldName = "11月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "11月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "11月(計画)_返品計")
    //private String planNovReturnTotal;
    // 11月(計画)_リベート(在庫)
    @Required(fieldName = "11月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "11月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "11月(計画)_リベート(在庫)")
    private String planNovRebateInven;
    // 11月(計画)_リベート(直送)
    @Required(fieldName = "11月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "11月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "11月(計画)_リベート(直送)")
    private String planNovRebateChokusou;
    // 11月(計画)_リベート計
    //@Required(fieldName = "11月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "11月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "11月(計画)_リベート計")
    //private String planNovRebateTotal;
    // 11月(計画)_センターフィ(在庫)
    @Required(fieldName = "11月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "11月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "11月(計画)_センターフィ(在庫)")
    private String planNovCenterFeeInven;
    // 11月(計画)_センターフィ(直送)
    @Required(fieldName = "11月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "11月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "11月(計画)_センターフィ(直送)")
    private String planNovCenterFeeChokusou;
    // 11月(計画)_センターフィ計
    //@Required(fieldName = "11月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "11月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "11月(計画)_センターフィ計")
    //private String planNovCenterFeeTotal;
    // 11月(計画)_直接利益(在庫)
    @Required(fieldName = "11月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "11月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "11月(計画)_直接利益(在庫)")
    private String planNovChokuRiekiInven;
    // 11月(計画)_直接利益(直送)
    @Required(fieldName = "11月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "11月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "11月(計画)_直接利益(直送)")
    private String planNovChokuRiekiChokusou;
    // 11月(計画)_直接利益計
    //@Required(fieldName = "11月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "11月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "11月(計画)_直接利益計")
    //private String planNovChokuRiekiTotal;
    // 11月(計画)_直利率(在庫)
    //@Required(fieldName = "11月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "11月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "11月(計画)_直利率(在庫)")
    //private String planNovChokuRateInven;
    // 11月(計画)_直利率(直送)
    //@Required(fieldName = "11月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "11月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "11月(計画)_直利率(直送)")
    //private String planNovChokuRateChokusou;
    // 11月(計画)_直利率計
    //@Required(fieldName = "11月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "11月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "11月(計画)_直利率計")
    //private String planNovChokuRateTotal;
    // 12月(計画)_総売上高(在庫)
    @Required(fieldName = "12月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "12月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "12月(計画)_総売上高(在庫)")
    private String planDecAllSalInven;
    // 12月(計画)_総売上高(直送)
    @Required(fieldName = "12月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "12月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "12月(計画)_総売上高(直送)")
    private String planDecAllSalChokusou;
    // 12月(計画)_総売上高計
    //@Required(fieldName = "12月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "12月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "12月(計画)_総売上高計")
    //private String planDecAllSalTotal;
    // 12月(計画)_返品(在庫)
    @Required(fieldName = "12月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "12月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "12月(計画)_返品(在庫)")
    private String planDecReturnInven;
    // 12月(計画)_返品(直送)
    @Required(fieldName = "12月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "12月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "12月(計画)_返品(直送)")
    private String planDecReturnChokusou;
    // 12月(計画)_返品計
    //@Required(fieldName = "12月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "12月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "12月(計画)_返品計")
    //private String planDecReturnTotal;
    // 12月(計画)_リベート(在庫)
    @Required(fieldName = "12月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "12月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "12月(計画)_リベート(在庫)")
    private String planDecRebateInven;
    // 12月(計画)_リベート(直送)
    @Required(fieldName = "12月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "12月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "12月(計画)_リベート(直送)")
    private String planDecRebateChokusou;
    // 12月(計画)_リベート計
    //@Required(fieldName = "12月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "12月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "12月(計画)_リベート計")
    //private String planDecRebateTotal;
    // 12月(計画)_センターフィ(在庫)
    @Required(fieldName = "12月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "12月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "12月(計画)_センターフィ(在庫)")
    private String planDecCenterFeeInven;
    // 12月(計画)_センターフィ(直送)
    @Required(fieldName = "12月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "12月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "12月(計画)_センターフィ(直送)")
    private String planDecCenterFeeChokusou;
    // 12月(計画)_センターフィ計
    //@Required(fieldName = "12月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "12月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "12月(計画)_センターフィ計")
    //private String planDecCenterFeeTotal;
    // 12月(計画)_直接利益(在庫)
    @Required(fieldName = "12月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "12月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "12月(計画)_直接利益(在庫)")
    private String planDecChokuRiekiInven;
    // 12月(計画)_直接利益(直送)
    @Required(fieldName = "12月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "12月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "12月(計画)_直接利益(直送)")
    private String planDecChokuRiekiChokusou;
    // 12月(計画)_直接利益計
    //@Required(fieldName = "12月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "12月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "12月(計画)_直接利益計")
    //private String planDecChokuRiekiTotal;
    // 12月(計画)_直利率(在庫)
    //@Required(fieldName = "12月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "12月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "12月(計画)_直利率(在庫)")
    //private String planDecChokuRateInven;
    // 12月(計画)_直利率(直送)
    //@Required(fieldName = "12月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "12月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "12月(計画)_直利率(直送)")
    //private String planDecChokuRateChokusou;
    // 12月(計画)_直利率計
    //@Required(fieldName = "12月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "12月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "12月(計画)_直利率計")
    //private String planDecChokuRateTotal;
    // 1月(計画)_総売上高(在庫)
    @Required(fieldName = "1月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "1月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "1月(計画)_総売上高(在庫)")
    private String planJanAllSalInven;
    // 1月(計画)_総売上高(直送)
    @Required(fieldName = "1月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "1月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "1月(計画)_総売上高(直送)")
    private String planJanAllSalChokusou;
    // 1月(計画)_総売上高計
    //@Required(fieldName = "1月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "1月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "1月(計画)_総売上高計")
    //private String planJanAllSalTotal;
    // 1月(計画)_返品(在庫)
    @Required(fieldName = "1月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "1月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "1月(計画)_返品(在庫)")
    private String planJanReturnInven;
    // 1月(計画)_返品(直送)
    @Required(fieldName = "1月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "1月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "1月(計画)_返品(直送)")
    private String planJanReturnChokusou;
    // 1月(計画)_返品計
    //@Required(fieldName = "1月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "1月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "1月(計画)_返品計")
    //private String planJanReturnTotal;
    // 1月(計画)_リベート(在庫)
    @Required(fieldName = "1月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "1月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "1月(計画)_リベート(在庫)")
    private String planJanRebateInven;
    // 1月(計画)_リベート(直送)
    @Required(fieldName = "1月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "1月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "1月(計画)_リベート(直送)")
    private String planJanRebateChokusou;
    // 1月(計画)_リベート計
    //@Required(fieldName = "1月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "1月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "1月(計画)_リベート計")
    //private String planJanRebateTotal;
    // 1月(計画)_センターフィ(在庫)
    @Required(fieldName = "1月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "1月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "1月(計画)_センターフィ(在庫)")
    private String planJanCenterFeeInven;
    // 1月(計画)_センターフィ(直送)
    @Required(fieldName = "1月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "1月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "1月(計画)_センターフィ(直送)")
    private String planJanCenterFeeChokusou;
    // 1月(計画)_センターフィ計
    //@Required(fieldName = "1月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "1月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "1月(計画)_センターフィ計")
    //private String planJanCenterFeeTotal;
    // 1月(計画)_直接利益(在庫)
    @Required(fieldName = "1月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "1月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "1月(計画)_直接利益(在庫)")
    private String planJanChokuRiekiInven;
    // 1月(計画)_直接利益(直送)
    @Required(fieldName = "1月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "1月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "1月(計画)_直接利益(直送)")
    private String planJanChokuRiekiChokusou;
    // 1月(計画)_直接利益計
    //@Required(fieldName = "1月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "1月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "1月(計画)_直接利益計")
    //private String planJanChokuRiekiTotal;
    // 1月(計画)_直利率(在庫)
    //@Required(fieldName = "1月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "1月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "1月(計画)_直利率(在庫)")
    //private String planJanChokuRateInven;
    // 1月(計画)_直利率(直送)
    //@Required(fieldName = "1月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "1月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "1月(計画)_直利率(直送)")
    //private String planJanChokuRateChokusou;
    // 1月(計画)_直利率計
    //@Required(fieldName = "1月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "1月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "1月(計画)_直利率計")
    //private String planJanChokuRateTotal;
    // 2月(計画)_総売上高(在庫)
    @Required(fieldName = "2月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "2月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "2月(計画)_総売上高(在庫)")
    private String planFebAllSalInven;
    // 2月(計画)_総売上高(直送)
    @Required(fieldName = "2月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "2月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "2月(計画)_総売上高(直送)")
    private String planFebAllSalChokusou;
    // 2月(計画)_総売上高計
    //@Required(fieldName = "2月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "2月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "2月(計画)_総売上高計")
    //private String planFebAllSalTotal;
    // 2月(計画)_返品(在庫)
    @Required(fieldName = "2月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "2月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "2月(計画)_返品(在庫)")
    private String planFebReturnInven;
    // 2月(計画)_返品(直送)
    @Required(fieldName = "2月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "2月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "2月(計画)_返品(直送)")
    private String planFebReturnChokusou;
    // 2月(計画)_返品計
    //@Required(fieldName = "2月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "2月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "2月(計画)_返品計")
    //private String planFebReturnTotal;
    // 2月(計画)_リベート(在庫)
    @Required(fieldName = "2月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "2月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "2月(計画)_リベート(在庫)")
    private String planFebRebateInven;
    // 2月(計画)_リベート(直送)
    @Required(fieldName = "2月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "2月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "2月(計画)_リベート(直送)")
    private String planFebRebateChokusou;
    // 2月(計画)_リベート計
    //@Required(fieldName = "2月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "2月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "2月(計画)_リベート計")
    //private String planFebRebateTotal;
    // 2月(計画)_センターフィ(在庫)
    @Required(fieldName = "2月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "2月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "2月(計画)_センターフィ(在庫)")
    private String planFebCenterFeeInven;
    // 2月(計画)_センターフィ(直送)
    @Required(fieldName = "2月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "2月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "2月(計画)_センターフィ(直送)")
    private String planFebCenterFeeChokusou;
    // 2月(計画)_センターフィ計
    //@Required(fieldName = "2月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "2月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "2月(計画)_センターフィ計")
    //private String planFebCenterFeeTotal;
    // 2月(計画)_直接利益(在庫)
    @Required(fieldName = "2月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "2月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "2月(計画)_直接利益(在庫)")
    private String planFebChokuRiekiInven;
    // 2月(計画)_直接利益(直送)
    @Required(fieldName = "2月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "2月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "2月(計画)_直接利益(直送)")
    private String planFebChokuRiekiChokusou;
    // 2月(計画)_直接利益計
    //@Required(fieldName = "2月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "2月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "2月(計画)_直接利益計")
    //private String planFebChokuRiekiTotal;
    // 2月(計画)_直利率(在庫)
    //@Required(fieldName = "2月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "2月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "2月(計画)_直利率(在庫)")
    //private String planFebChokuRateInven;
    // 2月(計画)_直利率(直送)
    //@Required(fieldName = "2月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "2月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "2月(計画)_直利率(直送)")
    //private String planFebChokuRateChokusou;
    // 2月(計画)_直利率計
    //@Required(fieldName = "2月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "2月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "2月(計画)_直利率計")
    //private String planFebChokuRateTotal;
    // 3月(計画)_総売上高(在庫)
    @Required(fieldName = "3月(計画)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "3月(計画)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "3月(計画)_総売上高(在庫)")
    private String planMarAllSalInven;
    // 3月(計画)_総売上高(直送)
    @Required(fieldName = "3月(計画)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "3月(計画)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "3月(計画)_総売上高(直送)")
    private String planMarAllSalChokusou;
    // 3月(計画)_総売上高計
    //@Required(fieldName = "3月(計画)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "3月(計画)_総売上高計")
    //@NumericHalfWidth(fieldName = "3月(計画)_総売上高計")
    //private String planMarAllSalTotal;
    // 3月(計画)_返品(在庫)
    @Required(fieldName = "3月(計画)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "3月(計画)_返品(在庫)")
    @NumericHalfWidth(fieldName = "3月(計画)_返品(在庫)")
    private String planMarReturnInven;
    // 3月(計画)_返品(直送)
    @Required(fieldName = "3月(計画)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "3月(計画)_返品(直送)")
    @NumericHalfWidth(fieldName = "3月(計画)_返品(直送)")
    private String planMarReturnChokusou;
    // 3月(計画)_返品計
    //@Required(fieldName = "3月(計画)_返品計")
    //@Range(min = 1, max = 10, fieldName = "3月(計画)_返品計")
    //@NumericHalfWidth(fieldName = "3月(計画)_返品計")
    //private String planMarReturnTotal;
    // 3月(計画)_リベート(在庫)
    @Required(fieldName = "3月(計画)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "3月(計画)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "3月(計画)_リベート(在庫)")
    private String planMarRebateInven;
    // 3月(計画)_リベート(直送)
    @Required(fieldName = "3月(計画)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "3月(計画)_リベート(直送)")
    @NumericHalfWidth(fieldName = "3月(計画)_リベート(直送)")
    private String planMarRebateChokusou;
    // 3月(計画)_リベート計
    //@Required(fieldName = "3月(計画)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "3月(計画)_リベート計")
    //@NumericHalfWidth(fieldName = "3月(計画)_リベート計")
    //private String planMarRebateTotal;
    // 3月(計画)_センターフィ(在庫)
    @Required(fieldName = "3月(計画)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "3月(計画)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "3月(計画)_センターフィ(在庫)")
    private String planMarCenterFeeInven;
    // 3月(計画)_センターフィ(直送)
    @Required(fieldName = "3月(計画)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "3月(計画)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "3月(計画)_センターフィ(直送)")
    private String planMarCenterFeeChokusou;
    // 3月(計画)_センターフィ計
    //@Required(fieldName = "3月(計画)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "3月(計画)_センターフィ計")
    //@NumericHalfWidth(fieldName = "3月(計画)_センターフィ計")
    //private String planMarCenterFeeTotal;
    // 3月(計画)_直接利益(在庫)
    @Required(fieldName = "3月(計画)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "3月(計画)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "3月(計画)_直接利益(在庫)")
    private String planMarChokuRiekiInven;
    // 3月(計画)_直接利益(直送)
    @Required(fieldName = "3月(計画)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "3月(計画)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "3月(計画)_直接利益(直送)")
    private String planMarChokuRiekiChokusou;
    // 3月(計画)_直接利益計
    //@Required(fieldName = "3月(計画)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "3月(計画)_直接利益計")
    //@NumericHalfWidth(fieldName = "3月(計画)_直接利益計")
    //private String planMarChokuRiekiTotal;
    // 3月(計画)_直利率(在庫)
    //@Required(fieldName = "3月(計画)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "3月(計画)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "3月(計画)_直利率(在庫)")
    //private String planMarChokuRateInven;
    // 3月(計画)_直利率(直送)
    //@Required(fieldName = "3月(計画)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "3月(計画)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "3月(計画)_直利率(直送)")
    //private String planMarChokuRateChokusou;
    // 3月(計画)_直利率計
    //@Required(fieldName = "3月(計画)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "3月(計画)_直利率計")
    //@NumericHalfWidth(fieldName = "3月(計画)_直利率計")
    //private String planMarChokuRateTotal;



    // 4月(実績見通し)_総売上高(在庫)
    private String outlookAprAllSalInven;
    // 4月(実績見通し)_総売上高(直送)
    private String outlookAprAllSalChokusou;
    // 4月(実績見通し)_総売上高計
    //private String outlookAprAllSalTotal;
    // 4月(実績見通し)_返品(在庫)
    private String outlookAprReturnInven;
    // 4月(実績見通し)_返品(直送)
    private String outlookAprReturnChokusou;
    // 4月(実績見通し)_返品計
    //private String outlookAprReturnTotal;
    // 4月(実績見通し)_リベート(在庫)
    private String outlookAprRebateInven;
    // 4月(実績見通し)_リベート(直送)
    private String outlookAprRebateChokusou;
    // 4月(実績見通し)_リベート計
    //private String outlookAprRebateTotal;
    // 4月(実績見通し)_センターフィ(在庫)
    private String outlookAprCenterFeeInven;
    // 4月(実績見通し)_センターフィ(直送)
    private String outlookAprCenterFeeChokusou;
    // 4月(実績見通し)_センターフィ計
    //private String outlookAprCenterFeeTotal;
    // 4月(実績見通し)_直接利益(在庫)
    private String outlookAprChokuRiekiInven;
    // 4月(実績見通し)_直接利益(直送)
    private String outlookAprChokuRiekiChokusou;
    // 4月(実績見通し)_直接利益計
    //private String outlookAprChokuRiekiTotal;
    // 4月(実績見通し)_直利率(在庫)
    //private String outlookAprChokuRateInven;
    // 4月(実績見通し)_直利率(直送)
    //private String outlookAprChokuRateChokusou;
    // 4月(実績見通し)_直利率計
    //private String outlookAprChokuRateTotal;

    // 5月(実績見通し)_総売上高(在庫)
    private String outlookMayAllSalInven;
    // 5月(実績見通し)_総売上高(直送)
    private String outlookMayAllSalChokusou;
    // 5月(実績見通し)_総売上高計
    //private String outlookMayAllSalTotal;
    // 5月(実績見通し)_返品(在庫)
    private String outlookMayReturnInven;
    // 5月(実績見通し)_返品(直送)
    private String outlookMayReturnChokusou;
    // 5月(実績見通し)_返品計
    //private String outlookMayReturnTotal;
    // 5月(実績見通し)_リベート(在庫)
    private String outlookMayRebateInven;
    // 5月(実績見通し)_リベート(直送)
    private String outlookMayRebateChokusou;
    // 5月(実績見通し)_リベート計
    //private String outlookMayRebateTotal;
    // 5月(実績見通し)_センターフィ(在庫)
    private String outlookMayCenterFeeInven;
    // 5月(実績見通し)_センターフィ(直送)
    private String outlookMayCenterFeeChokusou;
    // 5月(実績見通し)_センターフィ計
    //private String outlookMayCenterFeeTotal;
    // 5月(実績見通し)_直接利益(在庫)
    private String outlookMayChokuRiekiInven;
    // 5月(実績見通し)_直接利益(直送)
    private String outlookMayChokuRiekiChokusou;
    // 5月(実績見通し)_直接利益計
    //private String outlookMayChokuRiekiTotal;
    // 5月(実績見通し)_直利率(在庫)
    //private String outlookMayChokuRateInven;
    // 5月(実績見通し)_直利率(直送)
    //private String outlookMayChokuRateChokusou;
    // 5月(実績見通し)_直利率計
    //private String outlookMayChokuRateTotal;

    // 6月(実績見通し)_総売上高(在庫)
    private String outlookJunAllSalInven;
    // 6月(実績見通し)_総売上高(直送)
    private String outlookJunAllSalChokusou;
    // 6月(実績見通し)_総売上高計
    //private String outlookJunAllSalTotal;
    // 6月(実績見通し)_返品(在庫)
    private String outlookJunReturnInven;
    // 6月(実績見通し)_返品(直送)
    private String outlookJunReturnChokusou;
    // 6月(実績見通し)_返品計
    //private String outlookJunReturnTotal;
    // 6月(実績見通し)_リベート(在庫)
    private String outlookJunRebateInven;
    // 6月(実績見通し)_リベート(直送)
    private String outlookJunRebateChokusou;
    // 6月(実績見通し)_リベート計
    //private String outlookJunRebateTotal;
    // 6月(実績見通し)_センターフィ(在庫)
    private String outlookJunCenterFeeInven;
    // 6月(実績見通し)_センターフィ(直送)
    private String outlookJunCenterFeeChokusou;
    // 6月(実績見通し)_センターフィ計
    //private String outlookJunCenterFeeTotal;
    // 6月(実績見通し)_直接利益(在庫)
    private String outlookJunChokuRiekiInven;
    // 6月(実績見通し)_直接利益(直送)
    private String outlookJunChokuRiekiChokusou;
    // 6月(実績見通し)_直接利益計
    //private String outlookJunChokuRiekiTotal;
    // 6月(実績見通し)_直利率(在庫)
    //private String outlookJunChokuRateInven;
    // 6月(実績見通し)_直利率(直送)
    //private String outlookJunChokuRateChokusou;
    // 6月(実績見通し)_直利率計
    //private String outlookJunChokuRateTotal;

    // 7月(実績見通し)_総売上高(在庫)
    private String outlookJulAllSalInven;
    // 7月(実績見通し)_総売上高(直送)
    private String outlookJulAllSalChokusou;
    // 7月(実績見通し)_総売上高計
    //private String outlookJulAllSalTotal;
    // 7月(実績見通し)_返品(在庫)
    private String outlookJulReturnInven;
    // 7月(実績見通し)_返品(直送)
    private String outlookJulReturnChokusou;
    // 7月(実績見通し)_返品計
    //private String outlookJulReturnTotal;
    // 7月(実績見通し)_リベート(在庫)
    private String outlookJulRebateInven;
    // 7月(実績見通し)_リベート(直送)
    private String outlookJulRebateChokusou;
    // 7月(実績見通し)_リベート計
    //private String outlookJulRebateTotal;
    // 7月(実績見通し)_センターフィ(在庫)
    private String outlookJulCenterFeeInven;
    // 7月(実績見通し)_センターフィ(直送)
    private String outlookJulCenterFeeChokusou;
    // 7月(実績見通し)_センターフィ計
    //private String outlookJulCenterFeeTotal;
    // 7月(実績見通し)_直接利益(在庫)
    private String outlookJulChokuRiekiInven;
    // 7月(実績見通し)_直接利益(直送)
    private String outlookJulChokuRiekiChokusou;
    // 7月(実績見通し)_直接利益計
    //private String outlookJulChokuRiekiTotal;
    // 7月(実績見通し)_直利率(在庫)
    //private String outlookJulChokuRateInven;
    // 7月(実績見通し)_直利率(直送)
    //private String outlookJulChokuRateChokusou;
    // 7月(実績見通し)_直利率計
    //private String outlookJulChokuRateTotal;

    // 8月(実績見通し)_総売上高(在庫)
    private String outlookAugAllSalInven;
    // 8月(実績見通し)_総売上高(直送)
    private String outlookAugAllSalChokusou;
    // 8月(実績見通し)_総売上高計
    //private String outlookAugAllSalTotal;
    // 8月(実績見通し)_返品(在庫)
    private String outlookAugReturnInven;
    // 8月(実績見通し)_返品(直送)
    private String outlookAugReturnChokusou;
    // 8月(実績見通し)_返品計
    //private String outlookAugReturnTotal;
    // 8月(実績見通し)_リベート(在庫)
    private String outlookAugRebateInven;
    // 8月(実績見通し)_リベート(直送)
    private String outlookAugRebateChokusou;
    // 8月(実績見通し)_リベート計
    //private String outlookAugRebateTotal;
    // 8月(実績見通し)_センターフィ(在庫)
    private String outlookAugCenterFeeInven;
    // 8月(実績見通し)_センターフィ(直送)
    private String outlookAugCenterFeeChokusou;
    // 8月(実績見通し)_センターフィ計
    //private String outlookAugCenterFeeTotal;
    // 8月(実績見通し)_直接利益(在庫)
    private String outlookAugChokuRiekiInven;
    // 8月(実績見通し)_直接利益(直送)
    private String outlookAugChokuRiekiChokusou;
    // 8月(実績見通し)_直接利益計
    //private String outlookAugChokuRiekiTotal;
    // 8月(実績見通し)_直利率(在庫)
    //private String outlookAugChokuRateInven;
    // 8月(実績見通し)_直利率(直送)
    //private String outlookAugChokuRateChokusou;
    // 8月(実績見通し)_直利率計
    //private String outlookAugChokuRateTotal;

    // 9月(実績見通し)_総売上高(在庫)
    private String outlookSepAllSalInven;
    // 9月(実績見通し)_総売上高(直送)
    private String outlookSepAllSalChokusou;
    // 9月(実績見通し)_総売上高計
    //private String outlookSepAllSalTotal;
    // 9月(実績見通し)_返品(在庫)
    private String outlookSepReturnInven;
    // 9月(実績見通し)_返品(直送)
    private String outlookSepReturnChokusou;
    // 9月(実績見通し)_返品計
    //private String outlookSepReturnTotal;
    // 9月(実績見通し)_リベート(在庫)
    private String outlookSepRebateInven;
    // 9月(実績見通し)_リベート(直送)
    private String outlookSepRebateChokusou;
    // 9月(実績見通し)_リベート計
    //private String outlookSepRebateTotal;
    // 9月(実績見通し)_センターフィ(在庫)
    private String outlookSepCenterFeeInven;
    // 9月(実績見通し)_センターフィ(直送)
    private String outlookSepCenterFeeChokusou;
    // 9月(実績見通し)_センターフィ計
    //private String outlookSepCenterFeeTotal;
    // 9月(実績見通し)_直接利益(在庫)
    private String outlookSepChokuRiekiInven;
    // 9月(実績見通し)_直接利益(直送)
    private String outlookSepChokuRiekiChokusou;
    // 9月(実績見通し)_直接利益計
    //private String outlookSepChokuRiekiTotal;
    // 9月(実績見通し)_直利率(在庫)
    //private String outlookSepChokuRateInven;
    // 9月(実績見通し)_直利率(直送)
    //private String outlookSepChokuRateChokusou;
    // 9月(実績見通し)_直利率計
    //private String outlookSepChokuRateTotal;

    // 10月(実績見通し)_総売上高(在庫)
    private String outlookOctAllSalInven;
    // 10月(実績見通し)_総売上高(直送)
    private String outlookOctAllSalChokusou;
    // 10月(実績見通し)_総売上高計
    //private String outlookOctAllSalTotal;
    // 10月(実績見通し)_返品(在庫)
    private String outlookOctReturnInven;
    // 10月(実績見通し)_返品(直送)
    private String outlookOctReturnChokusou;
    // 10月(実績見通し)_返品計
    //private String outlookOctReturnTotal;
    // 10月(実績見通し)_リベート(在庫)
    private String outlookOctRebateInven;
    // 10月(実績見通し)_リベート(直送)
    private String outlookOctRebateChokusou;
    // 10月(実績見通し)_リベート計
    //private String outlookOctRebateTotal;
    // 10月(実績見通し)_センターフィ(在庫)
    private String outlookOctCenterFeeInven;
    // 10月(実績見通し)_センターフィ(直送)
    private String outlookOctCenterFeeChokusou;
    // 10月(実績見通し)_センターフィ計
    //private String outlookOctCenterFeeTotal;
    // 10月(実績見通し)_直接利益(在庫)
    private String outlookOctChokuRiekiInven;
    // 10月(実績見通し)_直接利益(直送)
    private String outlookOctChokuRiekiChokusou;
    // 10月(実績見通し)_直接利益計
    //private String outlookOctChokuRiekiTotal;
    // 10月(実績見通し)_直利率(在庫)
    //private String outlookOctChokuRateInven;
    // 10月(実績見通し)_直利率(直送)
    //private String outlookOctChokuRateChokusou;
    // 10月(実績見通し)_直利率計
    //private String outlookOctChokuRateTotal;

    // 11月(実績見通し)_総売上高(在庫)
    private String outlookNovAllSalInven;
    // 11月(実績見通し)_総売上高(直送)
    private String outlookNovAllSalChokusou;
    // 11月(実績見通し)_総売上高計
    //private String outlookNovAllSalTotal;
    // 11月(実績見通し)_返品(在庫)
    private String outlookNovReturnInven;
    // 11月(実績見通し)_返品(直送)
    private String outlookNovReturnChokusou;
    // 11月(実績見通し)_返品計
    //private String outlookNovReturnTotal;
    // 11月(実績見通し)_リベート(在庫)
    private String outlookNovRebateInven;
    // 11月(実績見通し)_リベート(直送)
    private String outlookNovRebateChokusou;
    // 11月(実績見通し)_リベート計
    //private String outlookNovRebateTotal;
    // 11月(実績見通し)_センターフィ(在庫)
    private String outlookNovCenterFeeInven;
    // 11月(実績見通し)_センターフィ(直送)
    private String outlookNovCenterFeeChokusou;
    // 11月(実績見通し)_センターフィ計
    //private String outlookNovCenterFeeTotal;
    // 11月(実績見通し)_直接利益(在庫)
    private String outlookNovChokuRiekiInven;
    // 11月(実績見通し)_直接利益(直送)
    private String outlookNovChokuRiekiChokusou;
    // 11月(実績見通し)_直接利益計
    //private String outlookNovChokuRiekiTotal;
    // 11月(実績見通し)_直利率(在庫)
    //private String outlookNovChokuRateInven;
    // 11月(実績見通し)_直利率(直送)
    //private String outlookNovChokuRateChokusou;
    // 11月(実績見通し)_直利率計
    //private String outlookNovChokuRateTotal;

    // 12月(実績見通し)_総売上高(在庫)
    private String outlookDecAllSalInven;
    // 12月(実績見通し)_総売上高(直送)
    private String outlookDecAllSalChokusou;
    // 12月(実績見通し)_総売上高計
    //private String outlookDecAllSalTotal;
    // 12月(実績見通し)_返品(在庫)
    private String outlookDecReturnInven;
    // 12月(実績見通し)_返品(直送)
    private String outlookDecReturnChokusou;
    // 12月(実績見通し)_返品計
    //private String outlookDecReturnTotal;
    // 12月(実績見通し)_リベート(在庫)
    private String outlookDecRebateInven;
    // 12月(実績見通し)_リベート(直送)
    private String outlookDecRebateChokusou;
    // 12月(実績見通し)_リベート計
    //private String outlookDecRebateTotal;
    // 12月(実績見通し)_センターフィ(在庫)
    private String outlookDecCenterFeeInven;
    // 12月(実績見通し)_センターフィ(直送)
    private String outlookDecCenterFeeChokusou;
    // 12月(実績見通し)_センターフィ計
    //private String outlookDecCenterFeeTotal;
    // 12月(実績見通し)_直接利益(在庫)
    private String outlookDecChokuRiekiInven;
    // 12月(実績見通し)_直接利益(直送)
    private String outlookDecChokuRiekiChokusou;
    // 12月(実績見通し)_直接利益計
    //private String outlookDecChokuRiekiTotal;
    // 12月(実績見通し)_直利率(在庫)
    //private String outlookDecChokuRateInven;
    // 12月(実績見通し)_直利率(直送)
    //private String outlookDecChokuRateChokusou;
    // 12月(実績見通し)_直利率計
    //private String outlookDecChokuRateTotal;


    // 1月(実績見通し)_総売上高(在庫)
    @Required(fieldName = "1月(実績見通し)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "1月(実績見通し)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "1月(実績見通し)_総売上高(在庫)")
    private String outlookJanAllSalInven;
    // 1月(実績見通し)_総売上高(直送)
    @Required(fieldName = "1月(実績見通し)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "1月(実績見通し)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "1月(実績見通し)_総売上高(直送)")
    private String outlookJanAllSalChokusou;
    // 1月(実績見通し)_総売上高計
    //@Required(fieldName = "1月(実績見通し)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "1月(実績見通し)_総売上高計")
    //@NumericHalfWidth(fieldName = "1月(実績見通し)_総売上高計")
    //private String outlookJanAllSalTotal;
    // 1月(実績見通し)_返品(在庫)
    @Required(fieldName = "1月(実績見通し)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "1月(実績見通し)_返品(在庫)")
    @NumericHalfWidth(fieldName = "1月(実績見通し)_返品(在庫)")
    private String outlookJanReturnInven;
    // 1月(実績見通し)_返品(直送)
    @Required(fieldName = "1月(実績見通し)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "1月(実績見通し)_返品(直送)")
    @NumericHalfWidth(fieldName = "1月(実績見通し)_返品(直送)")
    private String outlookJanReturnChokusou;
    // 1月(実績見通し)_返品計
    //@Required(fieldName = "1月(実績見通し)_返品計")
    //@Range(min = 1, max = 10, fieldName = "1月(実績見通し)_返品計")
    //@NumericHalfWidth(fieldName = "1月(実績見通し)_返品計")
    //private String outlookJanReturnTotal;
    // 1月(実績見通し)_リベート(在庫)
    @Required(fieldName = "1月(実績見通し)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "1月(実績見通し)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "1月(実績見通し)_リベート(在庫)")
    private String outlookJanRebateInven;
    // 1月(実績見通し)_リベート(直送)
    @Required(fieldName = "1月(実績見通し)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "1月(実績見通し)_リベート(直送)")
    @NumericHalfWidth(fieldName = "1月(実績見通し)_リベート(直送)")
    private String outlookJanRebateChokusou;
    // 1月(実績見通し)_リベート計
    //@Required(fieldName = "1月(実績見通し)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "1月(実績見通し)_リベート計")
    //@NumericHalfWidth(fieldName = "1月(実績見通し)_リベート計")
    //private String outlookJanRebateTotal;
    // 1月(実績見通し)_センターフィ(在庫)
    @Required(fieldName = "1月(実績見通し)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "1月(実績見通し)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "1月(実績見通し)_センターフィ(在庫)")
    private String outlookJanCenterFeeInven;
    // 1月(実績見通し)_センターフィ(直送)
    @Required(fieldName = "1月(実績見通し)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "1月(実績見通し)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "1月(実績見通し)_センターフィ(直送)")
    private String outlookJanCenterFeeChokusou;
    // 1月(実績見通し)_センターフィ計
    //@Required(fieldName = "1月(実績見通し)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "1月(実績見通し)_センターフィ計")
    //@NumericHalfWidth(fieldName = "1月(実績見通し)_センターフィ計")
    //private String outlookJanCenterFeeTotal;
    // 1月(実績見通し)_直接利益(在庫)
    @Required(fieldName = "1月(実績見通し)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "1月(実績見通し)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "1月(実績見通し)_直接利益(在庫)")
    private String outlookJanChokuRiekiInven;
    // 1月(実績見通し)_直接利益(直送)
    @Required(fieldName = "1月(実績見通し)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "1月(実績見通し)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "1月(実績見通し)_直接利益(直送)")
    private String outlookJanChokuRiekiChokusou;
    // 1月(実績見通し)_直接利益計
    //@Required(fieldName = "1月(実績見通し)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "1月(実績見通し)_直接利益計")
    //@NumericHalfWidth(fieldName = "1月(実績見通し)_直接利益計")
    //private String outlookJanChokuRiekiTotal;
    // 1月(実績見通し)_直利率(在庫)
    //@Required(fieldName = "1月(実績見通し)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "1月(実績見通し)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "1月(実績見通し)_直利率(在庫)")
    //private String outlookJanChokuRateInven;
    // 1月(実績見通し)_直利率(直送)
    //@Required(fieldName = "1月(実績見通し)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "1月(実績見通し)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "1月(実績見通し)_直利率(直送)")
    //private String outlookJanChokuRateChokusou;
    // 1月(実績見通し)_直利率計
    //@Required(fieldName = "1月(実績見通し)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "1月(実績見通し)_直利率計")
    //@NumericHalfWidth(fieldName = "1月(実績見通し)_直利率計")
    //private String outlookJanChokuRateTotal;
    // 2月(実績見通し)_総売上高(在庫)
    @Required(fieldName = "2月(実績見通し)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "2月(実績見通し)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "2月(実績見通し)_総売上高(在庫)")
    private String outlookFebAllSalInven;
    // 2月(実績見通し)_総売上高(直送)
    @Required(fieldName = "2月(実績見通し)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "2月(実績見通し)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "2月(実績見通し)_総売上高(直送)")
    private String outlookFebAllSalChokusou;
    // 2月(実績見通し)_総売上高計
    //@Required(fieldName = "2月(実績見通し)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "2月(実績見通し)_総売上高計")
    //@NumericHalfWidth(fieldName = "2月(実績見通し)_総売上高計")
    //private String outlookFebAllSalTotal;
    // 2月(実績見通し)_返品(在庫)
    @Required(fieldName = "2月(実績見通し)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "2月(実績見通し)_返品(在庫)")
    @NumericHalfWidth(fieldName = "2月(実績見通し)_返品(在庫)")
    private String outlookFebReturnInven;
    // 2月(実績見通し)_返品(直送)
    @Required(fieldName = "2月(実績見通し)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "2月(実績見通し)_返品(直送)")
    @NumericHalfWidth(fieldName = "2月(実績見通し)_返品(直送)")
    private String outlookFebReturnChokusou;
    // 2月(実績見通し)_返品計
    //@Required(fieldName = "2月(実績見通し)_返品計")
    //@Range(min = 1, max = 10, fieldName = "2月(実績見通し)_返品計")
    //@NumericHalfWidth(fieldName = "2月(実績見通し)_返品計")
    //private String outlookFebReturnTotal;
    // 2月(実績見通し)_リベート(在庫)
    @Required(fieldName = "2月(実績見通し)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "2月(実績見通し)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "2月(実績見通し)_リベート(在庫)")
    private String outlookFebRebateInven;
    // 2月(実績見通し)_リベート(直送)
    @Required(fieldName = "2月(実績見通し)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "2月(実績見通し)_リベート(直送)")
    @NumericHalfWidth(fieldName = "2月(実績見通し)_リベート(直送)")
    private String outlookFebRebateChokusou;
    // 2月(実績見通し)_リベート計
    //@Required(fieldName = "2月(実績見通し)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "2月(実績見通し)_リベート計")
    //@NumericHalfWidth(fieldName = "2月(実績見通し)_リベート計")
    //private String outlookFebRebateTotal;
    // 2月(実績見通し)_センターフィ(在庫)
    @Required(fieldName = "2月(実績見通し)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "2月(実績見通し)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "2月(実績見通し)_センターフィ(在庫)")
    private String outlookFebCenterFeeInven;
    // 2月(実績見通し)_センターフィ(直送)
    @Required(fieldName = "2月(実績見通し)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "2月(実績見通し)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "2月(実績見通し)_センターフィ(直送)")
    private String outlookFebCenterFeeChokusou;
    // 2月(実績見通し)_センターフィ計
    //@Required(fieldName = "2月(実績見通し)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "2月(実績見通し)_センターフィ計")
    //@NumericHalfWidth(fieldName = "2月(実績見通し)_センターフィ計")
    //private String outlookFebCenterFeeTotal;
    // 2月(実績見通し)_直接利益(在庫)
    @Required(fieldName = "2月(実績見通し)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "2月(実績見通し)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "2月(実績見通し)_直接利益(在庫)")
    private String outlookFebChokuRiekiInven;
    // 2月(実績見通し)_直接利益(直送)
    @Required(fieldName = "2月(実績見通し)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "2月(実績見通し)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "2月(実績見通し)_直接利益(直送)")
    private String outlookFebChokuRiekiChokusou;
    // 2月(実績見通し)_直接利益計
    //@Required(fieldName = "2月(実績見通し)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "2月(実績見通し)_直接利益計")
    //@NumericHalfWidth(fieldName = "2月(実績見通し)_直接利益計")
    //private String outlookFebChokuRiekiTotal;
    // 2月(実績見通し)_直利率(在庫)
    //@Required(fieldName = "2月(実績見通し)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "2月(実績見通し)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "2月(実績見通し)_直利率(在庫)")
    //private String outlookFebChokuRateInven;
    // 2月(実績見通し)_直利率(直送)
    //@Required(fieldName = "2月(実績見通し)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "2月(実績見通し)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "2月(実績見通し)_直利率(直送)")
    //private String outlookFebChokuRateChokusou;
    // 2月(実績見通し)_直利率計
    //@Required(fieldName = "2月(実績見通し)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "2月(実績見通し)_直利率計")
    //@NumericHalfWidth(fieldName = "2月(実績見通し)_直利率計")
    //private String outlookFebChokuRateTotal;
    // 3月(実績見通し)_総売上高(在庫)
    @Required(fieldName = "3月(実績見通し)_総売上高(在庫)")
    @Range(min = 1, max = 10, fieldName = "3月(実績見通し)_総売上高(在庫)")
    @NumericHalfWidth(fieldName = "3月(実績見通し)_総売上高(在庫)")
    private String outlookMarAllSalInven;
    // 3月(実績見通し)_総売上高(直送)
    @Required(fieldName = "3月(実績見通し)_総売上高(直送)")
    @Range(min = 1, max = 10, fieldName = "3月(実績見通し)_総売上高(直送)")
    @NumericHalfWidth(fieldName = "3月(実績見通し)_総売上高(直送)")
    private String outlookMarAllSalChokusou;
    // 3月(実績見通し)_総売上高計
    //@Required(fieldName = "3月(実績見通し)_総売上高計")
    //@Range(min = 1, max = 10, fieldName = "3月(実績見通し)_総売上高計")
    //@NumericHalfWidth(fieldName = "3月(実績見通し)_総売上高計")
    //private String outlookMarAllSalTotal;
    // 3月(実績見通し)_返品(在庫)
    @Required(fieldName = "3月(実績見通し)_返品(在庫)")
    @Range(min = 1, max = 10, fieldName = "3月(実績見通し)_返品(在庫)")
    @NumericHalfWidth(fieldName = "3月(実績見通し)_返品(在庫)")
    private String outlookMarReturnInven;
    // 3月(実績見通し)_返品(直送)
    @Required(fieldName = "3月(実績見通し)_返品(直送)")
    @Range(min = 1, max = 10, fieldName = "3月(実績見通し)_返品(直送)")
    @NumericHalfWidth(fieldName = "3月(実績見通し)_返品(直送)")
    private String outlookMarReturnChokusou;
    // 3月(実績見通し)_返品計
    //@Required(fieldName = "3月(実績見通し)_返品計")
    //@Range(min = 1, max = 10, fieldName = "3月(実績見通し)_返品計")
    //@NumericHalfWidth(fieldName = "3月(実績見通し)_返品計")
    //private String outlookMarReturnTotal;
    // 3月(実績見通し)_リベート(在庫)
    @Required(fieldName = "3月(実績見通し)_リベート(在庫)")
    @Range(min = 1, max = 10, fieldName = "3月(実績見通し)_リベート(在庫)")
    @NumericHalfWidth(fieldName = "3月(実績見通し)_リベート(在庫)")
    private String outlookMarRebateInven;
    // 3月(実績見通し)_リベート(直送)
    @Required(fieldName = "3月(実績見通し)_リベート(直送)")
    @Range(min = 1, max = 10, fieldName = "3月(実績見通し)_リベート(直送)")
    @NumericHalfWidth(fieldName = "3月(実績見通し)_リベート(直送)")
    private String outlookMarRebateChokusou;
    // 3月(実績見通し)_リベート計
    //@Required(fieldName = "3月(実績見通し)_リベート計")
    //@Range(min = 1, max = 10, fieldName = "3月(実績見通し)_リベート計")
    //@NumericHalfWidth(fieldName = "3月(実績見通し)_リベート計")
    //private String outlookMarRebateTotal;
    // 3月(実績見通し)_センターフィ(在庫)
    @Required(fieldName = "3月(実績見通し)_センターフィ(在庫)")
    @Range(min = 1, max = 10, fieldName = "3月(実績見通し)_センターフィ(在庫)")
    @NumericHalfWidth(fieldName = "3月(実績見通し)_センターフィ(在庫)")
    private String outlookMarCenterFeeInven;
    // 3月(実績見通し)_センターフィ(直送)
    @Required(fieldName = "3月(実績見通し)_センターフィ(直送)")
    @Range(min = 1, max = 10, fieldName = "3月(実績見通し)_センターフィ(直送)")
    @NumericHalfWidth(fieldName = "3月(実績見通し)_センターフィ(直送)")
    private String outlookMarCenterFeeChokusou;
    // 3月(実績見通し)_センターフィ計
    //@Required(fieldName = "3月(実績見通し)_センターフィ計")
    //@Range(min = 1, max = 10, fieldName = "3月(実績見通し)_センターフィ計")
    //@NumericHalfWidth(fieldName = "3月(実績見通し)_センターフィ計")
    //private String outlookMarCenterFeeTotal;
    // 3月(実績見通し)_直接利益(在庫)
    @Required(fieldName = "3月(実績見通し)_直接利益(在庫)")
    @Range(min = 1, max = 10, fieldName = "3月(実績見通し)_直接利益(在庫)")
    @NumericHalfWidth(fieldName = "3月(実績見通し)_直接利益(在庫)")
    private String outlookMarChokuRiekiInven;
    // 3月(実績見通し)_直接利益(直送)
    @Required(fieldName = "3月(実績見通し)_直接利益(直送)")
    @Range(min = 1, max = 10, fieldName = "3月(実績見通し)_直接利益(直送)")
    @NumericHalfWidth(fieldName = "3月(実績見通し)_直接利益(直送)")
    private String outlookMarChokuRiekiChokusou;
    // 3月(実績見通し)_直接利益計
    //@Required(fieldName = "3月(実績見通し)_直接利益計")
    //@Range(min = 1, max = 10, fieldName = "3月(実績見通し)_直接利益計")
    //@NumericHalfWidth(fieldName = "3月(実績見通し)_直接利益計")
    //private String outlookMarChokuRiekiTotal;
    // 3月(実績見通し)_直利率(在庫)
    //@Required(fieldName = "3月(実績見通し)_直利率(在庫)")
    //@Range(min = 1, max = 10, fieldName = "3月(実績見通し)_直利率(在庫)")
    //@NumericHalfWidth(fieldName = "3月(実績見通し)_直利率(在庫)")
    //private String outlookMarChokuRateInven;
    // 3月(実績見通し)_直利率(直送)
    //@Required(fieldName = "3月(実績見通し)_直利率(直送)")
    //@Range(min = 1, max = 10, fieldName = "3月(実績見通し)_直利率(直送)")
    //@NumericHalfWidth(fieldName = "3月(実績見通し)_直利率(直送)")
    //private String outlookMarChokuRateChokusou;
    // 3月(実績見通し)_直利率計
    //@Required(fieldName = "3月(実績見通し)_直利率計")
    //@Range(min = 1, max = 10, fieldName = "3月(実績見通し)_直利率計")
    //@NumericHalfWidth(fieldName = "3月(実績見通し)_直利率計")
    //private String outlookMarChokuRateTotal;

    // ファイル情報1
    private String fileInfo1;
    private String fileInfo2;

    /**
     * データタイプ   1:次年度計画マスタ
     *             2:見通し・計画_採算管理単位C別 / 見通し・計画_企業別＜本社＞
     *             3:見通し・計画_採算管理単位C別 / 見通し・計画_企業別＜エリア＞
     *             4:間接利益計画_メーカー別
     */
    private String dataType;

    /**
     * エリアリスト   画面選択されたエリアのエリアコードリスト
     *              採算管理単位計画策定エリアが選択された場合は"SKSA"
     *              複数のエリアを指定可能
     */
    private List<AreaInfo> areaList;

    /**
     * 本社場所区分  0:本社
     *             1:場所
     */
    private String hnshBashoKubun;

    /**
     * データ区分リスト  0:移管前（当年度組織）
     *                1:移管後（次年度組織）
     *                複数のデータ区分を指定可能
     */
    private List<String> dataKubun;

    // ==================== 便利メソッド ====================
    /**
     * データ区分リストをカンマ区切り文字列として取得
     * @return カンマ区切りのデータ区分文字列
     */
    public String getDataKubunString() {
        if (dataKubun == null || dataKubun.isEmpty()) {
            return null;
        }
        return String.join(",", dataKubun);
    }

    @Override
    public Map<String, Object> toDatabaseFieldsCore(boolean isInsert) {
        Map<String, Object> fields = new HashMap<>();

        // 年度（次年度を自動設定）
        fields.put("NENDO", DateUtil.getNextFiscalYear());
        fields.put("GROUP_CODE", groupCode);
        fields.put("SSNKN_TNCD", saisanCode);

        // 基本フィールド
        fields.put("AREA_CODE", areaCode);
        fields.put("UNIT_CODE", unitCode);
        fields.put("TNTSH_MEI_KANJI", tantoshaName);
        fields.put("KIGYO_CODE", kigyoCode);
        fields.put("SUB_CTGRY_MEI_TNSHK_KANA", kategori);
        fields.put("SUB_CTGRY_MEI_TNSHK_KANJI", subKategori);
        fields.put("GYT_MEI", gyotai);
        fields.put("TRKM_KUBUN", afterTorikumKbn);
        //java.text.DecimalFormat df = new java.text.DecimalFormat("0.00");
        fields.put("GYT_HRTS", Float.parseFloat(gyotaiHiritsu));

        // 各種金額
        // 計画＿在庫売上＿１月目
        fields.put("KKK_ZAIKO_URG_1_TSKM", Long.parseLong(planAprAllSalInven) * 1000);
        // 計画＿直送売上＿１月目
        fields.put("KKK_CHKS_URG_1_TSKM", Long.parseLong(planAprAllSalChokusou) * 1000);
        // 計画＿在庫返品等＿１月目
        fields.put("KKK_ZAIKO_HMPNT_1_TSKM", Long.parseLong(planAprReturnInven) * 1000);
        // 計画＿直送返品等＿１月目
        fields.put("KKK_CHKS_HMPNT_1_TSKM", Long.parseLong(planAprReturnChokusou) * 1000);
        // 計画＿在庫支払リベート＿１月目
        fields.put("KKK_ZAIKO_SHHR_RBT_1_TSKM", Long.parseLong(planAprRebateInven) * 1000);
        // 計画＿直送支払リベート＿１月目
        fields.put("KKK_CHKS_SHHR_RBT_1_TSKM", Long.parseLong(planAprRebateChokusou) * 1000);
        // 計画＿在庫センターフィ＿１月目
        fields.put("KKK_ZAIKO_CNTR_FEE_1_TSKM", Long.parseLong(planAprCenterFeeInven) * 1000);
        // 計画＿直送センターフィ＿１月目
        fields.put("KKK_CHKS_CNTR_FEE_1_TSKM", Long.parseLong(planAprCenterFeeChokusou) * 1000);
        // 計画＿在庫利益＿１月目
        fields.put("KKK_ZAIKO_RIEKI_1_TSKM", Long.parseLong(planAprChokuRiekiInven) * 1000);
        // 計画＿直送利益＿１月目
        fields.put("KKK_CHKS_RIEKI_1_TSKM", Long.parseLong(planAprChokuRiekiChokusou) * 1000);
        // 計画＿在庫売上＿２月目
        fields.put("KKK_ZAIKO_URG_2_TSKM", Long.parseLong(planMayAllSalInven) * 1000);
        // 計画＿直送売上＿２月目
        fields.put("KKK_CHKS_URG_2_TSKM", Long.parseLong(planMayAllSalChokusou) * 1000);
        // 計画＿在庫返品等＿２月目
        fields.put("KKK_ZAIKO_HMPNT_2_TSKM", Long.parseLong(planMayReturnInven) * 1000);
        // 計画＿直送返品等＿２月目
        fields.put("KKK_CHKS_HMPNT_2_TSKM", Long.parseLong(planMayReturnChokusou) * 1000);
        // 計画＿在庫支払リベート＿２月目
        fields.put("KKK_ZAIKO_SHHR_RBT_2_TSKM", Long.parseLong(planMayRebateInven) * 1000);
        // 計画＿直送支払リベート＿２月目
        fields.put("KKK_CHKS_SHHR_RBT_2_TSKM", Long.parseLong(planMayRebateChokusou) * 1000);
        // 計画＿在庫センターフィ＿２月目
        fields.put("KKK_ZAIKO_CNTR_FEE_2_TSKM", Long.parseLong(planMayCenterFeeInven) * 1000);
        // 計画＿直送センターフィ＿２月目
        fields.put("KKK_CHKS_CNTR_FEE_2_TSKM", Long.parseLong(planMayCenterFeeChokusou) * 1000);
        // 計画＿在庫利益＿２月目
        fields.put("KKK_ZAIKO_RIEKI_2_TSKM", Long.parseLong(planMayChokuRiekiInven) * 1000);
        // 計画＿直送利益＿２月目
        fields.put("KKK_CHKS_RIEKI_2_TSKM", Long.parseLong(planMayChokuRiekiChokusou) * 1000);
        // 計画＿在庫売上＿３月目
        fields.put("KKK_ZAIKO_URG_3_TSKM", Long.parseLong(planJunAllSalInven) * 1000);
        // 計画＿直送売上＿３月目
        fields.put("KKK_CHKS_URG_3_TSKM", Long.parseLong(planJunAllSalChokusou) * 1000);
        // 計画＿在庫返品等＿３月目
        fields.put("KKK_ZAIKO_HMPNT_3_TSKM", Long.parseLong(planJunReturnInven) * 1000);
        // 計画＿直送返品等＿３月目
        fields.put("KKK_CHKS_HMPNT_3_TSKM", Long.parseLong(planJunReturnChokusou) * 1000);
        // 計画＿在庫支払リベート＿３月目
        fields.put("KKK_ZAIKO_SHHR_RBT_3_TSKM", Long.parseLong(planJunRebateInven) * 1000);
        // 計画＿直送支払リベート＿３月目
        fields.put("KKK_CHKS_SHHR_RBT_3_TSKM", Long.parseLong(planJunRebateChokusou) * 1000);
        // 計画＿在庫センターフィ＿３月目
        fields.put("KKK_ZAIKO_CNTR_FEE_3_TSKM", Long.parseLong(planJunCenterFeeInven) * 1000);
        // 計画＿直送センターフィ＿３月目
        fields.put("KKK_CHKS_CNTR_FEE_3_TSKM", Long.parseLong(planJunCenterFeeChokusou) * 1000);
        // 計画＿在庫利益＿３月目
        fields.put("KKK_ZAIKO_RIEKI_3_TSKM", Long.parseLong(planJunChokuRiekiInven) * 1000);
        // 計画＿直送利益＿３月目
        fields.put("KKK_CHKS_RIEKI_3_TSKM", Long.parseLong(planJunChokuRiekiChokusou) * 1000);
        // 計画＿在庫売上＿４月目
        fields.put("KKK_ZAIKO_URG_4_TSKM", Long.parseLong(planJulAllSalInven) * 1000);
        // 計画＿直送売上＿４月目
        fields.put("KKK_CHKS_URG_4_TSKM", Long.parseLong(planJulAllSalChokusou) * 1000);
        // 計画＿在庫返品等＿４月目
        fields.put("KKK_ZAIKO_HMPNT_4_TSKM", Long.parseLong(planJulReturnInven) * 1000);
        // 計画＿直送返品等＿４月目
        fields.put("KKK_CHKS_HMPNT_4_TSKM", Long.parseLong(planJulReturnChokusou) * 1000);
        // 計画＿在庫支払リベート＿４月目
        fields.put("KKK_ZAIKO_SHHR_RBT_4_TSKM", Long.parseLong(planJulRebateInven) * 1000);
        // 計画＿直送支払リベート＿４月目
        fields.put("KKK_CHKS_SHHR_RBT_4_TSKM", Long.parseLong(planJulRebateChokusou) * 1000);
        // 計画＿在庫センターフィ＿４月目
        fields.put("KKK_ZAIKO_CNTR_FEE_4_TSKM", Long.parseLong(planJulCenterFeeInven) * 1000);
        // 計画＿直送センターフィ＿４月目
        fields.put("KKK_CHKS_CNTR_FEE_4_TSKM", Long.parseLong(planJulCenterFeeChokusou) * 1000);
        // 計画＿在庫利益＿４月目
        fields.put("KKK_ZAIKO_RIEKI_4_TSKM", Long.parseLong(planJulChokuRiekiInven) * 1000);
        // 計画＿直送利益＿４月目
        fields.put("KKK_CHKS_RIEKI_4_TSKM", Long.parseLong(planJulChokuRiekiChokusou) * 1000);
        // 計画＿在庫売上＿５月目
        fields.put("KKK_ZAIKO_URG_5_TSKM", Long.parseLong(planAugAllSalInven) * 1000);
        // 計画＿直送売上＿５月目
        fields.put("KKK_CHKS_URG_5_TSKM", Long.parseLong(planAugAllSalChokusou) * 1000);
        // 計画＿在庫返品等＿５月目
        fields.put("KKK_ZAIKO_HMPNT_5_TSKM", Long.parseLong(planAugReturnInven) * 1000);
        // 計画＿直送返品等＿５月目
        fields.put("KKK_CHKS_HMPNT_5_TSKM", Long.parseLong(planAugReturnChokusou) * 1000);
        // 計画＿在庫支払リベート＿５月目
        fields.put("KKK_ZAIKO_SHHR_RBT_5_TSKM", Long.parseLong(planAugRebateInven) * 1000);
        // 計画＿直送支払リベート＿５月目
        fields.put("KKK_CHKS_SHHR_RBT_5_TSKM", Long.parseLong(planAugRebateChokusou) * 1000);
        // 計画＿在庫センターフィ＿５月目
        fields.put("KKK_ZAIKO_CNTR_FEE_5_TSKM", Long.parseLong(planAugCenterFeeInven) * 1000);
        // 計画＿直送センターフィ＿５月目
        fields.put("KKK_CHKS_CNTR_FEE_5_TSKM", Long.parseLong(planAugCenterFeeChokusou) * 1000);
        // 計画＿在庫利益＿５月目
        fields.put("KKK_ZAIKO_RIEKI_5_TSKM", Long.parseLong(planAugChokuRiekiInven) * 1000);
        // 計画＿直送利益＿５月目
        fields.put("KKK_CHKS_RIEKI_5_TSKM", Long.parseLong(planAugChokuRiekiChokusou) * 1000);
        // 計画＿在庫売上＿６月目
        fields.put("KKK_ZAIKO_URG_6_TSKM", Long.parseLong(planSepAllSalInven) * 1000);
        // 計画＿直送売上＿６月目
        fields.put("KKK_CHKS_URG_6_TSKM", Long.parseLong(planSepAllSalChokusou) * 1000);
        // 計画＿在庫返品等＿６月目
        fields.put("KKK_ZAIKO_HMPNT_6_TSKM", Long.parseLong(planSepReturnInven) * 1000);
        // 計画＿直送返品等＿６月目
        fields.put("KKK_CHKS_HMPNT_6_TSKM", Long.parseLong(planSepReturnChokusou) * 1000);
        // 計画＿在庫支払リベート＿６月目
        fields.put("KKK_ZAIKO_SHHR_RBT_6_TSKM", Long.parseLong(planSepRebateInven) * 1000);
        // 計画＿直送支払リベート＿６月目
        fields.put("KKK_CHKS_SHHR_RBT_6_TSKM", Long.parseLong(planSepRebateChokusou) * 1000);
        // 計画＿在庫センターフィ＿６月目
        fields.put("KKK_ZAIKO_CNTR_FEE_6_TSKM", Long.parseLong(planSepCenterFeeInven) * 1000);
        // 計画＿直送センターフィ＿６月目
        fields.put("KKK_CHKS_CNTR_FEE_6_TSKM", Long.parseLong(planSepCenterFeeChokusou) * 1000);
        // 計画＿在庫利益＿６月目
        fields.put("KKK_ZAIKO_RIEKI_6_TSKM", Long.parseLong(planSepChokuRiekiInven) * 1000);
        // 計画＿直送利益＿６月目
        fields.put("KKK_CHKS_RIEKI_6_TSKM", Long.parseLong(planSepChokuRiekiChokusou) * 1000);
        // 計画＿在庫売上＿７月目
        fields.put("KKK_ZAIKO_URG_7_TSKM", Long.parseLong(planOctAllSalInven) * 1000);
        // 計画＿直送売上＿７月目
        fields.put("KKK_CHKS_URG_7_TSKM", Long.parseLong(planOctAllSalChokusou) * 1000);
        // 計画＿在庫返品等＿７月目
        fields.put("KKK_ZAIKO_HMPNT_7_TSKM", Long.parseLong(planOctReturnInven) * 1000);
        // 計画＿直送返品等＿７月目
        fields.put("KKK_CHKS_HMPNT_7_TSKM", Long.parseLong(planOctReturnChokusou) * 1000);
        // 計画＿在庫支払リベート＿７月目
        fields.put("KKK_ZAIKO_SHHR_RBT_7_TSKM", Long.parseLong(planOctRebateInven) * 1000);
        // 計画＿直送支払リベート＿７月目
        fields.put("KKK_CHKS_SHHR_RBT_7_TSKM", Long.parseLong(planOctRebateChokusou) * 1000);
        // 計画＿在庫センターフィ＿７月目
        fields.put("KKK_ZAIKO_CNTR_FEE_7_TSKM", Long.parseLong(planOctCenterFeeInven) * 1000);
        // 計画＿直送センターフィ＿７月目
        fields.put("KKK_CHKS_CNTR_FEE_7_TSKM", Long.parseLong(planOctCenterFeeChokusou) * 1000);
        // 計画＿在庫利益＿７月目
        fields.put("KKK_ZAIKO_RIEKI_7_TSKM", Long.parseLong(planOctChokuRiekiInven) * 1000);
        // 計画＿直送利益＿７月目
        fields.put("KKK_CHKS_RIEKI_7_TSKM", Long.parseLong(planOctChokuRiekiChokusou) * 1000);
        // 計画＿在庫売上＿８月目
        fields.put("KKK_ZAIKO_URG_8_TSKM", Long.parseLong(planNovAllSalInven) * 1000);
        // 計画＿直送売上＿８月目
        fields.put("KKK_CHKS_URG_8_TSKM", Long.parseLong(planNovAllSalChokusou) * 1000);
        // 計画＿在庫返品等＿８月目
        fields.put("KKK_ZAIKO_HMPNT_8_TSKM", Long.parseLong(planNovReturnInven) * 1000);
        // 計画＿直送返品等＿８月目
        fields.put("KKK_CHKS_HMPNT_8_TSKM", Long.parseLong(planNovReturnChokusou) * 1000);
        // 計画＿在庫支払リベート＿８月目
        fields.put("KKK_ZAIKO_SHHR_RBT_8_TSKM", Long.parseLong(planNovRebateInven) * 1000);
        // 計画＿直送支払リベート＿８月目
        fields.put("KKK_CHKS_SHHR_RBT_8_TSKM", Long.parseLong(planNovRebateChokusou) * 1000);
        // 計画＿在庫センターフィ＿８月目
        fields.put("KKK_ZAIKO_CNTR_FEE_8_TSKM", Long.parseLong(planNovCenterFeeInven) * 1000);
        // 計画＿直送センターフィ＿８月目
        fields.put("KKK_CHKS_CNTR_FEE_8_TSKM", Long.parseLong(planNovCenterFeeChokusou) * 1000);
        // 計画＿在庫利益＿８月目
        fields.put("KKK_ZAIKO_RIEKI_8_TSKM", Long.parseLong(planNovChokuRiekiInven) * 1000);
        // 計画＿直送利益＿８月目
        fields.put("KKK_CHKS_RIEKI_8_TSKM", Long.parseLong(planNovChokuRiekiChokusou) * 1000);
        // 計画＿在庫売上＿９月目
        fields.put("KKK_ZAIKO_URG_9_TSKM", Long.parseLong(planDecAllSalInven) * 1000);
        // 計画＿直送売上＿９月目
        fields.put("KKK_CHKS_URG_9_TSKM", Long.parseLong(planDecAllSalChokusou) * 1000);
        // 計画＿在庫返品等＿９月目
        fields.put("KKK_ZAIKO_HMPNT_9_TSKM", Long.parseLong(planDecReturnInven) * 1000);
        // 計画＿直送返品等＿９月目
        fields.put("KKK_CHKS_HMPNT_9_TSKM", Long.parseLong(planDecReturnChokusou) * 1000);
        // 計画＿在庫支払リベート＿９月目
        fields.put("KKK_ZAIKO_SHHR_RBT_9_TSKM", Long.parseLong(planDecRebateInven) * 1000);
        // 計画＿直送支払リベート＿９月目
        fields.put("KKK_CHKS_SHHR_RBT_9_TSKM", Long.parseLong(planDecRebateChokusou) * 1000);
        // 計画＿在庫センターフィ＿９月目
        fields.put("KKK_ZAIKO_CNTR_FEE_9_TSKM", Long.parseLong(planDecCenterFeeInven) * 1000);
        // 計画＿直送センターフィ＿９月目
        fields.put("KKK_CHKS_CNTR_FEE_9_TSKM", Long.parseLong(planDecCenterFeeChokusou) * 1000);
        // 計画＿在庫利益＿９月目
        fields.put("KKK_ZAIKO_RIEKI_9_TSKM", Long.parseLong(planDecChokuRiekiInven) * 1000);
        // 計画＿直送利益＿９月目
        fields.put("KKK_CHKS_RIEKI_9_TSKM", Long.parseLong(planDecChokuRiekiChokusou) * 1000);
        // 計画＿在庫売上＿１０月目
        fields.put("KKK_ZAIKO_URG_10_TSKM", Long.parseLong(planJanAllSalInven) * 1000);
        // 計画＿直送売上＿１０月目
        fields.put("KKK_CHKS_URG_10_TSKM", Long.parseLong(planJanAllSalChokusou) * 1000);
        // 計画＿在庫返品等＿１０月目
        fields.put("KKK_ZAIKO_HMPNT_10_TSKM", Long.parseLong(planJanReturnInven) * 1000);
        // 計画＿直送返品等＿１０月目
        fields.put("KKK_CHKS_HMPNT_10_TSKM", Long.parseLong(planJanReturnChokusou) * 1000);
        // 計画＿在庫支払リベート＿１０月目
        fields.put("KKK_ZAIKO_SHHR_RBT_10_TSKM", Long.parseLong(planJanRebateInven) * 1000);
        // 計画＿直送支払リベート＿１０月目
        fields.put("KKK_CHKS_SHHR_RBT_10_TSKM", Long.parseLong(planJanRebateChokusou) * 1000);
        // 計画＿在庫センターフィ＿１０月目
        fields.put("KKK_ZAIKO_CNTR_FEE_10_TSKM", Long.parseLong(planJanCenterFeeInven) * 1000);
        // 計画＿直送センターフィ＿１０月目
        fields.put("KKK_CHKS_CNTR_FEE_10_TSKM", Long.parseLong(planJanCenterFeeChokusou) * 1000);
        // 計画＿在庫利益＿１０月目
        fields.put("KKK_ZAIKO_RIEKI_10_TSKM", Long.parseLong(planJanChokuRiekiInven) * 1000);
        // 計画＿直送利益＿１０月目
        fields.put("KKK_CHKS_RIEKI_10_TSKM", Long.parseLong(planJanChokuRiekiChokusou) * 1000);
        // 計画＿在庫売上＿１１月目
        fields.put("KKK_ZAIKO_URG_11_TSKM", Long.parseLong(planFebAllSalInven) * 1000);
        // 計画＿直送売上＿１１月目
        fields.put("KKK_CHKS_URG_11_TSKM", Long.parseLong(planFebAllSalChokusou) * 1000);
        // 計画＿在庫返品等＿１１月目
        fields.put("KKK_ZAIKO_HMPNT_11_TSKM", Long.parseLong(planFebReturnInven) * 1000);
        // 計画＿直送返品等＿１１月目
        fields.put("KKK_CHKS_HMPNT_11_TSKM", Long.parseLong(planFebReturnChokusou) * 1000);
        // 計画＿在庫支払リベート＿１１月目
        fields.put("KKK_ZAIKO_SHHR_RBT_11_TSKM", Long.parseLong(planFebRebateInven) * 1000);
        // 計画＿直送支払リベート＿１１月目
        fields.put("KKK_CHKS_SHHR_RBT_11_TSKM", Long.parseLong(planFebRebateChokusou) * 1000);
        // 計画＿在庫センターフィ＿１１月目
        fields.put("KKK_ZAIKO_CNTR_FEE_11_TSKM", Long.parseLong(planFebCenterFeeInven) * 1000);
        // 計画＿直送センターフィ＿１１月目
        fields.put("KKK_CHKS_CNTR_FEE_11_TSKM", Long.parseLong(planFebCenterFeeChokusou) * 1000);
        // 計画＿在庫利益＿１１月目
        fields.put("KKK_ZAIKO_RIEKI_11_TSKM", Long.parseLong(planFebChokuRiekiInven) * 1000);
        // 計画＿直送利益＿１１月目
        fields.put("KKK_CHKS_RIEKI_11_TSKM", Long.parseLong(planFebChokuRiekiChokusou) * 1000);
        // 計画＿在庫売上＿１２月目
        fields.put("KKK_ZAIKO_URG_12_TSKM", Long.parseLong(planMarAllSalInven) * 1000);
        // 計画＿直送売上＿１２月目
        fields.put("KKK_CHKS_URG_12_TSKM", Long.parseLong(planMarAllSalChokusou) * 1000);
        // 計画＿在庫返品等＿１２月目
        fields.put("KKK_ZAIKO_HMPNT_12_TSKM", Long.parseLong(planMarReturnInven) * 1000);
        // 計画＿直送返品等＿１２月目
        fields.put("KKK_CHKS_HMPNT_12_TSKM", Long.parseLong(planMarReturnChokusou) * 1000);
        // 計画＿在庫支払リベート＿１２月目
        fields.put("KKK_ZAIKO_SHHR_RBT_12_TSKM", Long.parseLong(planMarRebateInven) * 1000);
        // 計画＿直送支払リベート＿１２月目
        fields.put("KKK_CHKS_SHHR_RBT_12_TSKM", Long.parseLong(planMarRebateChokusou) * 1000);
        // 計画＿在庫センターフィ＿１２月目
        fields.put("KKK_ZAIKO_CNTR_FEE_12_TSKM", Long.parseLong(planMarCenterFeeInven) * 1000);
        // 計画＿直送センターフィ＿１２月目
        fields.put("KKK_CHKS_CNTR_FEE_12_TSKM", Long.parseLong(planMarCenterFeeChokusou) * 1000);
        // 計画＿在庫利益＿１２月目
        fields.put("KKK_ZAIKO_RIEKI_12_TSKM", Long.parseLong(planMarChokuRiekiInven) * 1000);
        // 計画＿直送利益＿１２月目
        fields.put("KKK_CHKS_RIEKI_12_TSKM", Long.parseLong(planMarChokuRiekiChokusou) * 1000);
        // 実績見通し＿在庫売上＿１０月目
        fields.put("JSSK_MTSH_ZAIKO_URG_10_TSKM", Long.parseLong(outlookJanAllSalInven) * 1000);
        // 実績見通し＿直送売上＿１０月目
        fields.put("JSSK_MTSH_CHKS_URG_10_TSKM", Long.parseLong(outlookJanAllSalChokusou) * 1000);
        // 実績見通し＿在庫返品等＿１０月目
        fields.put("JSSK_MTSH_ZAIKO_HMPNT_10_TSKM", Long.parseLong(outlookJanReturnInven) * 1000);
        // 実績見通し＿直送返品等＿１０月目
        fields.put("JSSK_MTSH_CHKS_HMPNT_10_TSKM", Long.parseLong(outlookJanReturnChokusou) * 1000);
        // 実績見通し＿在庫支払リベート＿１０月目
        fields.put("JSSK_MTSH_ZAIKO_SHHR_RBT_10_TSKM", Long.parseLong(outlookJanRebateInven) * 1000);
        // 実績見通し＿直送支払リベート＿１０月目
        fields.put("JSSK_MTSH_CHKS_SHHR_RBT_10_TSKM", Long.parseLong(outlookJanRebateChokusou) * 1000);
        // 実績見通し＿在庫センターフィ＿１０月目
        fields.put("JSSK_MTSH_ZAIKO_CNTR_FEE_10_TSKM", Long.parseLong(outlookJanCenterFeeInven) * 1000);
        // 実績見通し＿直送センターフィ＿１０月目
        fields.put("JSSK_MTSH_CHKS_CNTR_FEE_10_TSKM", Long.parseLong(outlookJanCenterFeeChokusou) * 1000);
        // 実績見通し＿在庫利益＿１０月目
        fields.put("JSSK_MTSH_ZAIKO_RIEKI_10_TSKM", Long.parseLong(outlookJanChokuRiekiInven) * 1000);
        // 実績見通し＿直送利益＿１０月目
        fields.put("JSSK_MTSH_CHKS_RIEKI_10_TSKM", Long.parseLong(outlookJanChokuRiekiChokusou) * 1000);
        // 実績見通し＿在庫売上＿１１月目
        fields.put("JSSK_MTSH_ZAIKO_URG_11_TSKM", Long.parseLong(outlookFebAllSalInven) * 1000);
        // 実績見通し＿直送売上＿１１月目
        fields.put("JSSK_MTSH_CHKS_URG_11_TSKM", Long.parseLong(outlookFebAllSalChokusou) * 1000);
        // 実績見通し＿在庫返品等＿１１月目
        fields.put("JSSK_MTSH_ZAIKO_HMPNT_11_TSKM", Long.parseLong(outlookFebReturnInven) * 1000);
        // 実績見通し＿直送返品等＿１１月目
        fields.put("JSSK_MTSH_CHKS_HMPNT_11_TSKM", Long.parseLong(outlookFebReturnChokusou) * 1000);
        // 実績見通し＿在庫支払リベート＿１１月目
        fields.put("JSSK_MTSH_ZAIKO_SHHR_RBT_11_TSKM", Long.parseLong(outlookFebRebateInven) * 1000);
        // 実績見通し＿直送支払リベート＿１１月目
        fields.put("JSSK_MTSH_CHKS_SHHR_RBT_11_TSKM", Long.parseLong(outlookFebRebateChokusou) * 1000);
        // 実績見通し＿在庫センターフィ＿１１月目
        fields.put("JSSK_MTSH_ZAIKO_CNTR_FEE_11_TSKM", Long.parseLong(outlookFebCenterFeeInven) * 1000);
        // 実績見通し＿直送センターフィ＿１１月目
        fields.put("JSSK_MTSH_CHKS_CNTR_FEE_11_TSKM", Long.parseLong(outlookFebCenterFeeChokusou) * 1000);
        // 実績見通し＿在庫利益＿１１月目
        fields.put("JSSK_MTSH_ZAIKO_RIEKI_11_TSKM", Long.parseLong(outlookFebChokuRiekiInven) * 1000);
        // 実績見通し＿直送利益＿１１月目
        fields.put("JSSK_MTSH_CHKS_RIEKI_11_TSKM", Long.parseLong(outlookFebChokuRiekiChokusou) * 1000);
        // 実績見通し＿在庫売上＿１２月目
        fields.put("JSSK_MTSH_ZAIKO_URG_12_TSKM", Long.parseLong(outlookMarAllSalInven) * 1000);
        // 実績見通し＿直送売上＿１２月目
        fields.put("JSSK_MTSH_CHKS_URG_12_TSKM", Long.parseLong(outlookMarAllSalChokusou) * 1000);
        // 実績見通し＿在庫返品等＿１２月目
        fields.put("JSSK_MTSH_ZAIKO_HMPNT_12_TSKM", Long.parseLong(outlookMarReturnInven) * 1000);
        // 実績見通し＿直送返品等＿１２月目
        fields.put("JSSK_MTSH_CHKS_HMPNT_12_TSKM", Long.parseLong(outlookMarReturnChokusou) * 1000);
        // 実績見通し＿在庫支払リベート＿１２月目
        fields.put("JSSK_MTSH_ZAIKO_SHHR_RBT_12_TSKM", Long.parseLong(outlookMarRebateInven) * 1000);
        // 実績見通し＿直送支払リベート＿１２月目
        fields.put("JSSK_MTSH_CHKS_SHHR_RBT_12_TSKM", Long.parseLong(outlookMarRebateChokusou) * 1000);
        // 実績見通し＿在庫センターフィ＿１２月目
        fields.put("JSSK_MTSH_ZAIKO_CNTR_FEE_12_TSKM", Long.parseLong(outlookMarCenterFeeInven) * 1000);
        // 実績見通し＿直送センターフィ＿１２月目
        fields.put("JSSK_MTSH_CHKS_CNTR_FEE_12_TSKM", Long.parseLong(outlookMarCenterFeeChokusou) * 1000);
        // 実績見通し＿在庫利益＿１２月目
        fields.put("JSSK_MTSH_ZAIKO_RIEKI_12_TSKM", Long.parseLong(outlookMarChokuRiekiInven) * 1000);
        // 実績見通し＿直送利益＿１２月目
        fields.put("JSSK_MTSH_CHKS_RIEKI_12_TSKM", Long.parseLong(outlookMarChokuRiekiChokusou) * 1000);

        // パラメータ.ファイル種別 = 3:見通し・計画_採算管理単位C別 / 見通し・計画_企業別＜エリア＞　の場合
        // 計画＿総売上高計登録
        ImportRequest importRequest = RequestContext.getImportRequest();
        dataType = importRequest.getDataType();
        if(dataType.equals(BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE))
        {
            // 計画＿総売上高計
            // 下記項目の合計値でセットする
            // CSV.【採算管理単位コード単位内の1行目】計画4月総売上高在庫～計画3月総売上高在庫
            // CSV.【採算管理単位コード単位内の2行目】計画4月総売上高直送～計画3月総売上高直送
            Long sumTotal = Long.parseLong(planAprAllSalInven) + Long.parseLong(planMayAllSalInven) + Long.parseLong(planJunAllSalInven) + Long.parseLong(planJulAllSalInven) +
                    Long.parseLong(planAugAllSalInven) + Long.parseLong(planSepAllSalInven) + Long.parseLong(planOctAllSalInven) + Long.parseLong(planNovAllSalInven) +
                    Long.parseLong(planDecAllSalInven) + Long.parseLong(planJanAllSalInven) + Long.parseLong(planFebAllSalInven) + Long.parseLong(planMarAllSalInven) +
                    Long.parseLong(planAprAllSalChokusou) + Long.parseLong(planMayAllSalChokusou) + Long.parseLong(planJunAllSalChokusou) + Long.parseLong(planJulAllSalChokusou) +
                    Long.parseLong(planAugAllSalChokusou) + Long.parseLong(planSepAllSalChokusou) + Long.parseLong(planOctAllSalChokusou) + Long.parseLong(planNovAllSalChokusou) +
                    Long.parseLong(planDecAllSalChokusou) + Long.parseLong(planJanAllSalChokusou) + Long.parseLong(planFebAllSalChokusou) + Long.parseLong(planMarAllSalChokusou);

            fields.put("KKK_URG_KEI", sumTotal * 1000);
        }

        // プログラムIDをFunctionUtilから取得
        String programId = FunctionUtil.getFunctionId(BusinessConstants.OPERATION_UPLOAD_CODE,
                BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE).getFunctionId();
        if(dataType.equals(BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE))
        {
            programId = FunctionUtil.getFunctionId(BusinessConstants.OPERATION_UPLOAD_CODE,
                    BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE).getFunctionId();
        }
        // 自動生成フィールド
        Date currentTime = new Date();
        String currentTimeStr = DateUtil.formatDateTime(currentTime);

        if (isInsert) {
            // 挿入時のみ設定するフィールド
            fields.put("TRK_PRGRM_ID", programId);
            fields.put("RCRD_TRK_NCHJ", currentTimeStr);
            fields.put("VRSN", 1);
        }

        // 更新時は常に設定
        fields.put("KSHN_PRGRM_ID", programId);
        fields.put("RCRD_KSHN_NCHJ", currentTimeStr);

        return fields;
    }
}
