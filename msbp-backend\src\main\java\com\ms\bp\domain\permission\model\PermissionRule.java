package com.ms.bp.domain.permission.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 権限ルールマスタ領域モデル
 * 権限ルールに関するビジネスロジックを含む富ドメインモデル
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PermissionRule {
    
    /**
     * システム運用企業コード
     */
    private String systemOperationCompanyCode;
    
    /**
     * 種類コード
     */
    private String typeCode;
    
    /**
     * ルール種類
     */
    private String ruleType;
    
    /**
     * ルール開始日 (YYYYMMDD)
     */
    private String ruleStartDate;
    
    /**
     * ルール終了日 (YYYYMMDD)
     */
    private String ruleEndDate;
    
}
