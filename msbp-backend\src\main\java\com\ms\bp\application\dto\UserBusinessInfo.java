package com.ms.bp.application.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * ユーザー業務情報DTO
 * ログインユーザーの業務関連情報を保持するデータ転送オブジェクト
 * 社員マスタ、ユニットマスタ、グループマスタ、組織エリアマスタから取得した情報を統合
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserBusinessInfo {

    /**
     * ユニットコード（5桁）
     * 社員マスタから取得
     */
    private String unitCode;

    /**
     * 役職区分コード（2桁）
     * 社員マスタから取得
     */
    private String positionCode;

    /**
     * 所属区分（1:本社 2:エリア）
     * エリアコードに基づいて判定
     */
    private String affiliationDivision;

    /**
     * エリアコード（4桁）
     * グループマスタから取得
     */
    private String areaCode;

    /**
     * エリア名称（漢字）
     * 組織エリアマスタから取得
     */
    private String areaName;

    /**
     * 社員コード（参照用）
     * 検索条件として使用した社員コード
     */
    private String shainCode;

    /**
     * システム運用企業コード（参照用）
     * 検索条件として使用したシステム運用企業コード
     */
    private String systemOperationCompanyCode;

    /**
     * グループコード
     * M_GROUPMST.GROUP_CODE
     */
    private String groupCode;
}
