package com.ms.bp.domain.file.areaoutlookplan;

import com.ms.bp.application.data.strategy.FileSplitStrategy;
import com.ms.bp.domain.file.areaoutlookplan.strategy.AreaOutlookPlanCompanyDataStrategy;
import com.ms.bp.domain.file.areaoutlookplan.strategy.AreaOutlookPlanFileSplitStrategy;
import com.ms.bp.domain.file.areaoutlookplan.strategy.AreaOutlookPlanUnitDataStrategy;
import com.ms.bp.domain.file.base.AbstractExportService;
import com.ms.bp.domain.file.base.DataExpander;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.io.model.FileFormat;
import com.ms.bp.shared.common.io.options.ExportOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AreaOutlookPlanExportService extends AbstractExportService<Map<String, Object>> {

    private static final Logger logger = LoggerFactory.getLogger(AreaOutlookPlanExportService.class);

    /** エリア 見通し・計画_採算管理単位C別_CSV出力列名リスト*/
    private static final List<String> UNIT_CSV_COLUMNS = Arrays.asList(
            "no",                                         //No
            "iknmt_area_code",                            //移管元部署
            "iknsk_area_code",                            //移管先部署
            "area_code",                                  //ｴﾘｱｺｰﾄﾞ
            "area_mei_kanji",                             //ｴﾘｱ名漢字
            "group_code",                                 //ｸﾞﾙｰﾌﾟCD
            "unit_code",                                  //ﾕﾆｯﾄCD
            "tntsh_mei_kanji",                            //担当名
            "ssnkn_tncd",                                 //採算CD7桁
            "ssn_kanri_tnm_kanji",                        //採算名
            "kigyo_code",                                 //企業CD
            "kgym_kanji",                                 //企業名
            "ctgry_mei_kanji",                            //ｶﾃｺﾞﾘ
            "sub_ctgry_mei_kanji",                        //ｻﾌﾞｶﾃ
            "gyt_mei",                                    //業態(事業計画)
            "hnkg_trkm_kubun",                            //変更後取組区分
            "gyt_hrts",                                   //業態比率
            "kkk_zaiko_urg_1_tskm",                       //4月(計画)_総売上高(在庫)
            "kkk_chks_urg_1_tskm",                        //4月(計画)_総売上高(直送)
            "kkk_zaiko_hmpnt_1_tskm",                     //4月(計画)_返品(在庫)
            "kkk_chks_hmpnt_1_tskm",                      //4月(計画)_返品(直送)
            "kkk_zaiko_shhr_rbt_1_tskm",                  //4月(計画)_リベート(在庫)
            "kkk_chks_shhr_rbt_1_tskm",                   //4月(計画)_リベート(直送)
            "kkk_zaiko_cntr_fee_1_tskm",                  //4月(計画)_センターフィ(在庫)
            "kkk_chks_cntr_fee_1_tskm",                   //4月(計画)_センターフィ(直送)
            "kkk_zaiko_rieki_1_tskm",                     //4月(計画)_直接利益(在庫)
            "kkk_chks_rieki_1_tskm",                      //4月(計画)_直接利益(直送)
            "kkk_zaiko_urg_2_tskm",                       //5月(計画)_総売上高(在庫)
            "kkk_chks_urg_2_tskm",                        //5月(計画)_総売上高(直送)
            "kkk_zaiko_hmpnt_2_tskm",                     //5月(計画)_返品(在庫)
            "kkk_chks_hmpnt_2_tskm",                      //5月(計画)_返品(直送)
            "kkk_zaiko_shhr_rbt_2_tskm",                  //5月(計画)_リベート(在庫)
            "kkk_chks_shhr_rbt_2_tskm",                   //5月(計画)_リベート(直送)
            "kkk_zaiko_cntr_fee_2_tskm",                  //5月(計画)_センターフィ(在庫)
            "kkk_chks_cntr_fee_2_tskm",                   //5月(計画)_センターフィ(直送)
            "kkk_zaiko_rieki_2_tskm",                     //5月(計画)_直接利益(在庫)
            "kkk_chks_rieki_2_tskm",                      //5月(計画)_直接利益(直送)
            "kkk_zaiko_urg_3_tskm",                       //6月(計画)_総売上高(在庫)
            "kkk_chks_urg_3_tskm",                        //6月(計画)_総売上高(直送)
            "kkk_zaiko_hmpnt_3_tskm",                     //6月(計画)_返品(在庫)
            "kkk_chks_hmpnt_3_tskm",                      //6月(計画)_返品(直送)
            "kkk_zaiko_shhr_rbt_3_tskm",                  //6月(計画)_リベート(在庫)
            "kkk_chks_shhr_rbt_3_tskm",                   //6月(計画)_リベート(直送)
            "kkk_zaiko_cntr_fee_3_tskm",                  //6月(計画)_センターフィ(在庫)
            "kkk_chks_cntr_fee_3_tskm",                   //6月(計画)_センターフィ(直送)
            "kkk_zaiko_rieki_3_tskm",                     //6月(計画)_直接利益(在庫)
            "kkk_chks_rieki_3_tskm",                      //6月(計画)_直接利益(直送)
            "kkk_zaiko_urg_4_tskm",                       //7月(計画)_総売上高(在庫)
            "kkk_chks_urg_4_tskm",                        //7月(計画)_総売上高(直送)
            "kkk_zaiko_hmpnt_4_tskm",                     //7月(計画)_返品(在庫)
            "kkk_chks_hmpnt_4_tskm",                      //7月(計画)_返品(直送)
            "kkk_zaiko_shhr_rbt_4_tskm",                  //7月(計画)_リベート(在庫)
            "kkk_chks_shhr_rbt_4_tskm",                   //7月(計画)_リベート(直送)
            "kkk_zaiko_cntr_fee_4_tskm",                  //7月(計画)_センターフィ(在庫)
            "kkk_chks_cntr_fee_4_tskm",                   //7月(計画)_センターフィ(直送)
            "kkk_zaiko_rieki_4_tskm",                     //7月(計画)_直接利益(在庫)
            "kkk_chks_rieki_4_tskm",                      //7月(計画)_直接利益(直送)
            "kkk_zaiko_urg_5_tskm",                       //8月(計画)_総売上高(在庫)
            "kkk_chks_urg_5_tskm",                        //8月(計画)_総売上高(直送)
            "kkk_zaiko_hmpnt_5_tskm",                     //8月(計画)_返品(在庫)
            "kkk_chks_hmpnt_5_tskm",                      //8月(計画)_返品(直送)
            "kkk_zaiko_shhr_rbt_5_tskm",                  //8月(計画)_リベート(在庫)
            "kkk_chks_shhr_rbt_5_tskm",                   //8月(計画)_リベート(直送)
            "kkk_zaiko_cntr_fee_5_tskm",                  //8月(計画)_センターフィ(在庫)
            "kkk_chks_cntr_fee_5_tskm",                   //8月(計画)_センターフィ(直送)
            "kkk_zaiko_rieki_5_tskm",                     //8月(計画)_直接利益(在庫)
            "kkk_chks_rieki_5_tskm",                      //8月(計画)_直接利益(直送)
            "kkk_zaiko_urg_6_tskm",                       //9月(計画)_総売上高(在庫)
            "kkk_chks_urg_6_tskm",                        //9月(計画)_総売上高(直送)
            "kkk_zaiko_hmpnt_6_tskm",                     //9月(計画)_返品(在庫)
            "kkk_chks_hmpnt_6_tskm",                      //9月(計画)_返品(直送)
            "kkk_zaiko_shhr_rbt_6_tskm",                  //9月(計画)_リベート(在庫)
            "kkk_chks_shhr_rbt_6_tskm",                   //9月(計画)_リベート(直送)
            "kkk_zaiko_cntr_fee_6_tskm",                  //9月(計画)_センターフィ(在庫)
            "kkk_chks_cntr_fee_6_tskm",                   //9月(計画)_センターフィ(直送)
            "kkk_zaiko_rieki_6_tskm",                     //9月(計画)_直接利益(在庫)
            "kkk_chks_rieki_6_tskm",                      //9月(計画)_直接利益(直送)
            "kkk_zaiko_urg_7_tskm",                       //10月(計画)_総売上高(在庫)
            "kkk_chks_urg_7_tskm",                        //10月(計画)_総売上高(直送)
            "kkk_zaiko_hmpnt_7_tskm",                     //10月(計画)_返品(在庫)
            "kkk_chks_hmpnt_7_tskm",                      //10月(計画)_返品(直送)
            "kkk_zaiko_shhr_rbt_7_tskm",                  //10月(計画)_リベート(在庫)
            "kkk_chks_shhr_rbt_7_tskm",                   //10月(計画)_リベート(直送)
            "kkk_zaiko_cntr_fee_7_tskm",                  //10月(計画)_センターフィ(在庫)
            "kkk_chks_cntr_fee_7_tskm",                   //10月(計画)_センターフィ(直送)
            "kkk_zaiko_rieki_7_tskm",                     //10月(計画)_直接利益(在庫)
            "kkk_chks_rieki_7_tskm",                      //10月(計画)_直接利益(直送)
            "kkk_zaiko_urg_8_tskm",                       //11月(計画)_総売上高(在庫)
            "kkk_chks_urg_8_tskm",                        //11月(計画)_総売上高(直送)
            "kkk_zaiko_hmpnt_8_tskm",                     //11月(計画)_返品(在庫)
            "kkk_chks_hmpnt_8_tskm",                      //11月(計画)_返品(直送)
            "kkk_zaiko_shhr_rbt_8_tskm",                  //11月(計画)_リベート(在庫)
            "kkk_chks_shhr_rbt_8_tskm",                   //11月(計画)_リベート(直送)
            "kkk_zaiko_cntr_fee_8_tskm",                  //11月(計画)_センターフィ(在庫)
            "kkk_chks_cntr_fee_8_tskm",                   //11月(計画)_センターフィ(直送)
            "kkk_zaiko_rieki_8_tskm",                     //11月(計画)_直接利益(在庫)
            "kkk_chks_rieki_8_tskm",                      //11月(計画)_直接利益(直送)
            "kkk_zaiko_urg_9_tskm",                       //12月(計画)_総売上高(在庫)
            "kkk_chks_urg_9_tskm",                        //12月(計画)_総売上高(直送)
            "kkk_zaiko_hmpnt_9_tskm",                     //12月(計画)_返品(在庫)
            "kkk_chks_hmpnt_9_tskm",                      //12月(計画)_返品(直送)
            "kkk_zaiko_shhr_rbt_9_tskm",                  //12月(計画)_リベート(在庫)
            "kkk_chks_shhr_rbt_9_tskm",                   //12月(計画)_リベート(直送)
            "kkk_zaiko_cntr_fee_9_tskm",                  //12月(計画)_センターフィ(在庫)
            "kkk_chks_cntr_fee_9_tskm",                   //12月(計画)_センターフィ(直送)
            "kkk_zaiko_rieki_9_tskm",                     //12月(計画)_直接利益(在庫)
            "kkk_chks_rieki_9_tskm",                      //12月(計画)_直接利益(直送)
            "kkk_zaiko_urg_10_tskm",                      //1月(計画)_総売上高(在庫)
            "kkk_chks_urg_10_tskm",                       //1月(計画)_総売上高(直送)
            "kkk_zaiko_hmpnt_10_tskm",                    //1月(計画)_返品(在庫)
            "kkk_chks_hmpnt_10_tskm",                     //1月(計画)_返品(直送)
            "kkk_zaiko_shhr_rbt_10_tskm",                 //1月(計画)_リベート(在庫)
            "kkk_chks_shhr_rbt_10_tskm",                  //1月(計画)_リベート(直送)
            "kkk_zaiko_cntr_fee_10_tskm",                 //1月(計画)_センターフィ(在庫)
            "kkk_chks_cntr_fee_10_tskm",                  //1月(計画)_センターフィ(直送)
            "kkk_zaiko_rieki_10_tskm",                    //1月(計画)_直接利益(在庫)
            "kkk_chks_rieki_10_tskm",                     //1月(計画)_直接利益(直送)
            "kkk_zaiko_urg_11_tskm",                      //2月(計画)_総売上高(在庫)
            "kkk_chks_urg_11_tskm",                       //2月(計画)_総売上高(直送)
            "kkk_zaiko_hmpnt_11_tskm",                    //2月(計画)_返品(在庫)
            "kkk_chks_hmpnt_11_tskm",                     //2月(計画)_返品(直送)
            "kkk_zaiko_shhr_rbt_11_tskm",                 //2月(計画)_リベート(在庫)
            "kkk_chks_shhr_rbt_11_tskm",                  //2月(計画)_リベート(直送)
            "kkk_zaiko_cntr_fee_11_tskm",                 //2月(計画)_センターフィ(在庫)
            "kkk_chks_cntr_fee_11_tskm",                  //2月(計画)_センターフィ(直送)
            "kkk_zaiko_rieki_11_tskm",                    //2月(計画)_直接利益(在庫)
            "kkk_chks_rieki_11_tskm",                     //2月(計画)_直接利益(直送)
            "kkk_zaiko_urg_12_tskm",                      //3月(計画)_総売上高(在庫)
            "kkk_chks_urg_12_tskm",                       //3月(計画)_総売上高(直送)
            "kkk_zaiko_hmpnt_12_tskm",                    //3月(計画)_返品(在庫)
            "kkk_chks_hmpnt_12_tskm",                     //3月(計画)_返品(直送)
            "kkk_zaiko_shhr_rbt_12_tskm",                 //3月(計画)_リベート(在庫)
            "kkk_chks_shhr_rbt_12_tskm",                  //3月(計画)_リベート(直送)
            "kkk_zaiko_cntr_fee_12_tskm",                 //3月(計画)_センターフィ(在庫)
            "kkk_chks_cntr_fee_12_tskm",                  //3月(計画)_センターフィ(直送)
            "kkk_zaiko_rieki_12_tskm",                    //3月(計画)_直接利益(在庫)
            "kkk_chks_rieki_12_tskm",                     //3月(計画)_直接利益(直送)
            "ssnkn_jssk_zaiko_urg_1_tskm",                //4月(実績見通し)_総売上高(在庫)
            "ssnkn_jssk_chks_urg_1_tskm",                 //4月(実績見通し)_総売上高(直送)
            "ssnkn_jssk_zaiko_hmpnt_1_tskm",              //4月(実績見通し)_返品(在庫)
            "ssnkn_jssk_chks_hmpnt_1_tskm",               //4月(実績見通し)_返品(直送)
            "ssnkn_jssk_zaiko_shhr_rbt_1_tskm",           //4月(実績見通し)_リベート(在庫)
            "ssnkn_jssk_chks_shhr_rbt_1_tskm",            //4月(実績見通し)_リベート(直送)
            "ssnkn_jssk_zaiko_cntr_fee_1_tskm",           //4月(実績見通し)_センターフィ(在庫)
            "ssnkn_jssk_chks_cntr_fee_1_tskm",            //4月(実績見通し)_センターフィ(直送)
            "ssnkn_jssk_zaiko_rieki_1_tskm",              //4月(実績見通し)_直接利益(在庫)
            "ssnkn_jssk_chks_rieki_1_tskm",               //4月(実績見通し)_直接利益(直送)
            "ssnkn_jssk_zaiko_urg_2_tskm",                //5月(実績見通し)_総売上高(在庫)
            "ssnkn_jssk_chks_urg_2_tskm",                 //5月(実績見通し)_総売上高(直送)
            "ssnkn_jssk_zaiko_hmpnt_2_tskm",              //5月(実績見通し)_返品(在庫)
            "ssnkn_jssk_chks_hmpnt_2_tskm",               //5月(実績見通し)_返品(直送)
            "ssnkn_jssk_zaiko_shhr_rbt_2_tskm",           //5月(実績見通し)_リベート(在庫)
            "ssnkn_jssk_chks_shhr_rbt_2_tskm",            //5月(実績見通し)_リベート(直送)
            "ssnkn_jssk_zaiko_cntr_fee_2_tskm",           //5月(実績見通し)_センターフィ(在庫)
            "ssnkn_jssk_chks_cntr_fee_2_tskm",            //5月(実績見通し)_センターフィ(直送)
            "ssnkn_jssk_zaiko_rieki_2_tskm",              //5月(実績見通し)_直接利益(在庫)
            "ssnkn_jssk_chks_rieki_2_tskm",               //5月(実績見通し)_直接利益(直送)
            "ssnkn_jssk_zaiko_urg_3_tskm",                //6月(実績見通し)_総売上高(在庫)
            "ssnkn_jssk_chks_urg_3_tskm",                 //6月(実績見通し)_総売上高(直送)
            "ssnkn_jssk_zaiko_hmpnt_3_tskm",              //6月(実績見通し)_返品(在庫)
            "ssnkn_jssk_chks_hmpnt_3_tskm",               //6月(実績見通し)_返品(直送)
            "ssnkn_jssk_zaiko_shhr_rbt_3_tskm",           //6月(実績見通し)_リベート(在庫)
            "ssnkn_jssk_chks_shhr_rbt_3_tskm",            //6月(実績見通し)_リベート(直送)
            "ssnkn_jssk_zaiko_cntr_fee_3_tskm",           //6月(実績見通し)_センターフィ(在庫)
            "ssnkn_jssk_chks_cntr_fee_3_tskm",            //6月(実績見通し)_センターフィ(直送)
            "ssnkn_jssk_zaiko_rieki_3_tskm",              //6月(実績見通し)_直接利益(在庫)
            "ssnkn_jssk_chks_rieki_3_tskm",               //6月(実績見通し)_直接利益(直送)
            "ssnkn_jssk_zaiko_urg_4_tskm",                //7月(実績見通し)_総売上高(在庫)
            "ssnkn_jssk_chks_urg_4_tskm",                 //7月(実績見通し)_総売上高(直送)
            "ssnkn_jssk_zaiko_hmpnt_4_tskm",              //7月(実績見通し)_返品(在庫)
            "ssnkn_jssk_chks_hmpnt_4_tskm",               //7月(実績見通し)_返品(直送)
            "ssnkn_jssk_zaiko_shhr_rbt_4_tskm",           //7月(実績見通し)_リベート(在庫)
            "ssnkn_jssk_chks_shhr_rbt_4_tskm",            //7月(実績見通し)_リベート(直送)
            "ssnkn_jssk_zaiko_cntr_fee_4_tskm",           //7月(実績見通し)_センターフィ(在庫)
            "ssnkn_jssk_chks_cntr_fee_4_tskm",            //7月(実績見通し)_センターフィ(直送)
            "ssnkn_jssk_zaiko_rieki_4_tskm",              //7月(実績見通し)_直接利益(在庫)
            "ssnkn_jssk_chks_rieki_4_tskm",               //7月(実績見通し)_直接利益(直送)
            "ssnkn_jssk_zaiko_urg_5_tskm",                //8月(実績見通し)_総売上高(在庫)
            "ssnkn_jssk_chks_urg_5_tskm",                 //8月(実績見通し)_総売上高(直送)
            "ssnkn_jssk_zaiko_hmpnt_5_tskm",              //8月(実績見通し)_返品(在庫)
            "ssnkn_jssk_chks_hmpnt_5_tskm",               //8月(実績見通し)_返品(直送)
            "ssnkn_jssk_zaiko_shhr_rbt_5_tskm",           //8月(実績見通し)_リベート(在庫)
            "ssnkn_jssk_chks_shhr_rbt_5_tskm",            //8月(実績見通し)_リベート(直送)
            "ssnkn_jssk_zaiko_cntr_fee_5_tskm",           //8月(実績見通し)_センターフィ(在庫)
            "ssnkn_jssk_chks_cntr_fee_5_tskm",            //8月(実績見通し)_センターフィ(直送)
            "ssnkn_jssk_zaiko_rieki_5_tskm",              //8月(実績見通し)_直接利益(在庫)
            "ssnkn_jssk_chks_rieki_5_tskm",               //8月(実績見通し)_直接利益(直送)
            "ssnkn_jssk_zaiko_urg_6_tskm",                //9月(実績見通し)_総売上高(在庫)
            "ssnkn_jssk_chks_urg_6_tskm",                 //9月(実績見通し)_総売上高(直送)
            "ssnkn_jssk_zaiko_hmpnt_6_tskm",              //9月(実績見通し)_返品(在庫)
            "ssnkn_jssk_chks_hmpnt_6_tskm",               //9月(実績見通し)_返品(直送)
            "ssnkn_jssk_zaiko_shhr_rbt_6_tskm",           //9月(実績見通し)_リベート(在庫)
            "ssnkn_jssk_chks_shhr_rbt_6_tskm",            //9月(実績見通し)_リベート(直送)
            "ssnkn_jssk_zaiko_cntr_fee_6_tskm",           //9月(実績見通し)_センターフィ(在庫)
            "ssnkn_jssk_chks_cntr_fee_6_tskm",            //9月(実績見通し)_センターフィ(直送)
            "ssnkn_jssk_zaiko_rieki_6_tskm",              //9月(実績見通し)_直接利益(在庫)
            "ssnkn_jssk_chks_rieki_6_tskm",               //9月(実績見通し)_直接利益(直送)
            "ssnkn_jssk_zaiko_urg_7_tskm",                //10月(実績見通し)_総売上高(在庫)
            "ssnkn_jssk_chks_urg_7_tskm",                 //10月(実績見通し)_総売上高(直送)
            "ssnkn_jssk_zaiko_hmpnt_7_tskm",              //10月(実績見通し)_返品(在庫)
            "ssnkn_jssk_chks_hmpnt_7_tskm",               //10月(実績見通し)_返品(直送)
            "ssnkn_jssk_zaiko_shhr_rbt_7_tskm",           //10月(実績見通し)_リベート(在庫)
            "ssnkn_jssk_chks_shhr_rbt_7_tskm",            //10月(実績見通し)_リベート(直送)
            "ssnkn_jssk_zaiko_cntr_fee_7_tskm",           //10月(実績見通し)_センターフィ(在庫)
            "ssnkn_jssk_chks_cntr_fee_7_tskm",            //10月(実績見通し)_センターフィ(直送)
            "ssnkn_jssk_zaiko_rieki_7_tskm",              //10月(実績見通し)_直接利益(在庫)
            "ssnkn_jssk_chks_rieki_7_tskm",               //10月(実績見通し)_直接利益(直送)
            "ssnkn_jssk_zaiko_urg_8_tskm",                //11月(実績見通し)_総売上高(在庫)
            "ssnkn_jssk_chks_urg_8_tskm",                 //11月(実績見通し)_総売上高(直送)
            "ssnkn_jssk_zaiko_hmpnt_8_tskm",              //11月(実績見通し)_返品(在庫)
            "ssnkn_jssk_chks_hmpnt_8_tskm",               //11月(実績見通し)_返品(直送)
            "ssnkn_jssk_zaiko_shhr_rbt_8_tskm",           //11月(実績見通し)_リベート(在庫)
            "ssnkn_jssk_chks_shhr_rbt_8_tskm",            //11月(実績見通し)_リベート(直送)
            "ssnkn_jssk_zaiko_cntr_fee_8_tskm",           //11月(実績見通し)_センターフィ(在庫)
            "ssnkn_jssk_chks_cntr_fee_8_tskm",            //11月(実績見通し)_センターフィ(直送)
            "ssnkn_jssk_zaiko_rieki_8_tskm",              //11月(実績見通し)_直接利益(在庫)
            "ssnkn_jssk_chks_rieki_8_tskm",               //11月(実績見通し)_直接利益(直送)
            "ssnkn_jssk_zaiko_urg_9_tskm",                //12月(実績見通し)_総売上高(在庫)
            "ssnkn_jssk_chks_urg_9_tskm",                 //12月(実績見通し)_総売上高(直送)
            "ssnkn_jssk_zaiko_hmpnt_9_tskm",              //12月(実績見通し)_返品(在庫)
            "ssnkn_jssk_chks_hmpnt_9_tskm",               //12月(実績見通し)_返品(直送)
            "ssnkn_jssk_zaiko_shhr_rbt_9_tskm",           //12月(実績見通し)_リベート(在庫)
            "ssnkn_jssk_chks_shhr_rbt_9_tskm",            //12月(実績見通し)_リベート(直送)
            "ssnkn_jssk_zaiko_cntr_fee_9_tskm",           //12月(実績見通し)_センターフィ(在庫)
            "ssnkn_jssk_chks_cntr_fee_9_tskm",            //12月(実績見通し)_センターフィ(直送)
            "ssnkn_jssk_zaiko_rieki_9_tskm",              //12月(実績見通し)_直接利益(在庫)
            "ssnkn_jssk_chks_rieki_9_tskm",               //12月(実績見通し)_直接利益(直送)
            "jssk_mtsh_zaiko_urg_10_tskm",                //1月(実績見通し)_総売上高(在庫)
            "jssk_mtsh_chks_urg_10_tskm",                 //1月(実績見通し)_総売上高(直送)
            "jssk_mtsh_zaiko_hmpnt_10_tskm",              //1月(実績見通し)_返品(在庫)
            "jssk_mtsh_chks_hmpnt_10_tskm",               //1月(実績見通し)_返品(直送)
            "jssk_mtsh_zaiko_shhr_rbt_10_tskm",           //1月(実績見通し)_リベート(在庫)
            "jssk_mtsh_chks_shhr_rbt_10_tskm",            //1月(実績見通し)_リベート(直送)
            "jssk_mtsh_zaiko_cntr_fee_10_tskm",           //1月(実績見通し)_センターフィ(在庫)
            "jssk_mtsh_chks_cntr_fee_10_tskm",            //1月(実績見通し)_センターフィ(直送)
            "jssk_mtsh_zaiko_rieki_10_tskm",              //1月(実績見通し)_直接利益(在庫)
            "jssk_mtsh_chks_rieki_10_tskm",               //1月(実績見通し)_直接利益(直送)
            "jssk_mtsh_zaiko_urg_11_tskm",                //2月(実績見通し)_総売上高(在庫)
            "jssk_mtsh_chks_urg_11_tskm",                 //2月(実績見通し)_総売上高(直送)
            "jssk_mtsh_zaiko_hmpnt_11_tskm",              //2月(実績見通し)_返品(在庫)
            "jssk_mtsh_chks_hmpnt_11_tskm",               //2月(実績見通し)_返品(直送)
            "jssk_mtsh_zaiko_shhr_rbt_11_tskm",           //2月(実績見通し)_リベート(在庫)
            "jssk_mtsh_chks_shhr_rbt_11_tskm",            //2月(実績見通し)_リベート(直送)
            "jssk_mtsh_zaiko_cntr_fee_11_tskm",           //2月(実績見通し)_センターフィ(在庫)
            "jssk_mtsh_chks_cntr_fee_11_tskm",            //2月(実績見通し)_センターフィ(直送)
            "jssk_mtsh_zaiko_rieki_11_tskm",              //2月(実績見通し)_直接利益(在庫)
            "jssk_mtsh_chks_rieki_11_tskm",               //2月(実績見通し)_直接利益(直送)
            "jssk_mtsh_zaiko_urg_12_tskm",                //3月(実績見通し)_総売上高(在庫)
            "jssk_mtsh_chks_urg_12_tskm",                 //3月(実績見通し)_総売上高(直送)
            "jssk_mtsh_zaiko_hmpnt_12_tskm",              //3月(実績見通し)_返品(在庫)
            "jssk_mtsh_chks_hmpnt_12_tskm",               //3月(実績見通し)_返品(直送)
            "jssk_mtsh_zaiko_shhr_rbt_12_tskm",           //3月(実績見通し)_リベート(在庫)
            "jssk_mtsh_chks_shhr_rbt_12_tskm",            //3月(実績見通し)_リベート(直送)
            "jssk_mtsh_zaiko_cntr_fee_12_tskm",           //3月(実績見通し)_センターフィ(在庫)
            "jssk_mtsh_chks_cntr_fee_12_tskm",            //3月(実績見通し)_センターフィ(直送)
            "jssk_mtsh_zaiko_rieki_12_tskm",              //3月(実績見通し)_直接利益(在庫)
            "jssk_mtsh_chks_rieki_12_tskm",               //3月(実績見通し)_直接利益(直送)
            "file_info_1",                                //ファイル情報1
            "file_info_2"                                 //ファイル情報2
    );

    /** エリア 見通し・計画_企業別_CSV出力列名リスト */
    private static final List<String> COMPANY_CSV_COLUMNS = Arrays.asList(
            "no",                                    //No
            "group_code",                            //ｸﾞﾙｰﾌﾟCD
            "kigyo_code",                            //企業CD
            "kgym_kanji",                            //企業名
            "ctgry_mei_kanji",                       //ｶﾃｺﾞﾘ
            "hnkg_trkm_kubun",                       //変更後取組区分
            "gyt_hrts",                              //業態比率
            "total_kkk_zaiko_urg_1_tskm",            //4月(計画)_総売上高(在庫)
            "total_kkk_chks_urg_1_tskm",             //4月(計画)_総売上高(直送)
            "total_kkk_zaiko_rieki_1_tskm",          //4月(計画)_直接利益(在庫)
            "total_kkk_chks_rieki_1_tskm",           //4月(計画)_直接利益(直送)
            "total_kkk_zaiko_urg_2_tskm",            //5月(計画)_総売上高(在庫)
            "total_kkk_chks_urg_2_tskm",             //5月(計画)_総売上高(直送)
            "total_kkk_zaiko_rieki_2_tskm",          //5月(計画)_直接利益(在庫)
            "total_kkk_chks_rieki_2_tskm",           //5月(計画)_直接利益(直送)
            "total_kkk_zaiko_urg_3_tskm",            //6月(計画)_総売上高(在庫)
            "total_kkk_chks_urg_3_tskm",             //6月(計画)_総売上高(直送)
            "total_kkk_zaiko_rieki_3_tskm",          //6月(計画)_直接利益(在庫)
            "total_kkk_chks_rieki_3_tskm",           //6月(計画)_直接利益(直送)
            "total_kkk_zaiko_urg_4_tskm",            //7月(計画)_総売上高(在庫)
            "total_kkk_chks_urg_4_tskm",             //7月(計画)_総売上高(直送)
            "total_kkk_zaiko_rieki_4_tskm",          //7月(計画)_直接利益(在庫)
            "total_kkk_chks_rieki_4_tskm",           //7月(計画)_直接利益(直送)
            "total_kkk_zaiko_urg_5_tskm",            //8月(計画)_総売上高(在庫)
            "total_kkk_chks_urg_5_tskm",             //8月(計画)_総売上高(直送)
            "total_kkk_zaiko_rieki_5_tskm",          //8月(計画)_直接利益(在庫)
            "total_kkk_chks_rieki_5_tskm",           //8月(計画)_直接利益(直送)
            "total_kkk_zaiko_urg_6_tskm",            //9月(計画)_総売上高(在庫)
            "total_kkk_chks_urg_6_tskm",             //9月(計画)_総売上高(直送)
            "total_kkk_zaiko_rieki_6_tskm",          //9月(計画)_直接利益(在庫)
            "total_kkk_chks_rieki_6_tskm",           //9月(計画)_直接利益(直送)
            "total_kkk_zaiko_urg_7_tskm",            //10月(計画)_総売上高(在庫)
            "total_kkk_chks_urg_7_tskm",             //10月(計画)_総売上高(直送)
            "total_kkk_zaiko_rieki_7_tskm",          //10月(計画)_直接利益(在庫)
            "total_kkk_chks_rieki_7_tskm",           //10月(計画)_直接利益(直送)
            "total_kkk_zaiko_urg_8_tskm",            //11月(計画)_総売上高(在庫)
            "total_kkk_chks_urg_8_tskm",             //11月(計画)_総売上高(直送)
            "total_kkk_zaiko_rieki_8_tskm",          //11月(計画)_直接利益(在庫)
            "total_kkk_chks_rieki_8_tskm",           //11月(計画)_直接利益(直送)
            "total_kkk_zaiko_urg_9_tskm",            //12月(計画)_総売上高(在庫)
            "total_kkk_chks_urg_9_tskm",             //12月(計画)_総売上高(直送)
            "total_kkk_zaiko_rieki_9_tskm",          //12月(計画)_直接利益(在庫)
            "total_kkk_chks_rieki_9_tskm",           //12月(計画)_直接利益(直送)
            "total_kkk_zaiko_urg_10_tskm",           //1月(計画)_総売上高(在庫)
            "total_kkk_chks_urg_10_tskm",            //1月(計画)_総売上高(直送)
            "total_kkk_zaiko_rieki_10_tskm",         //1月(計画)_直接利益(在庫)
            "total_kkk_chks_rieki_10_tskm",          //1月(計画)_直接利益(直送)
            "total_kkk_zaiko_urg_11_tskm",           //2月(計画)_総売上高(在庫)
            "total_kkk_chks_urg_11_tskm",            //2月(計画)_総売上高(直送)
            "total_kkk_zaiko_rieki_11_tskm",         //2月(計画)_直接利益(在庫)
            "total_kkk_chks_rieki_11_tskm",          //2月(計画)_直接利益(直送)
            "total_kkk_zaiko_urg_12_tskm",           //3月(計画)_総売上高(在庫)
            "total_kkk_chks_urg_12_tskm",            //3月(計画)_総売上高(直送)
            "total_kkk_zaiko_rieki_12_tskm",         //3月(計画)_直接利益(在庫)
            "total_kkk_chks_rieki_12_tskm",          //3月(計画)_直接利益(直送)
            "total_ssnkn_jssk_zaiko_urg_1_tskm",     //4月(実績見通し)_総売上高(在庫)
            "total_ssnkn_jssk_chks_urg_1_tskm",      //4月(実績見通し)_総売上高(直送)
            "total_ssnkn_jssk_zaiko_rieki_1_tskm",   //4月(実績見通し)_直接利益(在庫)
            "total_ssnkn_jssk_chks_rieki_1_tskm",    //4月(実績見通し)_直接利益(直送)
            "total_ssnkn_jssk_zaiko_urg_2_tskm",     //5月(実績見通し)_総売上高(在庫)
            "total_ssnkn_jssk_chks_urg_2_tskm",      //5月(実績見通し)_総売上高(直送)
            "total_ssnkn_jssk_zaiko_rieki_2_tskm",   //5月(実績見通し)_直接利益(在庫)
            "total_ssnkn_jssk_chks_rieki_2_tskm",    //5月(実績見通し)_直接利益(直送)
            "total_ssnkn_jssk_zaiko_urg_3_tskm",     //6月(実績見通し)_総売上高(在庫)
            "total_ssnkn_jssk_chks_urg_3_tskm",      //6月(実績見通し)_総売上高(直送)
            "total_ssnkn_jssk_zaiko_rieki_3_tskm",   //6月(実績見通し)_直接利益(在庫)
            "total_ssnkn_jssk_chks_rieki_3_tskm",    //6月(実績見通し)_直接利益(直送)
            "total_ssnkn_jssk_zaiko_urg_4_tskm",     //7月(実績見通し)_総売上高(在庫)
            "total_ssnkn_jssk_chks_urg_4_tskm",      //7月(実績見通し)_総売上高(直送)
            "total_ssnkn_jssk_zaiko_rieki_4_tskm",   //7月(実績見通し)_直接利益(在庫)
            "total_ssnkn_jssk_chks_rieki_4_tskm",    //7月(実績見通し)_直接利益(直送)
            "total_ssnkn_jssk_zaiko_urg_5_tskm",     //8月(実績見通し)_総売上高(在庫)
            "total_ssnkn_jssk_chks_urg_5_tskm",      //8月(実績見通し)_総売上高(直送)
            "total_ssnkn_jssk_zaiko_rieki_5_tskm",   //8月(実績見通し)_直接利益(在庫)
            "total_ssnkn_jssk_chks_rieki_5_tskm",    //8月(実績見通し)_直接利益(直送)
            "total_ssnkn_jssk_zaiko_urg_6_tskm",     //9月(実績見通し)_総売上高(在庫)
            "total_ssnkn_jssk_chks_urg_6_tskm",      //9月(実績見通し)_総売上高(直送)
            "total_ssnkn_jssk_zaiko_rieki_6_tskm",   //9月(実績見通し)_直接利益(在庫)
            "total_ssnkn_jssk_chks_rieki_6_tskm",    //9月(実績見通し)_直接利益(直送)
            "total_ssnkn_jssk_zaiko_urg_7_tskm",     //10月(実績見通し)_総売上高(在庫)
            "total_ssnkn_jssk_chks_urg_7_tskm",      //10月(実績見通し)_総売上高(直送)
            "total_ssnkn_jssk_zaiko_rieki_7_tskm",   //10月(実績見通し)_直接利益(在庫)
            "total_ssnkn_jssk_chks_rieki_7_tskm",    //10月(実績見通し)_直接利益(直送)
            "total_ssnkn_jssk_zaiko_urg_8_tskm",     //11月(実績見通し)_総売上高(在庫)
            "total_ssnkn_jssk_chks_urg_8_tskm",      //11月(実績見通し)_総売上高(直送)
            "total_ssnkn_jssk_zaiko_rieki_8_tskm",   //11月(実績見通し)_直接利益(在庫)
            "total_ssnkn_jssk_chks_rieki_8_tskm",    //11月(実績見通し)_直接利益(直送)
            "total_ssnkn_jssk_zaiko_urg_9_tskm",     //12月(実績見通し)_総売上高(在庫)
            "total_ssnkn_jssk_chks_urg_9_tskm",      //12月(実績見通し)_総売上高(直送)
            "total_ssnkn_jssk_zaiko_rieki_9_tskm",   //12月(実績見通し)_直接利益(在庫)
            "total_ssnkn_jssk_chks_rieki_9_tskm",    //12月(実績見通し)_直接利益(直送)
            "total_jssk_mtsh_zaiko_urg_10_tskm",     //1月(実績見通し)_総売上高(在庫)
            "total_jssk_mtsh_chks_urg_10_tskm",      //1月(実績見通し)_総売上高(直送)
            "total_jssk_mtsh_zaiko_rieki_10_tskm",   //1月(実績見通し)_直接利益(在庫)
            "total_jssk_mtsh_chks_rieki_10_tskm",    //1月(実績見通し)_直接利益(直送)
            "total_jssk_mtsh_zaiko_urg_11_tskm",     //2月(実績見通し)_総売上高(在庫)
            "total_jssk_mtsh_chks_urg_11_tskm",      //2月(実績見通し)_総売上高(直送)
            "total_jssk_mtsh_zaiko_rieki_11_tskm",   //2月(実績見通し)_直接利益(在庫)
            "total_jssk_mtsh_chks_rieki_11_tskm",    //2月(実績見通し)_直接利益(直送)
            "total_jssk_mtsh_zaiko_urg_12_tskm",     //3月(実績見通し)_総売上高(在庫)
            "total_jssk_mtsh_chks_urg_12_tskm",      //3月(実績見通し)_総売上高(直送)
            "total_jssk_mtsh_zaiko_rieki_12_tskm",   //3月(実績見通し)_直接利益(在庫)
            "total_jssk_mtsh_chks_rieki_12_tskm",    //3月(実績見通し)_直接利益(直送)
            "file_info_1",                           //ファイル情報1
            "file_info_2"                            //ファイル情報2
    );

    /**
     * SQL戦略を登録（サブクラスで実装）
     */
    @Override
    protected void registerSqlStrategies() {
        logger.info("エリア_移管エクスポート用SQL戦略を登録中...");

        // エリア_移管専用のSQL戦略を登録
        sqlStrategyManager.registerStrategy(new AreaOutlookPlanUnitDataStrategy());
        sqlStrategyManager.registerStrategy(new AreaOutlookPlanCompanyDataStrategy());

        logger.info("エリア_移管エクスポート用SQL戦略登録完了");
        sqlStrategyManager.logStatistics();
    }

    /**
     * データ展開処理器を取得（サブクラスで実装）
     * データ展開が不要な場合はnullを返す
     *
     * @return データ展開処理器、または展開不要の場合はnull
     */
    @Override
    protected DataExpander getDataExpander() {
        // 次年度計画マスタデータは展開処理が不要
        // 複雑なJOINクエリで必要なデータを直接取得するため
        return null;
    }

    /**
     * ファイル分割戦略を取得（サブクラスで実装）
     * 分割が不要な場合はデフォルト戦略を返す
     *
     * @return ファイル分割戦略
     */
    @Override
    public FileSplitStrategy getSplitStrategy() {
        return new AreaOutlookPlanFileSplitStrategy();
    }

    /**
     * SQL戦略とファイル名を考慮したエクスポートオプションを構築
     * サブクラスで実装する具体的なオプション構築メソッド
     *
     * @param sqlStrategyKey SQL戦略キー（nullの場合はデフォルト戦略）
     * @return エクスポートオプション
     */
    @Override
    protected ExportOptions buildExportOptionsWithStrategy(String sqlStrategyKey) {
        return null;
    }

    /**
     * SQL戦略とExportTaskを考慮したエクスポートオプションを構築（新規追加）
     * ExportTask毎に異なるCSVヘッダー設定を可能にする拡張メソッド
     * サブクラスで必要に応じてオーバーライドし、タスク固有の設定を実装
     *
     * @param sqlStrategyKey SQL戦略キー（nullの場合はデフォルト戦略）
     * @param task エクスポートタスク（ヘッダー設定の参考情報）
     * @return エクスポートオプション
     */
    @Override
    protected ExportOptions buildExportOptionsWithStrategy(String sqlStrategyKey,Object task) {
        // ExportTaskオブジェクトからタスク名を取得
        String taskName = getTaskName(task);

        // タスク名に基づいて異なる設定を選択
        List<String> columns;
        Map<String, String> headerMapping;

        // エリア 見通し・計画_採算管理単位C別
        if (taskName.equals(BusinessConstants.MTSH_KKK_FILE_TYPE_C)) {
            columns = UNIT_CSV_COLUMNS;
            headerMapping = createUnitFieldHeaderMapping();
        } else {
            // エリア 見通し・計画_採算管理単位企業別
            columns = COMPANY_CSV_COLUMNS;
            headerMapping = createCompanyFieldHeaderMapping();
        }

        return ExportOptions.builder()
                .format(FileFormat.CSV)
                .columns(columns)
                .batchSize(BusinessConstants.CSV_DOWNLOAD_BATCH_SIZE)
                .countTotal(true)
                .enableFieldMapping(true)
                .fieldHeaderMapping(headerMapping)
                .build();
    }

    /**
     * ExportTaskオブジェクトからタスク名を取得するヘルパーメソッド
     * リフレクションを使用してタスク名を安全に取得
     * @param task ExportTaskオブジェクト
     * @return タスク名（取得できない場合は"default"）
     */
    private String getTaskName(Object task) {
        try {
            // ExportTaskのgetName()メソッドを呼び出し
            return (String) task.getClass().getMethod("getName").invoke(task);
        } catch (Exception e) {
            logger.warn("ExportTaskからタスク名を取得できませんでした: {}", e.getMessage());
            return "default";
        }
    }

    /**
     * エリア 見通し・計画_C別用フィールドヘッダーマッピングを作成
     * データベース列名から日本語ヘッダー名への変換マップ
     */
    private Map<String, String> createUnitFieldHeaderMapping() {
        Map<String, String> mapping = new HashMap<>();
        mapping.put("no", "No");
        mapping.put("iknmt_area_code", "移管元部署");
        mapping.put("iknsk_area_code", "移管先部署");
        mapping.put("area_code", "ｴﾘｱｺｰﾄﾞ");
        mapping.put("area_mei_kanji", "ｴﾘｱ名漢字");
        mapping.put("group_code", "ｸﾞﾙｰﾌﾟCD");
        mapping.put("unit_code", "ﾕﾆｯﾄCD");
        mapping.put("tntsh_mei_kanji", "担当名");
        mapping.put("ssnkn_tncd", "採算CD7桁");
        mapping.put("ssn_kanri_tnm_kanji", "採算名");
        mapping.put("kigyo_code", "企業CD");
        mapping.put("kgym_kanji", "企業名");
        mapping.put("ctgry_mei_kanji", "ｶﾃｺﾞﾘ");
        mapping.put("sub_ctgry_mei_kanji", "ｻﾌﾞｶﾃ");
        mapping.put("gyt_mei", "業態(事業計画)");
        mapping.put("hnkg_trkm_kubun", "変更後取組区分");
        mapping.put("gyt_hrts", "業態比率");
        mapping.put("kkk_zaiko_urg_1_tskm", "4月(計画)_総売上高(在庫)");
        mapping.put("kkk_chks_urg_1_tskm", "4月(計画)_総売上高(直送)");
        mapping.put("kkk_zaiko_hmpnt_1_tskm", "4月(計画)_返品(在庫)");
        mapping.put("kkk_chks_hmpnt_1_tskm", "4月(計画)_返品(直送)");
        mapping.put("kkk_zaiko_shhr_rbt_1_tskm", "4月(計画)_リベート(在庫)");
        mapping.put("kkk_chks_shhr_rbt_1_tskm", "4月(計画)_リベート(直送)");
        mapping.put("kkk_zaiko_cntr_fee_1_tskm", "4月(計画)_センターフィ(在庫)");
        mapping.put("kkk_chks_cntr_fee_1_tskm", "4月(計画)_センターフィ(直送)");
        mapping.put("kkk_zaiko_rieki_1_tskm", "4月(計画)_直接利益(在庫)");
        mapping.put("kkk_chks_rieki_1_tskm", "4月(計画)_直接利益(直送)");
        mapping.put("kkk_zaiko_urg_2_tskm", "5月(計画)_総売上高(在庫)");
        mapping.put("kkk_chks_urg_2_tskm", "5月(計画)_総売上高(直送)");
        mapping.put("kkk_zaiko_hmpnt_2_tskm", "5月(計画)_返品(在庫)");
        mapping.put("kkk_chks_hmpnt_2_tskm", "5月(計画)_返品(直送)");
        mapping.put("kkk_zaiko_shhr_rbt_2_tskm", "5月(計画)_リベート(在庫)");
        mapping.put("kkk_chks_shhr_rbt_2_tskm", "5月(計画)_リベート(直送)");
        mapping.put("kkk_zaiko_cntr_fee_2_tskm", "5月(計画)_センターフィ(在庫)");
        mapping.put("kkk_chks_cntr_fee_2_tskm", "5月(計画)_センターフィ(直送)");
        mapping.put("kkk_zaiko_rieki_2_tskm", "5月(計画)_直接利益(在庫)");
        mapping.put("kkk_chks_rieki_2_tskm", "5月(計画)_直接利益(直送)");
        mapping.put("kkk_zaiko_urg_3_tskm", "6月(計画)_総売上高(在庫)");
        mapping.put("kkk_chks_urg_3_tskm", "6月(計画)_総売上高(直送)");
        mapping.put("kkk_zaiko_hmpnt_3_tskm", "6月(計画)_返品(在庫)");
        mapping.put("kkk_chks_hmpnt_3_tskm", "6月(計画)_返品(直送)");
        mapping.put("kkk_zaiko_shhr_rbt_3_tskm", "6月(計画)_リベート(在庫)");
        mapping.put("kkk_chks_shhr_rbt_3_tskm", "6月(計画)_リベート(直送)");
        mapping.put("kkk_zaiko_cntr_fee_3_tskm", "6月(計画)_センターフィ(在庫)");
        mapping.put("kkk_chks_cntr_fee_3_tskm", "6月(計画)_センターフィ(直送)");
        mapping.put("kkk_zaiko_rieki_3_tskm", "6月(計画)_直接利益(在庫)");
        mapping.put("kkk_chks_rieki_3_tskm", "6月(計画)_直接利益(直送)");
        mapping.put("kkk_zaiko_urg_4_tskm", "7月(計画)_総売上高(在庫)");
        mapping.put("kkk_chks_urg_4_tskm", "7月(計画)_総売上高(直送)");
        mapping.put("kkk_zaiko_hmpnt_4_tskm", "7月(計画)_返品(在庫)");
        mapping.put("kkk_chks_hmpnt_4_tskm", "7月(計画)_返品(直送)");
        mapping.put("kkk_zaiko_shhr_rbt_4_tskm", "7月(計画)_リベート(在庫)");
        mapping.put("kkk_chks_shhr_rbt_4_tskm", "7月(計画)_リベート(直送)");
        mapping.put("kkk_zaiko_cntr_fee_4_tskm", "7月(計画)_センターフィ(在庫)");
        mapping.put("kkk_chks_cntr_fee_4_tskm", "7月(計画)_センターフィ(直送)");
        mapping.put("kkk_zaiko_rieki_4_tskm", "7月(計画)_直接利益(在庫)");
        mapping.put("kkk_chks_rieki_4_tskm", "7月(計画)_直接利益(直送)");
        mapping.put("kkk_zaiko_urg_5_tskm", "8月(計画)_総売上高(在庫)");
        mapping.put("kkk_chks_urg_5_tskm", "8月(計画)_総売上高(直送)");
        mapping.put("kkk_zaiko_hmpnt_5_tskm", "8月(計画)_返品(在庫)");
        mapping.put("kkk_chks_hmpnt_5_tskm", "8月(計画)_返品(直送)");
        mapping.put("kkk_zaiko_shhr_rbt_5_tskm", "8月(計画)_リベート(在庫)");
        mapping.put("kkk_chks_shhr_rbt_5_tskm", "8月(計画)_リベート(直送)");
        mapping.put("kkk_zaiko_cntr_fee_5_tskm", "8月(計画)_センターフィ(在庫)");
        mapping.put("kkk_chks_cntr_fee_5_tskm", "8月(計画)_センターフィ(直送)");
        mapping.put("kkk_zaiko_rieki_5_tskm", "8月(計画)_直接利益(在庫)");
        mapping.put("kkk_chks_rieki_5_tskm", "8月(計画)_直接利益(直送)");
        mapping.put("kkk_zaiko_urg_6_tskm", "9月(計画)_総売上高(在庫)");
        mapping.put("kkk_chks_urg_6_tskm", "9月(計画)_総売上高(直送)");
        mapping.put("kkk_zaiko_hmpnt_6_tskm", "9月(計画)_返品(在庫)");
        mapping.put("kkk_chks_hmpnt_6_tskm", "9月(計画)_返品(直送)");
        mapping.put("kkk_zaiko_shhr_rbt_6_tskm", "9月(計画)_リベート(在庫)");
        mapping.put("kkk_chks_shhr_rbt_6_tskm", "9月(計画)_リベート(直送)");
        mapping.put("kkk_zaiko_cntr_fee_6_tskm", "9月(計画)_センターフィ(在庫)");
        mapping.put("kkk_chks_cntr_fee_6_tskm", "9月(計画)_センターフィ(直送)");
        mapping.put("kkk_zaiko_rieki_6_tskm", "9月(計画)_直接利益(在庫)");
        mapping.put("kkk_chks_rieki_6_tskm", "9月(計画)_直接利益(直送)");
        mapping.put("kkk_zaiko_urg_7_tskm", "10月(計画)_総売上高(在庫)");
        mapping.put("kkk_chks_urg_7_tskm", "10月(計画)_総売上高(直送)");
        mapping.put("kkk_zaiko_hmpnt_7_tskm", "10月(計画)_返品(在庫)");
        mapping.put("kkk_chks_hmpnt_7_tskm", "10月(計画)_返品(直送)");
        mapping.put("kkk_zaiko_shhr_rbt_7_tskm", "10月(計画)_リベート(在庫)");
        mapping.put("kkk_chks_shhr_rbt_7_tskm", "10月(計画)_リベート(直送)");
        mapping.put("kkk_zaiko_cntr_fee_7_tskm", "10月(計画)_センターフィ(在庫)");
        mapping.put("kkk_chks_cntr_fee_7_tskm", "10月(計画)_センターフィ(直送)");
        mapping.put("kkk_zaiko_rieki_7_tskm", "10月(計画)_直接利益(在庫)");
        mapping.put("kkk_chks_rieki_7_tskm", "10月(計画)_直接利益(直送)");
        mapping.put("kkk_zaiko_urg_8_tskm", "11月(計画)_総売上高(在庫)");
        mapping.put("kkk_chks_urg_8_tskm", "11月(計画)_総売上高(直送)");
        mapping.put("kkk_zaiko_hmpnt_8_tskm", "11月(計画)_返品(在庫)");
        mapping.put("kkk_chks_hmpnt_8_tskm", "11月(計画)_返品(直送)");
        mapping.put("kkk_zaiko_shhr_rbt_8_tskm", "11月(計画)_リベート(在庫)");
        mapping.put("kkk_chks_shhr_rbt_8_tskm", "11月(計画)_リベート(直送)");
        mapping.put("kkk_zaiko_cntr_fee_8_tskm", "11月(計画)_センターフィ(在庫)");
        mapping.put("kkk_chks_cntr_fee_8_tskm", "11月(計画)_センターフィ(直送)");
        mapping.put("kkk_zaiko_rieki_8_tskm", "11月(計画)_直接利益(在庫)");
        mapping.put("kkk_chks_rieki_8_tskm", "11月(計画)_直接利益(直送)");
        mapping.put("kkk_zaiko_urg_9_tskm", "12月(計画)_総売上高(在庫)");
        mapping.put("kkk_chks_urg_9_tskm", "12月(計画)_総売上高(直送)");
        mapping.put("kkk_zaiko_hmpnt_9_tskm", "12月(計画)_返品(在庫)");
        mapping.put("kkk_chks_hmpnt_9_tskm", "12月(計画)_返品(直送)");
        mapping.put("kkk_zaiko_shhr_rbt_9_tskm", "12月(計画)_リベート(在庫)");
        mapping.put("kkk_chks_shhr_rbt_9_tskm", "12月(計画)_リベート(直送)");
        mapping.put("kkk_zaiko_cntr_fee_9_tskm", "12月(計画)_センターフィ(在庫)");
        mapping.put("kkk_chks_cntr_fee_9_tskm", "12月(計画)_センターフィ(直送)");
        mapping.put("kkk_zaiko_rieki_9_tskm", "12月(計画)_直接利益(在庫)");
        mapping.put("kkk_chks_rieki_9_tskm", "12月(計画)_直接利益(直送)");
        mapping.put("kkk_zaiko_urg_10_tskm", "1月(計画)_総売上高(在庫)");
        mapping.put("kkk_chks_urg_10_tskm", "1月(計画)_総売上高(直送)");
        mapping.put("kkk_zaiko_hmpnt_10_tskm", "1月(計画)_返品(在庫)");
        mapping.put("kkk_chks_hmpnt_10_tskm", "1月(計画)_返品(直送)");
        mapping.put("kkk_zaiko_shhr_rbt_10_tskm", "1月(計画)_リベート(在庫)");
        mapping.put("kkk_chks_shhr_rbt_10_tskm", "1月(計画)_リベート(直送)");
        mapping.put("kkk_zaiko_cntr_fee_10_tskm", "1月(計画)_センターフィ(在庫)");
        mapping.put("kkk_chks_cntr_fee_10_tskm", "1月(計画)_センターフィ(直送)");
        mapping.put("kkk_zaiko_rieki_10_tskm", "1月(計画)_直接利益(在庫)");
        mapping.put("kkk_chks_rieki_10_tskm", "1月(計画)_直接利益(直送)");
        mapping.put("kkk_zaiko_urg_11_tskm", "2月(計画)_総売上高(在庫)");
        mapping.put("kkk_chks_urg_11_tskm", "2月(計画)_総売上高(直送)");
        mapping.put("kkk_zaiko_hmpnt_11_tskm", "2月(計画)_返品(在庫)");
        mapping.put("kkk_chks_hmpnt_11_tskm", "2月(計画)_返品(直送)");
        mapping.put("kkk_zaiko_shhr_rbt_11_tskm", "2月(計画)_リベート(在庫)");
        mapping.put("kkk_chks_shhr_rbt_11_tskm", "2月(計画)_リベート(直送)");
        mapping.put("kkk_zaiko_cntr_fee_11_tskm", "2月(計画)_センターフィ(在庫)");
        mapping.put("kkk_chks_cntr_fee_11_tskm", "2月(計画)_センターフィ(直送)");
        mapping.put("kkk_zaiko_rieki_11_tskm", "2月(計画)_直接利益(在庫)");
        mapping.put("kkk_chks_rieki_11_tskm", "2月(計画)_直接利益(直送)");
        mapping.put("kkk_zaiko_urg_12_tskm", "3月(計画)_総売上高(在庫)");
        mapping.put("kkk_chks_urg_12_tskm", "3月(計画)_総売上高(直送)");
        mapping.put("kkk_zaiko_hmpnt_12_tskm", "3月(計画)_返品(在庫)");
        mapping.put("kkk_chks_hmpnt_12_tskm", "3月(計画)_返品(直送)");
        mapping.put("kkk_zaiko_shhr_rbt_12_tskm", "3月(計画)_リベート(在庫)");
        mapping.put("kkk_chks_shhr_rbt_12_tskm", "3月(計画)_リベート(直送)");
        mapping.put("kkk_zaiko_cntr_fee_12_tskm", "3月(計画)_センターフィ(在庫)");
        mapping.put("kkk_chks_cntr_fee_12_tskm", "3月(計画)_センターフィ(直送)");
        mapping.put("kkk_zaiko_rieki_12_tskm", "3月(計画)_直接利益(在庫)");
        mapping.put("kkk_chks_rieki_12_tskm", "3月(計画)_直接利益(直送)");
        mapping.put("ssnkn_jssk_zaiko_urg_1_tskm", "4月(実績見通し)_総売上高(在庫)");
        mapping.put("ssnkn_jssk_chks_urg_1_tskm", "4月(実績見通し)_総売上高(直送)");
        mapping.put("ssnkn_jssk_zaiko_hmpnt_1_tskm", "4月(実績見通し)_返品(在庫)");
        mapping.put("ssnkn_jssk_chks_hmpnt_1_tskm", "4月(実績見通し)_返品(直送)");
        mapping.put("ssnkn_jssk_zaiko_shhr_rbt_1_tskm", "4月(実績見通し)_リベート(在庫)");
        mapping.put("ssnkn_jssk_chks_shhr_rbt_1_tskm", "4月(実績見通し)_リベート(直送)");
        mapping.put("ssnkn_jssk_zaiko_cntr_fee_1_tskm", "4月(実績見通し)_センターフィ(在庫)");
        mapping.put("ssnkn_jssk_chks_cntr_fee_1_tskm", "4月(実績見通し)_センターフィ(直送)");
        mapping.put("ssnkn_jssk_zaiko_rieki_1_tskm", "4月(実績見通し)_直接利益(在庫)");
        mapping.put("ssnkn_jssk_chks_rieki_1_tskm", "4月(実績見通し)_直接利益(直送)");
        mapping.put("ssnkn_jssk_zaiko_urg_2_tskm", "5月(実績見通し)_総売上高(在庫)");
        mapping.put("ssnkn_jssk_chks_urg_2_tskm", "5月(実績見通し)_総売上高(直送)");
        mapping.put("ssnkn_jssk_zaiko_hmpnt_2_tskm", "5月(実績見通し)_返品(在庫)");
        mapping.put("ssnkn_jssk_chks_hmpnt_2_tskm", "5月(実績見通し)_返品(直送)");
        mapping.put("ssnkn_jssk_zaiko_shhr_rbt_2_tskm", "5月(実績見通し)_リベート(在庫)");
        mapping.put("ssnkn_jssk_chks_shhr_rbt_2_tskm", "5月(実績見通し)_リベート(直送)");
        mapping.put("ssnkn_jssk_zaiko_cntr_fee_2_tskm", "5月(実績見通し)_センターフィ(在庫)");
        mapping.put("ssnkn_jssk_chks_cntr_fee_2_tskm", "5月(実績見通し)_センターフィ(直送)");
        mapping.put("ssnkn_jssk_zaiko_rieki_2_tskm", "5月(実績見通し)_直接利益(在庫)");
        mapping.put("ssnkn_jssk_chks_rieki_2_tskm", "5月(実績見通し)_直接利益(直送)");
        mapping.put("ssnkn_jssk_zaiko_urg_3_tskm", "6月(実績見通し)_総売上高(在庫)");
        mapping.put("ssnkn_jssk_chks_urg_3_tskm", "6月(実績見通し)_総売上高(直送)");
        mapping.put("ssnkn_jssk_zaiko_hmpnt_3_tskm", "6月(実績見通し)_返品(在庫)");
        mapping.put("ssnkn_jssk_chks_hmpnt_3_tskm", "6月(実績見通し)_返品(直送)");
        mapping.put("ssnkn_jssk_zaiko_shhr_rbt_3_tskm", "6月(実績見通し)_リベート(在庫)");
        mapping.put("ssnkn_jssk_chks_shhr_rbt_3_tskm", "6月(実績見通し)_リベート(直送)");
        mapping.put("ssnkn_jssk_zaiko_cntr_fee_3_tskm", "6月(実績見通し)_センターフィ(在庫)");
        mapping.put("ssnkn_jssk_chks_cntr_fee_3_tskm", "6月(実績見通し)_センターフィ(直送)");
        mapping.put("ssnkn_jssk_zaiko_rieki_3_tskm", "6月(実績見通し)_直接利益(在庫)");
        mapping.put("ssnkn_jssk_chks_rieki_3_tskm", "6月(実績見通し)_直接利益(直送)");
        mapping.put("ssnkn_jssk_zaiko_urg_4_tskm", "7月(実績見通し)_総売上高(在庫)");
        mapping.put("ssnkn_jssk_chks_urg_4_tskm", "7月(実績見通し)_総売上高(直送)");
        mapping.put("ssnkn_jssk_zaiko_hmpnt_4_tskm", "7月(実績見通し)_返品(在庫)");
        mapping.put("ssnkn_jssk_chks_hmpnt_4_tskm", "7月(実績見通し)_返品(直送)");
        mapping.put("ssnkn_jssk_zaiko_shhr_rbt_4_tskm", "7月(実績見通し)_リベート(在庫)");
        mapping.put("ssnkn_jssk_chks_shhr_rbt_4_tskm", "7月(実績見通し)_リベート(直送)");
        mapping.put("ssnkn_jssk_zaiko_cntr_fee_4_tskm", "7月(実績見通し)_センターフィ(在庫)");
        mapping.put("ssnkn_jssk_chks_cntr_fee_4_tskm", "7月(実績見通し)_センターフィ(直送)");
        mapping.put("ssnkn_jssk_zaiko_rieki_4_tskm", "7月(実績見通し)_直接利益(在庫)");
        mapping.put("ssnkn_jssk_chks_rieki_4_tskm", "7月(実績見通し)_直接利益(直送)");
        mapping.put("ssnkn_jssk_zaiko_urg_5_tskm", "8月(実績見通し)_総売上高(在庫)");
        mapping.put("ssnkn_jssk_chks_urg_5_tskm", "8月(実績見通し)_総売上高(直送)");
        mapping.put("ssnkn_jssk_zaiko_hmpnt_5_tskm", "8月(実績見通し)_返品(在庫)");
        mapping.put("ssnkn_jssk_chks_hmpnt_5_tskm", "8月(実績見通し)_返品(直送)");
        mapping.put("ssnkn_jssk_zaiko_shhr_rbt_5_tskm", "8月(実績見通し)_リベート(在庫)");
        mapping.put("ssnkn_jssk_chks_shhr_rbt_5_tskm", "8月(実績見通し)_リベート(直送)");
        mapping.put("ssnkn_jssk_zaiko_cntr_fee_5_tskm", "8月(実績見通し)_センターフィ(在庫)");
        mapping.put("ssnkn_jssk_chks_cntr_fee_5_tskm", "8月(実績見通し)_センターフィ(直送)");
        mapping.put("ssnkn_jssk_zaiko_rieki_5_tskm", "8月(実績見通し)_直接利益(在庫)");
        mapping.put("ssnkn_jssk_chks_rieki_5_tskm", "8月(実績見通し)_直接利益(直送)");
        mapping.put("ssnkn_jssk_zaiko_urg_6_tskm", "9月(実績見通し)_総売上高(在庫)");
        mapping.put("ssnkn_jssk_chks_urg_6_tskm", "9月(実績見通し)_総売上高(直送)");
        mapping.put("ssnkn_jssk_zaiko_hmpnt_6_tskm", "9月(実績見通し)_返品(在庫)");
        mapping.put("ssnkn_jssk_chks_hmpnt_6_tskm", "9月(実績見通し)_返品(直送)");
        mapping.put("ssnkn_jssk_zaiko_shhr_rbt_6_tskm", "9月(実績見通し)_リベート(在庫)");
        mapping.put("ssnkn_jssk_chks_shhr_rbt_6_tskm", "9月(実績見通し)_リベート(直送)");
        mapping.put("ssnkn_jssk_zaiko_cntr_fee_6_tskm", "9月(実績見通し)_センターフィ(在庫)");
        mapping.put("ssnkn_jssk_chks_cntr_fee_6_tskm", "9月(実績見通し)_センターフィ(直送)");
        mapping.put("ssnkn_jssk_zaiko_rieki_6_tskm", "9月(実績見通し)_直接利益(在庫)");
        mapping.put("ssnkn_jssk_chks_rieki_6_tskm", "9月(実績見通し)_直接利益(直送)");
        mapping.put("ssnkn_jssk_zaiko_urg_7_tskm", "10月(実績見通し)_総売上高(在庫)");
        mapping.put("ssnkn_jssk_chks_urg_7_tskm", "10月(実績見通し)_総売上高(直送)");
        mapping.put("ssnkn_jssk_zaiko_hmpnt_7_tskm", "10月(実績見通し)_返品(在庫)");
        mapping.put("ssnkn_jssk_chks_hmpnt_7_tskm", "10月(実績見通し)_返品(直送)");
        mapping.put("ssnkn_jssk_zaiko_shhr_rbt_7_tskm", "10月(実績見通し)_リベート(在庫)");
        mapping.put("ssnkn_jssk_chks_shhr_rbt_7_tskm", "10月(実績見通し)_リベート(直送)");
        mapping.put("ssnkn_jssk_zaiko_cntr_fee_7_tskm", "10月(実績見通し)_センターフィ(在庫)");
        mapping.put("ssnkn_jssk_chks_cntr_fee_7_tskm", "10月(実績見通し)_センターフィ(直送)");
        mapping.put("ssnkn_jssk_zaiko_rieki_7_tskm", "10月(実績見通し)_直接利益(在庫)");
        mapping.put("ssnkn_jssk_chks_rieki_7_tskm", "10月(実績見通し)_直接利益(直送)");
        mapping.put("ssnkn_jssk_zaiko_urg_8_tskm", "11月(実績見通し)_総売上高(在庫)");
        mapping.put("ssnkn_jssk_chks_urg_8_tskm", "11月(実績見通し)_総売上高(直送)");
        mapping.put("ssnkn_jssk_zaiko_hmpnt_8_tskm", "11月(実績見通し)_返品(在庫)");
        mapping.put("ssnkn_jssk_chks_hmpnt_8_tskm", "11月(実績見通し)_返品(直送)");
        mapping.put("ssnkn_jssk_zaiko_shhr_rbt_8_tskm", "11月(実績見通し)_リベート(在庫)");
        mapping.put("ssnkn_jssk_chks_shhr_rbt_8_tskm", "11月(実績見通し)_リベート(直送)");
        mapping.put("ssnkn_jssk_zaiko_cntr_fee_8_tskm", "11月(実績見通し)_センターフィ(在庫)");
        mapping.put("ssnkn_jssk_chks_cntr_fee_8_tskm", "11月(実績見通し)_センターフィ(直送)");
        mapping.put("ssnkn_jssk_zaiko_rieki_8_tskm", "11月(実績見通し)_直接利益(在庫)");
        mapping.put("ssnkn_jssk_chks_rieki_8_tskm", "11月(実績見通し)_直接利益(直送)");
        mapping.put("ssnkn_jssk_zaiko_urg_9_tskm", "12月(実績見通し)_総売上高(在庫)");
        mapping.put("ssnkn_jssk_chks_urg_9_tskm", "12月(実績見通し)_総売上高(直送)");
        mapping.put("ssnkn_jssk_zaiko_hmpnt_9_tskm", "12月(実績見通し)_返品(在庫)");
        mapping.put("ssnkn_jssk_chks_hmpnt_9_tskm", "12月(実績見通し)_返品(直送)");
        mapping.put("ssnkn_jssk_zaiko_shhr_rbt_9_tskm", "12月(実績見通し)_リベート(在庫)");
        mapping.put("ssnkn_jssk_chks_shhr_rbt_9_tskm", "12月(実績見通し)_リベート(直送)");
        mapping.put("ssnkn_jssk_zaiko_cntr_fee_9_tskm", "12月(実績見通し)_センターフィ(在庫)");
        mapping.put("ssnkn_jssk_chks_cntr_fee_9_tskm", "12月(実績見通し)_センターフィ(直送)");
        mapping.put("ssnkn_jssk_zaiko_rieki_9_tskm", "12月(実績見通し)_直接利益(在庫)");
        mapping.put("ssnkn_jssk_chks_rieki_9_tskm", "12月(実績見通し)_直接利益(直送)");
        mapping.put("jssk_mtsh_zaiko_urg_10_tskm", "1月(実績見通し)_総売上高(在庫)");
        mapping.put("jssk_mtsh_chks_urg_10_tskm", "1月(実績見通し)_総売上高(直送)");
        mapping.put("jssk_mtsh_zaiko_hmpnt_10_tskm", "1月(実績見通し)_返品(在庫)");
        mapping.put("jssk_mtsh_chks_hmpnt_10_tskm", "1月(実績見通し)_返品(直送)");
        mapping.put("jssk_mtsh_zaiko_shhr_rbt_10_tskm", "1月(実績見通し)_リベート(在庫)");
        mapping.put("jssk_mtsh_chks_shhr_rbt_10_tskm", "1月(実績見通し)_リベート(直送)");
        mapping.put("jssk_mtsh_zaiko_cntr_fee_10_tskm", "1月(実績見通し)_センターフィ(在庫)");
        mapping.put("jssk_mtsh_chks_cntr_fee_10_tskm", "1月(実績見通し)_センターフィ(直送)");
        mapping.put("jssk_mtsh_zaiko_rieki_10_tskm", "1月(実績見通し)_直接利益(在庫)");
        mapping.put("jssk_mtsh_chks_rieki_10_tskm", "1月(実績見通し)_直接利益(直送)");
        mapping.put("jssk_mtsh_zaiko_urg_11_tskm", "2月(実績見通し)_総売上高(在庫)");
        mapping.put("jssk_mtsh_chks_urg_11_tskm", "2月(実績見通し)_総売上高(直送)");
        mapping.put("jssk_mtsh_zaiko_hmpnt_11_tskm", "2月(実績見通し)_返品(在庫)");
        mapping.put("jssk_mtsh_chks_hmpnt_11_tskm", "2月(実績見通し)_返品(直送)");
        mapping.put("jssk_mtsh_zaiko_shhr_rbt_11_tskm", "2月(実績見通し)_リベート(在庫)");
        mapping.put("jssk_mtsh_chks_shhr_rbt_11_tskm", "2月(実績見通し)_リベート(直送)");
        mapping.put("jssk_mtsh_zaiko_cntr_fee_11_tskm", "2月(実績見通し)_センターフィ(在庫)");
        mapping.put("jssk_mtsh_chks_cntr_fee_11_tskm", "2月(実績見通し)_センターフィ(直送)");
        mapping.put("jssk_mtsh_zaiko_rieki_11_tskm", "2月(実績見通し)_直接利益(在庫)");
        mapping.put("jssk_mtsh_chks_rieki_11_tskm", "2月(実績見通し)_直接利益(直送)");
        mapping.put("jssk_mtsh_zaiko_urg_12_tskm", "3月(実績見通し)_総売上高(在庫)");
        mapping.put("jssk_mtsh_chks_urg_12_tskm", "3月(実績見通し)_総売上高(直送)");
        mapping.put("jssk_mtsh_zaiko_hmpnt_12_tskm", "3月(実績見通し)_返品(在庫)");
        mapping.put("jssk_mtsh_chks_hmpnt_12_tskm", "3月(実績見通し)_返品(直送)");
        mapping.put("jssk_mtsh_zaiko_shhr_rbt_12_tskm", "3月(実績見通し)_リベート(在庫)");
        mapping.put("jssk_mtsh_chks_shhr_rbt_12_tskm", "3月(実績見通し)_リベート(直送)");
        mapping.put("jssk_mtsh_zaiko_cntr_fee_12_tskm", "3月(実績見通し)_センターフィ(在庫)");
        mapping.put("jssk_mtsh_chks_cntr_fee_12_tskm", "3月(実績見通し)_センターフィ(直送)");
        mapping.put("jssk_mtsh_zaiko_rieki_12_tskm", "3月(実績見通し)_直接利益(在庫)");
        mapping.put("jssk_mtsh_chks_rieki_12_tskm", "3月(実績見通し)_直接利益(直送)");
        mapping.put("file_info_1", "ファイル情報1");
        mapping.put("file_info_2", "ファイル情報2");
        return mapping;
    }

    /**
     * エリア 見通し・計画_企業別用フィールドヘッダーマッピングを作成
     * データベース列名から日本語ヘッダー名への変換マップ
     */
    private Map<String, String> createCompanyFieldHeaderMapping() {
        Map<String, String> mapping = new HashMap<>();
        mapping.put("no", "No");
        mapping.put("group_code", "ｸﾞﾙｰﾌﾟCD");
        mapping.put("kigyo_code", "企業CD");
        mapping.put("kgym_kanji", "企業名");
        mapping.put("ctgry_mei_kanji", "ｶﾃｺﾞﾘ");
        mapping.put("hnkg_trkm_kubun", "変更後取組区分");
        mapping.put("gyt_hrts", "業態比率");
        mapping.put("total_kkk_zaiko_urg_1_tskm", "4月(計画)_総売上高(在庫)");
        mapping.put("total_kkk_chks_urg_1_tskm", "4月(計画)_総売上高(直送)");
        mapping.put("total_kkk_zaiko_rieki_1_tskm", "4月(計画)_直接利益(在庫)");
        mapping.put("total_kkk_chks_rieki_1_tskm", "4月(計画)_直接利益(直送)");
        mapping.put("total_kkk_zaiko_urg_2_tskm", "5月(計画)_総売上高(在庫)");
        mapping.put("total_kkk_chks_urg_2_tskm", "5月(計画)_総売上高(直送)");
        mapping.put("total_kkk_zaiko_rieki_2_tskm", "5月(計画)_直接利益(在庫)");
        mapping.put("total_kkk_chks_rieki_2_tskm", "5月(計画)_直接利益(直送)");
        mapping.put("total_kkk_zaiko_urg_3_tskm", "6月(計画)_総売上高(在庫)");
        mapping.put("total_kkk_chks_urg_3_tskm", "6月(計画)_総売上高(直送)");
        mapping.put("total_kkk_zaiko_rieki_3_tskm", "6月(計画)_直接利益(在庫)");
        mapping.put("total_kkk_chks_rieki_3_tskm", "6月(計画)_直接利益(直送)");
        mapping.put("total_kkk_zaiko_urg_4_tskm", "7月(計画)_総売上高(在庫)");
        mapping.put("total_kkk_chks_urg_4_tskm", "7月(計画)_総売上高(直送)");
        mapping.put("total_kkk_zaiko_rieki_4_tskm", "7月(計画)_直接利益(在庫)");
        mapping.put("total_kkk_chks_rieki_4_tskm", "7月(計画)_直接利益(直送)");
        mapping.put("total_kkk_zaiko_urg_5_tskm", "8月(計画)_総売上高(在庫)");
        mapping.put("total_kkk_chks_urg_5_tskm", "8月(計画)_総売上高(直送)");
        mapping.put("total_kkk_zaiko_rieki_5_tskm", "8月(計画)_直接利益(在庫)");
        mapping.put("total_kkk_chks_rieki_5_tskm", "8月(計画)_直接利益(直送)");
        mapping.put("total_kkk_zaiko_urg_6_tskm", "9月(計画)_総売上高(在庫)");
        mapping.put("total_kkk_chks_urg_6_tskm", "9月(計画)_総売上高(直送)");
        mapping.put("total_kkk_zaiko_rieki_6_tskm", "9月(計画)_直接利益(在庫)");
        mapping.put("total_kkk_chks_rieki_6_tskm", "9月(計画)_直接利益(直送)");
        mapping.put("total_kkk_zaiko_urg_7_tskm", "10月(計画)_総売上高(在庫)");
        mapping.put("total_kkk_chks_urg_7_tskm", "10月(計画)_総売上高(直送)");
        mapping.put("total_kkk_zaiko_rieki_7_tskm", "10月(計画)_直接利益(在庫)");
        mapping.put("total_kkk_chks_rieki_7_tskm", "10月(計画)_直接利益(直送)");
        mapping.put("total_kkk_zaiko_urg_8_tskm", "11月(計画)_総売上高(在庫)");
        mapping.put("total_kkk_chks_urg_8_tskm", "11月(計画)_総売上高(直送)");
        mapping.put("total_kkk_zaiko_rieki_8_tskm", "11月(計画)_直接利益(在庫)");
        mapping.put("total_kkk_chks_rieki_8_tskm", "11月(計画)_直接利益(直送)");
        mapping.put("total_kkk_zaiko_urg_9_tskm", "12月(計画)_総売上高(在庫)");
        mapping.put("total_kkk_chks_urg_9_tskm", "12月(計画)_総売上高(直送)");
        mapping.put("total_kkk_zaiko_rieki_9_tskm", "12月(計画)_直接利益(在庫)");
        mapping.put("total_kkk_chks_rieki_9_tskm", "12月(計画)_直接利益(直送)");
        mapping.put("total_kkk_zaiko_urg_10_tskm", "1月(計画)_総売上高(在庫)");
        mapping.put("total_kkk_chks_urg_10_tskm", "1月(計画)_総売上高(直送)");
        mapping.put("total_kkk_zaiko_rieki_10_tskm", "1月(計画)_直接利益(在庫)");
        mapping.put("total_kkk_chks_rieki_10_tskm", "1月(計画)_直接利益(直送)");
        mapping.put("total_kkk_zaiko_urg_11_tskm", "2月(計画)_総売上高(在庫)");
        mapping.put("total_kkk_chks_urg_11_tskm", "2月(計画)_総売上高(直送)");
        mapping.put("total_kkk_zaiko_rieki_11_tskm", "2月(計画)_直接利益(在庫)");
        mapping.put("total_kkk_chks_rieki_11_tskm", "2月(計画)_直接利益(直送)");
        mapping.put("total_kkk_zaiko_urg_12_tskm", "3月(計画)_総売上高(在庫)");
        mapping.put("total_kkk_chks_urg_12_tskm", "3月(計画)_総売上高(直送)");
        mapping.put("total_kkk_zaiko_rieki_12_tskm", "3月(計画)_直接利益(在庫)");
        mapping.put("total_kkk_chks_rieki_12_tskm", "3月(計画)_直接利益(直送)");
        mapping.put("total_ssnkn_jssk_zaiko_urg_1_tskm", "4月(実績見通し)_総売上高(在庫)");
        mapping.put("total_ssnkn_jssk_chks_urg_1_tskm", "4月(実績見通し)_総売上高(直送)");
        mapping.put("total_ssnkn_jssk_zaiko_rieki_1_tskm", "4月(実績見通し)_直接利益(在庫)");
        mapping.put("total_ssnkn_jssk_chks_rieki_1_tskm", "4月(実績見通し)_直接利益(直送)");
        mapping.put("total_ssnkn_jssk_zaiko_urg_2_tskm", "5月(実績見通し)_総売上高(在庫)");
        mapping.put("total_ssnkn_jssk_chks_urg_2_tskm", "5月(実績見通し)_総売上高(直送)");
        mapping.put("total_ssnkn_jssk_zaiko_rieki_2_tskm", "5月(実績見通し)_直接利益(在庫)");
        mapping.put("total_ssnkn_jssk_chks_rieki_2_tskm", "5月(実績見通し)_直接利益(直送)");
        mapping.put("total_ssnkn_jssk_zaiko_urg_3_tskm", "6月(実績見通し)_総売上高(在庫)");
        mapping.put("total_ssnkn_jssk_chks_urg_3_tskm", "6月(実績見通し)_総売上高(直送)");
        mapping.put("total_ssnkn_jssk_zaiko_rieki_3_tskm", "6月(実績見通し)_直接利益(在庫)");
        mapping.put("total_ssnkn_jssk_chks_rieki_3_tskm", "6月(実績見通し)_直接利益(直送)");
        mapping.put("total_ssnkn_jssk_zaiko_urg_4_tskm", "7月(実績見通し)_総売上高(在庫)");
        mapping.put("total_ssnkn_jssk_chks_urg_4_tskm", "7月(実績見通し)_総売上高(直送)");
        mapping.put("total_ssnkn_jssk_zaiko_rieki_4_tskm", "7月(実績見通し)_直接利益(在庫)");
        mapping.put("total_ssnkn_jssk_chks_rieki_4_tskm", "7月(実績見通し)_直接利益(直送)");
        mapping.put("total_ssnkn_jssk_zaiko_urg_5_tskm", "8月(実績見通し)_総売上高(在庫)");
        mapping.put("total_ssnkn_jssk_chks_urg_5_tskm", "8月(実績見通し)_総売上高(直送)");
        mapping.put("total_ssnkn_jssk_zaiko_rieki_5_tskm", "8月(実績見通し)_直接利益(在庫)");
        mapping.put("total_ssnkn_jssk_chks_rieki_5_tskm", "8月(実績見通し)_直接利益(直送)");
        mapping.put("total_ssnkn_jssk_zaiko_urg_6_tskm", "9月(実績見通し)_総売上高(在庫)");
        mapping.put("total_ssnkn_jssk_chks_urg_6_tskm", "9月(実績見通し)_総売上高(直送)");
        mapping.put("total_ssnkn_jssk_zaiko_rieki_6_tskm", "9月(実績見通し)_直接利益(在庫)");
        mapping.put("total_ssnkn_jssk_chks_rieki_6_tskm", "9月(実績見通し)_直接利益(直送)");
        mapping.put("total_ssnkn_jssk_zaiko_urg_7_tskm", "10月(実績見通し)_総売上高(在庫)");
        mapping.put("total_ssnkn_jssk_chks_urg_7_tskm", "10月(実績見通し)_総売上高(直送)");
        mapping.put("total_ssnkn_jssk_zaiko_rieki_7_tskm", "10月(実績見通し)_直接利益(在庫)");
        mapping.put("total_ssnkn_jssk_chks_rieki_7_tskm", "10月(実績見通し)_直接利益(直送)");
        mapping.put("total_ssnkn_jssk_zaiko_urg_8_tskm", "11月(実績見通し)_総売上高(在庫)");
        mapping.put("total_ssnkn_jssk_chks_urg_8_tskm", "11月(実績見通し)_総売上高(直送)");
        mapping.put("total_ssnkn_jssk_zaiko_rieki_8_tskm", "11月(実績見通し)_直接利益(在庫)");
        mapping.put("total_ssnkn_jssk_chks_rieki_8_tskm", "11月(実績見通し)_直接利益(直送)");
        mapping.put("total_ssnkn_jssk_zaiko_urg_9_tskm", "12月(実績見通し)_総売上高(在庫)");
        mapping.put("total_ssnkn_jssk_chks_urg_9_tskm", "12月(実績見通し)_総売上高(直送)");
        mapping.put("total_ssnkn_jssk_zaiko_rieki_9_tskm", "12月(実績見通し)_直接利益(在庫)");
        mapping.put("total_ssnkn_jssk_chks_rieki_9_tskm", "12月(実績見通し)_直接利益(直送)");
        mapping.put("total_jssk_mtsh_zaiko_urg_10_tskm", "1月(実績見通し)_総売上高(在庫)");
        mapping.put("total_jssk_mtsh_chks_urg_10_tskm", "1月(実績見通し)_総売上高(直送)");
        mapping.put("total_jssk_mtsh_zaiko_rieki_10_tskm", "1月(実績見通し)_直接利益(在庫)");
        mapping.put("total_jssk_mtsh_chks_rieki_10_tskm", "1月(実績見通し)_直接利益(直送)");
        mapping.put("total_jssk_mtsh_zaiko_urg_11_tskm", "2月(実績見通し)_総売上高(在庫)");
        mapping.put("total_jssk_mtsh_chks_urg_11_tskm", "2月(実績見通し)_総売上高(直送)");
        mapping.put("total_jssk_mtsh_zaiko_rieki_11_tskm", "2月(実績見通し)_直接利益(在庫)");
        mapping.put("total_jssk_mtsh_chks_rieki_11_tskm", "2月(実績見通し)_直接利益(直送)");
        mapping.put("total_jssk_mtsh_zaiko_urg_12_tskm", "3月(実績見通し)_総売上高(在庫)");
        mapping.put("total_jssk_mtsh_chks_urg_12_tskm", "3月(実績見通し)_総売上高(直送)");
        mapping.put("total_jssk_mtsh_zaiko_rieki_12_tskm", "3月(実績見通し)_直接利益(在庫)");
        mapping.put("total_jssk_mtsh_chks_rieki_12_tskm", "3月(実績見通し)_直接利益(直送)");
        mapping.put("file_info_1", "ファイル情報1");
        mapping.put("file_info_2", "ファイル情報2");
        return mapping;
    }
}
