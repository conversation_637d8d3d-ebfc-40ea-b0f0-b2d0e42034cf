# Contributing to <PERSON><PERSON> Researcher

First off, we'd like to welcome you and thank you for your interest and effort in contributing to our open-source project ❤️. Contributions of all forms are welcome—from new features and bug fixes to documentation and more.

We are on a mission to build the #1 AI agent for comprehensive, unbiased, and factual research online, and we need your support to achieve this grand vision.

Please take a moment to review this document to make the contribution process easy and effective for everyone involved.

## Reporting Issues

If you come across any issue or have an idea for an improvement, don't hesitate to create an issue on GitHub. Describe your problem in sufficient detail, providing as much relevant information as possible. This way, we can reproduce the issue before attempting to fix it or respond appropriately.

## Contributing Code

1. **Fork the repository and create your branch from `master`.**  
   If it’s not an urgent bug fix, branch from `master` and work on the feature or fix there.

2. **Make your changes.**  
   Implement your changes following best practices for coding in the project's language.

3. **Test your changes.**  
   Ensure that your changes pass all tests if any exist. If the project doesn’t have automated tests, test your changes manually to confirm they behave as expected.

4. **Follow the coding style.**  
   Ensure your code adheres to the coding conventions used throughout the project, including indentation, accurate comments, etc.

5. **Commit your changes.**  
   Make your Git commits informative and concise. This is very helpful for others when they look at the Git log.

6. **Push to your fork and submit a pull request.**  
   When your work is ready and passes tests, push your branch to your fork of the repository and submit a pull request from there.

7. **Pat yourself on the back and wait for review.**  
   Your work is done, congratulations! Now sit tight. The project maintainers will review your submission as soon as possible. They might suggest changes or ask for improvements. Both constructive conversation and patience are key to the collaboration process.

## Documentation

If you would like to contribute to the project's documentation, please follow the same steps: fork the repository, make your changes, test them, and submit a pull request.

Documentation is a vital part of any software. It's not just about having good code; ensuring that users and contributors understand what's going on, how to use the software, or how to contribute is crucial.

We're grateful for all our contributors, and we look forward to building the world's leading AI research agent hand-in-hand with you. Let's harness the power of open source and AI to change the world together!
