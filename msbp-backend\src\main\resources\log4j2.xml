<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Appenders>
        <Lambda name="Lambda" format="${sys:aws.lambda.log.format:-${env:AWS_LAMBDA_LOG_FORMAT:-TEXT}}">
            <LambdaTextFormat>
                <PatternLayout>
                    <pattern>%d{yyyy-MM-dd HH:mm:ss} %X{AWSRequestId} %-5p %c{1} - %m%n</pattern>
                </PatternLayout>
            </LambdaTextFormat>
            <LambdaJSONFormat>
                <JsonTemplateLayout eventTemplateUri="classpath:LambdaLayout.json" />
            </LambdaJSONFormat>
        </Lambda>
    </Appenders>
    <Loggers>
        <Root level="${sys:aws.lambda.log.level:-${env:AWS_LAMBDA_LOG_LEVEL:-DEBUG}}">
            <AppenderRef ref="Lambda"/>
        </Root>
        <!-- 特定クラス/パッケージのログレベル設定 -->
        <Logger name="software.amazon.awssdk" level="INFO" />
<!--        <Logger name="software.amazon.awssdk.request" level="DEBUG" />-->
        <Logger name="com.ms.bp.shared.common.config" level="INFO" />

    </Loggers>
</Configuration>