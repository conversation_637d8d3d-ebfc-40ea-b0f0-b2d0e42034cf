package com.ms.bp.application;

import com.ms.bp.application.dto.ImportJobResult;
import com.ms.bp.domain.file.model.ExportJobStatus;
import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.shared.util.LambdaResourceManager;
import com.ms.bp.domain.file.repository.ImportJobStatusRepository;
import com.ms.bp.domain.file.model.ImportJobStatus;
import com.ms.bp.interfaces.dto.response.ImportHistoryResponse;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.util.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * インポートジョブステータスサービス
 * インポートジョブの状態管理に関するビジネスロジックを提供
 */
public class ImportJobStatusService {
    private static final Logger logger = LoggerFactory.getLogger(ImportJobStatusService.class);


    
    /**
     * 新しいインポートジョブを作成し、自動生成された履歴番号を返す
     * @param jobStatus インポートジョブステータス
     * @return 自動生成された履歴番号
     */
    public Long createJob(ImportJobStatus jobStatus) {
        return LambdaResourceManager.executeWithTransaction(serviceFactory -> {
            ImportJobStatusRepository repository = serviceFactory.getImportJobStatusRepository();

            // データベースに挿入（自動生成された履歴番号がjobStatusに設定される）
            repository.save(jobStatus);

            // 自動生成された履歴番号を返す
            return jobStatus.getRrkBango();
        });
    }

    /**
     * ジョブステータスを更新（統合メソッド）
     * 新しいステータス別更新ルールに対応
     * @param rrkBango 履歴番号
     * @param status ステータス（1:完了、3:失敗、4:システムエラー）
     * @param result 処理結果データ（エラーファイル名を含む）
     */
    public void updateJob(Long rrkBango, String status, ImportJobResult result) {
        LambdaResourceManager.executeWithTransaction(serviceFactory -> {
            ImportJobStatusRepository repository = serviceFactory.getImportJobStatusRepository();

            // 既存のジョブステータスを取得
            ImportJobStatus jobStatus = repository.findByRrkBango(rrkBango);
            if (jobStatus == null) {
                logger.warn("ジョブステータスが見つかりません: rrkBango={}", rrkBango);
                return null;
            }
            String errorFileName = (result != null) ? result.getErrorFileS3Key() : null;
            // ステータス別の更新処理
            switch (status) {
                case BusinessConstants.BATCH_STATUS_COMPLETED_CODE: // 完了（ステータス=1）
                    // 要件：アップロード完了日時=システム日時、エラーファイル名=null
                    jobStatus.updateCompletionStatus();
                    logger.info("インポートジョブが完了しました: rrkBango={}", rrkBango);
                    break;

                case BusinessConstants.BATCH_STATUS_FAILED_CODE: // 失敗（ステータス=3）
                    // 要件：アップロード完了日時=null、エラーファイル名=エラーファイル名、レコード更新日時=システム日時
                    jobStatus.updateFailureStatus(errorFileName);
                    logger.warn("インポートジョブが失敗しました: rrkBango={}, errorFileName={}",
                               rrkBango, errorFileName);
                    break;

                case BusinessConstants.BATCH_STATUS_SYSTEM_ERROR_CODE: // システムエラー（ステータス=4）
                    // 要件：アップロード完了日時=null、エラーファイル名=条件付き設定、レコード更新日時=システム日時
                    jobStatus.updateSystemErrorStatus(errorFileName);
                    logger.error("インポートジョブでシステムエラーが発生しました: rrkBango={}, errorFileName={}",
                                rrkBango, errorFileName);
                    break;
            }

            // データベースを更新
            repository.update(jobStatus);

            logger.debug("インポートジョブステータスを更新しました: rrkBango={}, status={}", rrkBango, status);

            return null;
        });
    }

    /**
     * ジョブステータスを更新（統合メソッド）
     * @param rrkBango 履歴番号
     */
    public void updateJobEmergency(Long rrkBango) {
        LambdaResourceManager.executeWithTransactionEmergency(serviceFactory -> {
            ImportJobStatusRepository repository = serviceFactory.getImportJobStatusRepository();

            // 既存のジョブステータスを取得
            ImportJobStatus jobStatus = repository.findByRrkBango(rrkBango);
            if (jobStatus == null) {
                logger.warn("ジョブステータスが見つかりません: rrkBango={}", rrkBango);
                return null;
            }
            // システムエラー
            jobStatus.updateSystemErrorStatus(null);
            logger.error("エクスポートジョブをシステムエラー状態に更新しました: rrkBango={}", rrkBango);

            // データベースを更新
            repository.update(jobStatus);
            logger.debug("エクスポートジョブステータスを更新しました: rrkBango={}, status={}", rrkBango, jobStatus.getStts());

            return null;
        });
    }

    /**
     * 履歴番号でインポートジョブステータスを取得
     * @param rrkBango 履歴番号
     * @return インポートジョブステータス
     */
    public ImportJobStatus getJobStatus(Long rrkBango) {
        return LambdaResourceManager.executeReadOnly(serviceFactory -> {
            ImportJobStatusRepository repository = serviceFactory.getImportJobStatusRepository();
            return repository.findByRrkBango(rrkBango);
        });
    }

    /**
     * インポート履歴リストを取得（最新20件）
     * M_SOSHIKIAREAMSTテーブルと連表查询してエリア名も一緒に取得し、
     * SQL内でCASE WHEN文によりファイル種別・ステータス変換を実行して返却する
     *
     * @param shainCode 社員コード
     * @return インポート履歴リスト（最新20件）
     */
    public List<ImportHistoryResponse> getImportHistoryList(String shainCode,String systemOperationCompanyCode) {
        return LambdaResourceManager.executeReadOnly(serviceFactory -> {
            ImportJobStatusRepository repository = serviceFactory.getImportJobStatusRepository();

            // 最新20件のインポートジョブステータスを取得（SQL内でファイル種別・ステータス変換済み）
            List<ImportJobStatus> jobStatusList = repository.findByShainCode(
                shainCode, systemOperationCompanyCode, BusinessConstants.DEFAULT_HISTORY_LIMIT, 0);

            if (CollectionUtils.isNotEmpty(jobStatusList)) {
                //エリアコードを取得
                String areaCodes = jobStatusList.stream()
                        .map(ImportJobStatus::getArea)
                        .flatMap(area -> Arrays.stream(area.split(",")))
                        .distinct()
                        .collect(Collectors.joining(","));

                // エリアコードマッピングを取得
                Map<String, String> areaMap = serviceFactory.getGroupAreaRepository()
                        .findAreaInfosByAreaCodes(areaCodes)
                        .stream()
                        .collect(Collectors.toMap(
                                AreaInfo::getAreaCode,
                                AreaInfo::getAreaName,
                                (existing, replacement) -> existing
                        ));

                // 各ジョブステータスのエリア名を更新
                jobStatusList.stream()
                        .filter(jobStatus -> StringUtils.isNotEmpty(jobStatus.getArea()))
                        .forEach(jobStatus -> {
                            String areaNames = Arrays.stream(jobStatus.getArea().split(","))
                                    .map(code -> {
                                        if ("SKSA".equals(code)) {
                                            return "採算管理単位計画策定エリア";
                                        } else {
                                            return areaMap.getOrDefault(code.trim(), "");
                                        }
                                    })
                                    .collect(Collectors.joining(","));
                            jobStatus.setAreaName(areaNames);
                        });
            }


            // ImportHistoryResponseに変換
            return jobStatusList.stream()
                .map(this::convertToImportHistoryResponse)
                .collect(Collectors.toList());
        });
    }


    /**
     * ImportJobStatusをImportHistoryResponseに変換（SQL内で全変換済み）
     * SQL内でCASE WHEN文により変換されたファイル種別、エリア名、ステータスを直接使用
     *
     * @param jobStatus インポートジョブステータス（SQL内で全変換済み）
     * @return インポート履歴レスポンス
     */
    private ImportHistoryResponse convertToImportHistoryResponse(ImportJobStatus jobStatus) {
        return ImportHistoryResponse.builder()
            .historyNumber(jobStatus.getRrkBango())
            .fileType(jobStatus.getFileShbtsName()) // SQL内で変換済み
            .area(jobStatus.getAreaName()) // SQL内で変換済み
            .fileName(jobStatus.getFileMei())
            .errorFileName(jobStatus.getErrorFileMei())
            .uploadStartDateTime(DateUtil.parseDateTime(jobStatus.getUploadKshNchj()))
            .uploadCompleteDateTime(DateUtil.parseDateTime(jobStatus.getUploadKnrNchj()))
            .status(jobStatus.getSttsName()) // SQL内で変換済み
            .build();
    }

}
