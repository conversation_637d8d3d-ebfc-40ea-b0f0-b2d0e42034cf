package com.ms.bp.shared.security;

import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;

/**
 * セキュアトークン生成ユーティリティクラス
 * アクティベーションやパスワードリセット用のトークンを生成する
 */
public class TokenGenerator {
    private static final Logger logger = LoggerFactory.getLogger(TokenGenerator.class);
    
    // トークンの長さ（バイト）
    private static final int TOKEN_LENGTH = 32;
    
    // SecureRandomインスタンス
    private static final SecureRandom secureRandom = new SecureRandom();
    
    /**
     * セキュアなランダムトークンを生成する
     * @return Base64エンコードされたトークン
     */
    public static String generateSecureToken() {
        byte[] tokenBytes = new byte[TOKEN_LENGTH];
        secureRandom.nextBytes(tokenBytes);
        
        String token = Base64.getUrlEncoder().withoutPadding().encodeToString(tokenBytes);
        logger.debug("セキュアトークンを生成しました（長さ: {}）", token.length());
        
        return token;
    }
    
    /**
     * ユーザーアクティベーション用トークンを生成する
     * @param userId ユーザーID
     * @return アクティベーショントークン
     */
    public static String generateActivationToken(Long userId) {
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("無効なユーザーIDです");
        }
        
        String baseToken = generateSecureToken();
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        
        // フォーマット: {timestamp}_{userId}_{randomToken}
        String activationToken = timestamp + "_" + userId + "_" + baseToken;
        
        logger.info("ユーザーID {} のアクティベーショントークンを生成しました", userId);
        return activationToken;
    }
    
    /**
     * パスワードリセット用トークンを生成する
     * @param userId ユーザーID
     * @return パスワードリセットトークン
     */
    public static String generatePasswordResetToken(Long userId) {
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("無効なユーザーIDです");
        }
        
        String baseToken = generateSecureToken();
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        
        // フォーマット: PWD_{timestamp}_{userId}_{randomToken}
        String resetToken = "PWD_" + timestamp + "_" + userId + "_" + baseToken;
        
        logger.info("ユーザーID {} のパスワードリセットトークンを生成しました", userId);
        return resetToken;
    }
    
    /**
     * トークンからユーザーIDを抽出する
     * @param token トークン
     * @return ユーザーID（抽出できない場合はnull）
     */
    public static Long extractUserIdFromToken(String token) {
        if (token == null || token.isEmpty()) {
            return null;
        }
        
        try {
            // アクティベーショントークンの場合: {timestamp}_{userId}_{randomToken}
            // パスワードリセットトークンの場合: PWD_{timestamp}_{userId}_{randomToken}
            String[] parts = token.split("_");
            
            if (parts.length >= 3) {
                // パスワードリセットトークンの場合
                if (token.startsWith("PWD_")) {
                    return Long.parseLong(parts[2]);
                } else {
                    // アクティベーショントークンの場合
                    return Long.parseLong(parts[1]);
                }
            }
            
            return null;
            
        } catch (NumberFormatException e) {
            logger.warn("トークンからユーザーIDを抽出できませんでした: {}", token);
            return null;
        }
    }
    
    /**
     * トークンの形式を検証する
     * @param token 検証対象のトークン
     * @param tokenType トークンタイプ
     * @return 検証結果
     */
    public static boolean validateTokenFormat(String token, TokenType tokenType) {
        if (token == null || token.isEmpty()) {
            return false;
        }
        
        String[] parts = token.split("_");

        return switch (tokenType) {
            case ACTIVATION ->
                // アクティベーショントークン: {timestamp}_{userId}_{randomToken}
                    parts.length == 3 && !token.startsWith("PWD_");
            case PASSWORD_RESET ->
                // パスワードリセットトークン: PWD_{timestamp}_{userId}_{randomToken}
                    parts.length == 4 && token.startsWith("PWD_");
            default -> false;
        };
    }
    
    /**
     * トークンタイプの列挙型
     */
    @Getter
    public enum TokenType {
        ACTIVATION("アクティベーション"),
        PASSWORD_RESET("パスワードリセット");
        
        private final String description;
        
        TokenType(String description) {
            this.description = description;
        }

    }
}
