package com.ms.bp.shared.common.exception;

import lombok.Getter;

/**
 * エラーコードオブジェクト
 * グローバルエラーコード
 * ビジネス例外エラーコードは [1 000 000 000, +∞) の範囲を占める
 */
@Getter
public class Message {

    /**
     * エラーコード
     */
    private final Integer code;
    /**
     * エラーメッセージ
     */
    private final String msg;

    public Message(Integer code, String message) {
        this.code = code;
        this.msg = message;
    }

}