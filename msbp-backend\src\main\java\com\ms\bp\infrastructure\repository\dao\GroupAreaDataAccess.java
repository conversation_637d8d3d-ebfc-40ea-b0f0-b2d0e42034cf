package com.ms.bp.infrastructure.repository.dao;

import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 組織エリアマスタデータアクセス実装
 * M_SOSHIKIAREAMST（組織エリアマスタ）への具体的なデータアクセスを実装
 */
public class GroupAreaDataAccess {
    private static final Logger logger = LoggerFactory.getLogger(GroupAreaDataAccess.class);

    // SQL定数
    private static final String EXISTS_BY_AREA_CODE_SQL = """
        SELECT 1
        FROM M_SOSHIKIAREAMST
        WHERE AREA_CODE = ?
        LIMIT 1
        """;

    private final JdbcTemplate jdbcTemplate;

    public GroupAreaDataAccess(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * エリアコードで組織エリアマスタの存在チェック
     *
     * @param areaCode エリアコード（4桁）
     * @return 存在する場合true、存在しない場合false
     * @throws SQLException データベースアクセスエラー
     */
    public boolean existsByAreaCode(String areaCode) throws SQLException {
        logger.debug("組織エリアマスタ存在チェック実行: エリアコード={}", areaCode);

        Object[] params = {areaCode};

        var results = jdbcTemplate.query(EXISTS_BY_AREA_CODE_SQL, params, rs -> rs.getInt(1));

        boolean exists = !results.isEmpty();
        logger.debug("組織エリアマスタ存在チェック結果: エリアコード={}, 存在={}", areaCode, exists);

        return exists;
    }

    /**
     * エリアコードでエリア情報リストを取得
     * M_SOSHIKIAREAMSTを関連付けてエリアコードとエリア名称を取得
     *
     * @param areaCodes エリアコード
     * @return エリア情報リスト（エリア表示順でソート済み）
     * @throws SQLException データベースアクセスエラー
     */
    public List<AreaInfo> findAreaInfosByAreaCodes(String areaCodes) throws SQLException {
        String sql = """
                    SELECT
                        area_code,
                        area_mei_tnshk_kanji
                    FROM(
                        SELECT
                            area_code,
                            area_mei_tnshk_kanji,
                            ROW_NUMBER() OVER (PARTITION BY area_code ORDER BY kshb DESC,sub_area_code) AS rn
                        FROM
                             M_SOSHIKIAREAMST
                        WHERE
                             %s
                            -- 現在有効で、かつ使用禁止でないレコードを対象にする
                            AND CURRENT_DATE::CHAR(8) >= kshb
                            AND CURRENT_DATE::CHAR(8) <= shryb
                            AND shiyo_knsh_kubun = '0'
                        ) area_info
                    WHERE rn = 1;
                """;

        List<String> areaCodeList = Arrays.stream(areaCodes.split(","))
                .map(String::trim)
                .distinct()
                .toList();
        logger.debug("エリア情報取得SQL実行: エリアコード={}", areaCodeList);

        String areaStr = String.join(",", Collections.nCopies(areaCodeList.size(), "?"));
        String areaCondition = String.format(" area_code IN (%s)",areaStr);
        sql = String.format(sql,areaCondition);

        Object[] params = areaCodeList.toArray();

        try {
            List<AreaInfo> areaInfos = jdbcTemplate.query(sql, params, rs -> {
                String areaCode = rs.getString("area_code");
                String areaTnshkName = rs.getString("area_mei_tnshk_kanji");
                return new AreaInfo(areaCode, areaTnshkName);
            });

            logger.debug("エリア情報取得結果: 件数={}", areaInfos.size());

            if (areaInfos.isEmpty()) {
                logger.warn("M_SOSHIKIAREAMSTテーブルからエリア情報が取得できませんでした: エリアコード={}", areaCodeList);
            }

            return areaInfos;

        } catch (SQLException e) {
            logger.error("エリア情報取得エラー: SQL={}, エリアコード={}", sql, areaCodeList, e);
            throw e;
        }
    }

}
