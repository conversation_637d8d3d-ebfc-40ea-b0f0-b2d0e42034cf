package com.ms.bp.shared.common.io.factory;

import com.ms.bp.shared.common.io.model.FileFormat;
import com.ms.bp.shared.common.io.strategy.DataWriteStrategy;
import com.ms.bp.shared.common.io.strategy.impl.CsvWriteStrategy;

/**
 * データ書き込み戦略ファクトリー
 * （ファクトリーパターン：具体的なデータ書き込み戦略を作成）
 */
public class DataWriteStrategyFactory {
    /**
     * CSV書き込み戦略を作成
     *
     * @param format ファイル形式
     * @return データ書き込み戦略
     */
    public static <T> DataWriteStrategy<T> createStrategy(FileFormat format) {
        // CSVのみをサポート
        if (format != FileFormat.CSV) {
            throw new IllegalArgumentException("サポートされていないファイル形式: " + format);
        }
        return new CsvWriteStrategy<>();
    }
}