package com.ms.bp.infrastructure.repository.impl;

import com.ms.bp.domain.concurrent.model.ConcurrentJobInfo;
import com.ms.bp.domain.concurrent.repository.ConcurrentJobRepository;
import com.ms.bp.infrastructure.repository.dao.ConcurrentJobDataAccess;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.List;

/**
 * 兼務マスタリポジトリ実装クラス
 * 領域インターフェースと基盤設備層DAOの橋渡しを行う
 * JDK21のvar、switch expression等を活用した実装
 */
public class ConcurrentJobRepositoryImpl implements ConcurrentJobRepository {
    private static final Logger logger = LoggerFactory.getLogger(ConcurrentJobRepositoryImpl.class);

    private final ConcurrentJobDataAccess dataAccess;

    public ConcurrentJobRepositoryImpl(ConcurrentJobDataAccess dataAccess) {
        this.dataAccess = dataAccess;
    }

    @Override
    public List<ConcurrentJobInfo> findValidConcurrentJobs(String shainCode, String systemOperationCompanyCode) {
        try {
            logger.debug("有効兼務情報取得開始: shainCode={}, systemOperationCompanyCode={}", 
                        shainCode, systemOperationCompanyCode);
            
            var result = dataAccess.findValidConcurrentJobs(shainCode, systemOperationCompanyCode);
            
            // JDK21: Switch Expressionでログレベル判定
            var logMessage = switch (result.size()) {
                case 0 -> "兼務情報が見つかりませんでした";
                case 1 -> "1件の兼務情報を取得しました";  
                default -> STR."\{result.size()}件の兼務情報を取得しました";
            };
            
            logger.debug("有効兼務情報取得完了: {}", logMessage);
            return result;
            
        } catch (SQLException e) {
            logger.error("有効兼務情報取得中にエラーが発生しました: shainCode={}, systemOperationCompanyCode={}", 
                        shainCode, systemOperationCompanyCode, e);
            throw new RuntimeException("有効兼務情報取得処理でエラーが発生しました", e);
        }
    }

    @Override
    public List<ConcurrentJobInfo> findValidConcurrentJobsWithAreaCode(String shainCode, String systemOperationCompanyCode) {
        try {
            logger.debug("有効兼務情報（エリアコード込み）取得開始: shainCode={}, systemOperationCompanyCode={}", 
                        shainCode, systemOperationCompanyCode);
            
            var result = dataAccess.findValidConcurrentJobsWithAreaCode(shainCode, systemOperationCompanyCode);
            
            // JDK21: Switch Expressionでログレベル判定とエリアコード検証
            var validResults = result.stream()
                .filter(job -> job.getAreaCode() != null && !job.getAreaCode().trim().isEmpty())
                .toList();
            
            var logMessage = switch (validResults.size()) {
                case 0 -> "有効なエリアコード付き兼務情報が見つかりませんでした";
                case 1 -> "1件の有効なエリアコード付き兼務情報を取得しました";  
                default -> STR."\{validResults.size()}件の有効なエリアコード付き兼務情報を取得しました";
            };
            
            if (result.size() != validResults.size()) {
                logger.warn("エリアコード取得失敗: 全体{}件中、有効{}件", result.size(), validResults.size());
            }
            
            logger.debug("有効兼務情報（エリアコード込み）取得完了: {}", logMessage);
            return validResults;
            
        } catch (SQLException e) {
            logger.error("有効兼務情報（エリアコード込み）取得中にエラーが発生しました: shainCode={}, systemOperationCompanyCode={}", 
                        shainCode, systemOperationCompanyCode, e);
            throw new RuntimeException("有効兼務情報（エリアコード込み）取得処理でエラーが発生しました", e);
        }
    }

    @Override
    public List<ConcurrentJobInfo> findAllConcurrentJobs(String shainCode, String systemOperationCompanyCode) {
        try {
            logger.debug("全兼務情報取得開始: shainCode={}, systemOperationCompanyCode={}", 
                        shainCode, systemOperationCompanyCode);
            
            var result = dataAccess.findAllConcurrentJobs(shainCode, systemOperationCompanyCode);
            
            logger.debug("全兼務情報取得完了: 取得件数={}", result.size());
            return result;
            
        } catch (SQLException e) {
            logger.error("全兼務情報取得中にエラーが発生しました: shainCode={}, systemOperationCompanyCode={}", 
                        shainCode, systemOperationCompanyCode, e);
            throw new RuntimeException("全兼務情報取得処理でエラーが発生しました", e);
        }
    }
}