package com.ms.bp.application.data;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.application.data.strategy.FileSplitStrategy;
import com.ms.bp.domain.file.areaoutlookplan.AreaOutlookPlanExportService;
import com.ms.bp.domain.file.base.AbstractExportService;
import com.ms.bp.domain.file.headofficeoutlookplan.HeadOfficeOutlookPlanExportService;
import com.ms.bp.domain.file.planmaster.PlanMasterExportService;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.infrastructure.file.FileProcessingService;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.io.model.ExportResult;
import com.ms.bp.shared.util.DateUtil;
import com.ms.bp.shared.util.ZipUtil;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static com.ms.bp.shared.util.MessageCodeUtil.formatMessage;

/**
 * ファイルエクスポート協調器
 * エクスポート処理のビジネスロジック協調を担当
 */
public class FileExportOrchestrator {
    private static final Logger logger = LoggerFactory.getLogger(FileExportOrchestrator.class);

    private final FileProcessingService fileProcessingService;
    private final Map<String, AbstractExportService<?>> exportServices;

    public FileExportOrchestrator() {
        this.fileProcessingService = new FileProcessingService();

        // エクスポートサービスの登録
        this.exportServices = new HashMap<>();
        // TODO 今後の拡張:
        this.exportServices.put(BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE, new PlanMasterExportService());
        this.exportServices.put(BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE, new HeadOfficeOutlookPlanExportService());
        this.exportServices.put(BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE, new AreaOutlookPlanExportService());
    }

    /**
     * エクスポート処理を実行
     * @param jobId ジョブID
     * @param exportRequest エクスポートリクエスト
     * @param userInfo ユーザー情報
     * @param context Lambda実行コンテキスト
     * @return エクスポート結果
     */
    public ExportProcessResult processExport(String jobId, ExportRequest exportRequest, UserInfo userInfo, Context context) {
        try {
            logger.info("エクスポート協調処理開始: jobId={}", jobId);

            // エクスポートサービス取得
            AbstractExportService<?> exportService = getExportService(exportRequest.getDataType());
            exportService.setLambdaContext(context);

            // ビジネスロジック: ファイル分割戦略の決定
            List<ExportTask> exportTasks = determineExportStrategy(exportRequest, userInfo, exportService);

            if (CollectionUtils.isEmpty(exportTasks)) {
                throw new ServiceException(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                        "エクスポートファイルがありません: " + exportRequest);
            }
            // CSV生成の協調
            CSVGenerationResult csvResult = coordinateCSVGeneration(jobId, exportTasks, exportService, userInfo);

            // ZIP作成の協調
            Path zipFile = coordinateZipCreation(csvResult.csvFiles());

            logger.info("エクスポート協調処理完了: jobId={}", jobId);
            return new ExportProcessResult(zipFile, csvResult.csvFiles(), csvResult.exportResults());

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            logger.error("エクスポート協調処理エラー: jobId={}", jobId, e);
            throw new ServiceException(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(),
                    "エクスポート処理中にエラーが発生しました: " + e.getMessage());
        }
    }

    /**
     * S3アップロードを協調
     * データタイプに応じた専用ディレクトリにアップロード
     * @param exportResult エクスポート結果
     * @param jobId ジョブID
     * @param dataType データタイプ（機能識別用）
     * @return ダウンロードURL
     */
    public String coordinateS3Upload(ExportProcessResult exportResult, String jobId, String dataType) {
        return fileProcessingService.uploadToS3(exportResult.zipFile(), jobId, dataType);
    }

    // ==================== ビジネスロジック ====================

    /**
     * エクスポート戦略を決定（ビジネスロジック）
     * @param exportRequest エクスポートリクエスト
     * @param userInfo ユーザー情報
     * @param exportService エクスポートサービス
     * @return エクスポートタスクリスト
     */
    private List<ExportTask> determineExportStrategy(ExportRequest exportRequest, UserInfo userInfo, AbstractExportService<?> exportService) {
        // エクスポートサービスから専用の分割戦略を取得
        FileSplitStrategy strategy = exportService.getSplitStrategy();

        if (strategy == null) {
            logger.warn("分割戦略が設定されていません。デフォルト単一ファイル戦略を使用: データタイプ={}", exportRequest.getDataType());
            // デフォルト: 単一ファイルタスクを作成
            ExportTask defaultTask = new ExportTask("default", exportRequest, "DEFAULT");
            return List.of(defaultTask);
        }

        logger.debug("選択された分割戦略: {} (データタイプ: {})", strategy.getClass().getSimpleName(), exportRequest.getDataType());
        return strategy.createExportTasks(exportRequest, userInfo);
    }



    /**
     * CSV生成を協調
     */
    private CSVGenerationResult coordinateCSVGeneration(String jobId, List<ExportTask> exportTasks, AbstractExportService<?> exportService, UserInfo userInfo) {
        if (exportTasks.size() == 1) {
            // 単一タスクの場合 - スレッドプールを使用しない
            ExportTask task = exportTasks.getFirst();

            // タスクのファイル名を含むパラメータでエクスポート実行
            ExportResult result = executeExportWithFileName(exportService, task, userInfo);
            List<Path> csvFiles = result.getFilePath() != null ? List.of(result.getFilePath()) : Collections.emptyList();
            List<ExportResult> exportResults = List.of(result);
            logger.info(formatMessage(GlobalMessageConstants.INF_003, task.getFileName(), jobId));
            return new CSVGenerationResult(csvFiles, exportResults);
        } else {
            // 複数タスクの並列処理 - ファイル数に応じたスレッドプール
            logger.info("並列エクスポート実行: ファイル数={}", exportTasks.size());
            return executeParallelExport(jobId, exportTasks, exportService, userInfo);
        }
    }

    /**
     * 並列エクスポート実行
     * ファイル数に応じてスレッド数を動的調整（最大5個）
     */
    private CSVGenerationResult executeParallelExport(String jobId,List<ExportTask> exportTasks, AbstractExportService<?> exportService, UserInfo userInfo) {
        List<Path> tempFiles = Collections.synchronizedList(new ArrayList<>());
        List<ExportResult> exportResults = Collections.synchronizedList(new ArrayList<>());

        // スレッド数 = ファイル数、ただし最大5個まで
        int threadCount = Math.min(exportTasks.size(), 5);
        try (ExecutorService executorService = Executors.newFixedThreadPool(threadCount)) {

            logger.info("並列エクスポート開始: タスク数={}, スレッド数={}", exportTasks.size(), threadCount);

            try {
                List<CompletableFuture<Void>> futures = exportTasks.stream()
                        .map(task -> CompletableFuture.runAsync(() -> {
                            String threadName = Thread.currentThread().getName();
                            logger.info("エクスポートタスク実行開始: タスク={}, SQL戦略={}, スレッド={}",
                                       task.getName(), task.getSqlStrategyKey(), threadName);

                            try {
                                // タスクのファイル名を含むパラメータでエクスポート実行
                                ExportResult result = executeExportWithFileName(exportService, task, userInfo);

                                // ExportResultを収集
                                exportResults.add(result);

                                if (result.getFilePath() != null) {
                                    tempFiles.add(result.getFilePath());
                                    logger.info("エクスポートタスク完了: タスク={}, ファイル={}, スレッド={}",
                                            task.getName(), result.getFilePath().getFileName(), threadName);
                                } else {
                                    logger.warn("エクスポートタスクでファイルが生成されませんでした: タスク={}", task.getName());
                                }
                                logger.info(formatMessage(GlobalMessageConstants.INF_003, task.getFileName(), jobId));
                            } catch (Exception e) {
                                logger.error("エクスポートタスクエラー: タスク={}, スレッド={}", task.getName(), threadName, e);
                                throw e;
                            }
                        }, executorService)).toList();

                // すべてのタスクの完了を待機
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                logger.info("全エクスポートタスク完了: 生成ファイル数={}/{}", tempFiles.size(), exportTasks.size());
                return new CSVGenerationResult(tempFiles, exportResults);

            } finally {
                // スレッドプールのクリーンアップ
                executorService.shutdown();
                try {
                    if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                        logger.warn("スレッドプールの正常終了がタイムアウトしました。強制終了します。");
                        executorService.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    logger.warn("スレッドプール終了待機中に割り込まれました。強制終了します。");
                    executorService.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
        }
    }

    /**
     * ZIP作成を協調
     */
    private Path coordinateZipCreation(List<Path> csvFiles) {
        if (csvFiles.isEmpty()) {
            throw new ServiceException(GlobalMessageConstants.BAD_REQUEST.getCode(),
                    "エクスポート対象のデータが見つかりませんでした");
        }

        String zipFileName = generateZipFileName();
        return ZipUtil.createZip(csvFiles, zipFileName);
    }

    // ==================== ユーティリティメソッド ====================

    /**
     * ファイル名を指定してエクスポートを実行（全ExportService共通）
     * ExportTaskオブジェクトをパラメータに追加してタスク固有のヘッダー設定を可能にする
     * @param exportService エクスポートサービス
     * @param task エクスポートタスク
     * @param userInfo ユーザー情報
     * @return エクスポート結果
     */
    private ExportResult executeExportWithFileName(AbstractExportService<?> exportService, ExportTask task, UserInfo userInfo) {
        // パラメータにファイル名とExportTaskオブジェクトを追加
        Map<String, Object> params = new HashMap<>();
        params.put("sqlStrategyKey", task.getSqlStrategyKey());
        params.put("userInfo", userInfo);
        params.put("exportTask", task); // 新規追加：ExportTaskオブジェクトを渡してヘッダー設定に使用

        // ファイル名が指定されている場合は追加
        if (task.getFileName() != null && !task.getFileName().trim().isEmpty()) {
            params.put("fileName", task.getFileName());
            logger.debug("カスタムファイル名でエクスポート実行: タスク={}, ファイル名={}",
                        task.getName(), task.getFileName());
        }

        logger.debug("エクスポートタスク実行: タスク名={}, SQL戦略={}",
                    task.getName(), task.getSqlStrategyKey());

        return exportService.execute(task.getExportRequest(), params);
    }

    /**
     * エクスポートサービスを取得
     */
    private AbstractExportService<?> getExportService(String dataType) {
        AbstractExportService<?> service = exportServices.get(dataType.toUpperCase());
        if (service == null) {
            throw new ServiceException(GlobalMessageConstants.BAD_REQUEST.getCode(),
                    "サポートされていないデータタイプ: " + dataType);
        }
        return service;
    }

    /**
     * ZIPファイル名を生成
     */
    private String generateZipFileName() {
        return String.format("事業計画_%s", DateUtil.getCurrentDateTimeString_YYYYMMDDHHMM());
    }

    // ==================== 内部クラス ====================

    /**
     * エクスポートタスク
     */
    @Getter
    public static class ExportTask {
        private final String name;
        private final ExportRequest exportRequest;
        private final String sqlStrategyKey; // SQL戦略識別子
        private final String fileName; // エクスポートファイル名

        public ExportTask(String name, ExportRequest exportRequest) {
            this.name = name;
            this.exportRequest = exportRequest;
            this.sqlStrategyKey = "DEFAULT"; // デフォルト戦略
            this.fileName = null; // デフォルトファイル名を使用
        }

        public ExportTask(String name, ExportRequest exportRequest, String sqlStrategyKey) {
            this.name = name;
            this.exportRequest = exportRequest;
            this.sqlStrategyKey = sqlStrategyKey != null ? sqlStrategyKey : "DEFAULT";
            this.fileName = null; // デフォルトファイル名を使用
        }

        public ExportTask(String name, ExportRequest exportRequest, String sqlStrategyKey, String fileName) {
            this.name = name;
            this.exportRequest = exportRequest;
            this.sqlStrategyKey = sqlStrategyKey != null ? sqlStrategyKey : "DEFAULT";
            this.fileName = fileName; // 指定されたファイル名を使用
        }

    }

    /**
     * CSV生成結果（内部使用）
     */
    private record CSVGenerationResult(List<Path> csvFiles, List<ExportResult> exportResults) {

    }

    /**
     * エクスポート処理結果
     */
    public record ExportProcessResult(Path zipFile, List<Path> csvFiles, List<ExportResult> exportResults) {

    }
}
