package com.ms.bp.interfaces.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.Date;

/**
 * インポート履歴レスポンスDTO
 * インポート履歴取得API専用のレスポンス形式
 * インターフェース層のデータ転送オブジェクト
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ImportHistoryResponse {

    /**
     * 履歴番号
     * T_IMPRT_RRK.RRK_BANGO
     */
    private Long historyNumber;

    /**
     * ファイル種別
     * ファイル種別コードから日本語名に変換済み
     * 例：1→次年度計画マスタ、2→見通し・計画_採算管理単位C別＜本社＞
     */
    private String fileType;

    /**
     * エリア
     * エリアコードから日本語名に変換済み
     * 複数エリアの場合はカンマ区切り
     */
    private String area;

    /**
     * ファイル名
     * T_IMPRT_RRK.FILE_MEI
     */
    private String fileName;

    /**
     * エラーファイル名
     * T_IMPRT_RRK.ERROR_FILE_MEI
     */
    private String errorFileName;

    /**
     * アップロード開始日時
     * T_IMPRT_RRK.UPLOAD_KSH_NCHJ
     */
    private String uploadStartDateTime;

    /**
     * アップロード完了日時
     * T_IMPRT_RRK.UPLOAD_KNR_NCHJ
     */
    private String uploadCompleteDateTime;

    /**
     * ステータス
     * ステータスコードから日本語名に変換済み
     * 0:処理中、1:完了、2:一部失敗、3:失敗、4:システムエラー
     */
    private String status;
}
