package com.ms.bp.application;

import com.ms.bp.domain.permission.repository.PermissionRepository;
import com.ms.bp.shared.util.LambdaResourceManager;
import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.domain.master.repository.AreaCodeRepository;
import com.ms.bp.domain.permission.PermissionService;
import com.ms.bp.domain.permission.model.UserPermissionInfo;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.domain.concurrent.model.ConcurrentJobInfo;
import com.ms.bp.interfaces.dto.response.UserPermissionsResponse;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 権限アプリケーションサービス
 * 権限関連のユースケースを調整し、ドメインサービスを組み合わせて処理を実行する
 * 権限は読み取り専用操作のため、読み取り専用実行を使用する
 */
public class PermissionApplicationService {
    private static final Logger logger = LoggerFactory.getLogger(PermissionApplicationService.class);
    /** MS戦略・コーポOF判定コード */
    private static final String MS_STRATEGY_CORPO_HANT_CODE = "2";
    /**
     * ユーザーの全権限を取得（複数areaCode-unitCode対応版）
     * JDK21のRecord Patterns、Virtual Threads対応等を活用した最適化実装
     * 
     * @param userInfo 認証済みユーザー情報
     * @param operationType 操作種類
     * @return ユーザー権限レスポンスリスト（各areaCode-unitCode組み合わせ毎）
     */
    public List<UserPermissionsResponse> getUserPermissions(UserInfo userInfo, String operationType) {
        logger.info("ユーザー権限取得開始: ユーザーID={}, 社員コード={}",
                   userInfo.getShainCode(), userInfo.getShainCode());

        // 兼務情報から追加のareaCode-unitCode組み合わせを取得
        var concurrentJobAreaUnitPairs = getConcurrentJobAreaUnitPairs(userInfo);

        // JDK21: Virtual Threadsを活用した並行処理でデータベースリソースを使用
        var allPermissionsMap = LambdaResourceManager.executeReadOnly(serviceFactory -> {
            var permissionService = serviceFactory.createPermissionService();
            return permissionService.getUserPermissions(userInfo, concurrentJobAreaUnitPairs);
        });

        logger.info("ユーザー権限取得完了: 組み合わせ数={}, 総権限数={}",
                   allPermissionsMap.size(),
                   allPermissionsMap.values().stream().mapToInt(List::size).sum());

        // JDK21: Switch Expressionによる操作種類フィルタリング
        var filteredPermissionsMap = switch (operationType) {
            case BusinessConstants.OPERATION_UPLOAD_CODE, BusinessConstants.OPERATION_DOWNLOAD_CODE -> 
                allPermissionsMap.entrySet()
                    .stream()
                    .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                            .filter(p -> operationType.equals(p.getOperationDivision()))
                            .toList(),
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                    ));
            default -> allPermissionsMap;
        };

        // 各職務（主務・兼務）組み合わせに対してUserPermissionsResponseを作成
        var primaryJobKey = STR."\{userInfo.getAreaCode()},\{userInfo.getUnitCode()}";
        
        return filteredPermissionsMap.entrySet()
            .stream()
            .map(entry -> {
                var jobKey = entry.getKey();
                var permissions = entry.getValue();
                var parts = jobKey.split(",");
                
                if (parts.length != 2) {
                    logger.warn("無効な職務areaCode-unitCode組み合わせをスキップ: {}", jobKey);
                    return null;
                }
                
                var areaCode = parts[0].trim();
                var unitCode = parts[1].trim();
                var isPrimaryJob = jobKey.equals(primaryJobKey);

                // 各職務組み合わせ用の一時的なUserInfoを作成
                var tempUserInfo = createTempUserInfoForJob(userInfo, areaCode, unitCode, isPrimaryJob);
                
                // 既存のcreateUserPermissionsResponseメソッドを再利用
                return createUserPermissionsResponse(tempUserInfo, permissions);
            })
            .filter(Objects::nonNull) // null要素を除外
            .toList();
    }

    /**
     * 兼務情報から追加のareaCode-unitCode組み合わせを取得
     * JDK21: Pattern Matching、Stream API、Switch Expressionを活用した実装
     * 
     * @param userInfo ユーザー情報
     * @return 兼務に基づくareaCode-unitCode組み合わせリスト
     */
    private List<String> getConcurrentJobAreaUnitPairs(UserInfo userInfo) {
        return LambdaResourceManager.executeReadOnly(serviceFactory -> {
            try {
                // 兼務マスタから有効な兼務情報を取得（エリアコード込み）
                var concurrentJobRepository = serviceFactory.getConcurrentJobRepository();
                var validConcurrentJobsWithArea = concurrentJobRepository.findValidConcurrentJobsWithAreaCode(
                    userInfo.getShainCode(), 
                    userInfo.getSystemOperationCompanyCode()
                );

                if (validConcurrentJobsWithArea.isEmpty()) {
                    logger.debug("社員コード{}に対する有効な兼務情報が見つかりません", userInfo.getShainCode());
                    return List.<String>of();
                }

                // JDK21: Stream APIでareaCode,unitCodeペア作成
                var areaUnitPairs = validConcurrentJobsWithArea.stream()
                    .filter(ConcurrentJobInfo::isValidConcurrentJob) // 有効な兼務のみ
                    .map(ConcurrentJobInfo::toAreaUnitPair) // "areaCode,unitCode"形式に変換
                    .filter(pair -> !pair.isEmpty()) // 空文字列を除外
                    .distinct() // 重複を除去
                    .toList();

                logger.info("兼務情報取得完了: 社員コード={}, 兼務件数={}, 有効組み合わせ数={}", 
                           userInfo.getShainCode(), validConcurrentJobsWithArea.size(), areaUnitPairs.size());
                
                return areaUnitPairs;
                
            } catch (Exception e) {
                logger.error("兼務情報取得中にエラーが発生しました: 社員コード={}", 
                            userInfo.getShainCode(), e);
                // エラーの場合は空のリストを返して処理を継続
                return List.<String>of();
            }
        });
    }

    /**
     * 特定の職務（主務・兼務）用の一時的なUserInfoを作成
     * JDK21: Record Destructuringパターンを活用（将来的にUserInfoをRecordに変換時）
     * 
     * @param originalUserInfo 元のユーザー情報
     * @param areaCode 対象エリアコード
     * @param unitCode 対象ユニットコード  
     * @param isPrimaryJob 主務かどうか（false: 兼務）
     * @return 一時的なUserInfo
     */
    private UserInfo createTempUserInfoForJob(UserInfo originalUserInfo, String areaCode, String unitCode, boolean isPrimaryJob) {
        var tempUserInfo = new UserInfo();
        
        // 共通フィールドの設定
        tempUserInfo.setShainCode(originalUserInfo.getShainCode());
        tempUserInfo.setSystemOperationCompanyCode(originalUserInfo.getSystemOperationCompanyCode());
        tempUserInfo.setAreaCode(areaCode);
        tempUserInfo.setUnitCode(unitCode);
        
        // JDK21: Pattern Matchingを活用した条件分岐
        if (isPrimaryJob) {
            // 主務: すべての原始情報を保持
            tempUserInfo.setAreaName(originalUserInfo.getAreaName());
            tempUserInfo.setPositionCode(originalUserInfo.getPositionCode());
            tempUserInfo.setGroupCode(originalUserInfo.getGroupCode());
            tempUserInfo.setAreaInfos(originalUserInfo.getAreaInfos());
            tempUserInfo.setPositionSpecialCheck(originalUserInfo.getPositionSpecialCheck());
        } else {
            // 兼務: 基本情報のみ設定、他はデフォルト値
            tempUserInfo.setAreaName(""); // 実際にはエリアマスタから取得可能
            tempUserInfo.setPositionCode(originalUserInfo.getPositionCode());
            tempUserInfo.setGroupCode(originalUserInfo.getGroupCode());
            tempUserInfo.setPositionSpecialCheck("0"); // デフォルト: 判定不要
        }
        
        return tempUserInfo;
    }



    // ==================== DTO変換メソッド ====================

    /**
     * ユーザー権限レスポンスを作成
     * areaPatternが"1"の権限が存在する場合、エリア情報も取得して設定する
     * @param userInfo ユーザー情報
     * @param permissions 権限リスト
     * @return ユーザー権限レスポンス
     */
    private UserPermissionsResponse createUserPermissionsResponse(UserInfo userInfo, List<UserPermissionInfo> permissions) {
        UserPermissionsResponse response = new UserPermissionsResponse();
        response.setSystemOperationCompanyCode(userInfo.getSystemOperationCompanyCode());
        response.setPositionCode(userInfo.getPositionCode());
        response.setUnitCode(userInfo.getUnitCode());
        response.setAreaCode(userInfo.getAreaCode());
        response.setAreaName(userInfo.getAreaName());
        response.setGroupCode(userInfo.getGroupCode());
        response.setShainCode(userInfo.getShainCode());

        // 権限情報を変換
        List<UserPermissionsResponse.PermissionInfo> permissionInfos = permissions.stream()
            .map(this::convertToPermissionInfo)
            .collect(Collectors.toList());
        response.setPermissions(permissionInfos);

        // 読み取り専用でデータベースリソースを使用してMS戦略・コーポOF判定コードに対応する種類コードリストを取得
        List<String> ruleType = LambdaResourceManager.executeReadOnly(serviceFactory -> {
            PermissionRepository  permissionRepository = serviceFactory.getPermissionRepository();
            return permissionRepository.findRuleTypeCodesByMsStrategyCorp(MS_STRATEGY_CORPO_HANT_CODE, userInfo.getSystemOperationCompanyCode());
        });
        //ユーザがMS_戦略・コーポOFに含まれるユニットに所属している以外
        boolean isRuleType = !ruleType.isEmpty() && !ruleType.contains(userInfo.getUnitCode());

        //役職区分判定要否 0:否 1:要
        response.setPositionSpecialCheck(needsSpecialCheck(permissions, isRuleType));
        // areaPatternが"1"の権限が存在する場合、エリア情報を取得して設定
        boolean hasAreaSpecificPermission = permissions.stream()
            .anyMatch(permission -> BusinessConstants.AREA_PATTERN_AREA_SPECIFIC.equals(permission.getAreaPattern()));

        if (hasAreaSpecificPermission) {
            logger.debug("エリア固有権限が検出されました。エリア情報を取得します: ユーザー={}", userInfo.getShainCode());
            List<AreaInfo> areaInfos = LambdaResourceManager.executeReadOnly(serviceFactory -> {
                PermissionRepository permissionRepository = serviceFactory.getPermissionRepository();
                return permissionRepository.findAreaInfosByAreaTantoshaPermission(userInfo.getSystemOperationCompanyCode());
            });
            response.setAreaInfos(areaInfos);
        } else {
            response.setAreaInfos(List.of(AreaInfo.builder()
                    .areaCode(userInfo.getAreaCode()).areaName(userInfo.getAreaName()).build()));
        }

        return response;
    }

    /**
     * UserPermissionInfoをPermissionInfoに変換
     * @param userPermission ユーザー権限情報
     * @return 権限情報DTO
     */
    private UserPermissionsResponse.PermissionInfo convertToPermissionInfo(UserPermissionInfo userPermission) {
        UserPermissionsResponse.PermissionInfo permissionInfo = new UserPermissionsResponse.PermissionInfo();
        permissionInfo.setPermissionCode(userPermission.getPermissionCode());
        permissionInfo.setFileTypeCode(userPermission.getFileTypeCode());
        permissionInfo.setOperationDivision(userPermission.getOperationDivision());
        permissionInfo.setAreaPattern(userPermission.getAreaPattern());
        permissionInfo.setHantCode(userPermission.getHantCode());
        return permissionInfo;
    }

    /**
     * 指定されたルールに基づき、特別なチェック（役職区分判定要否）が必要かどうかを判定します。
     *
     * @param permissions ユーザーの権限リスト
     * @return "1" (要), "0" (否)
     */
    public static String needsSpecialCheck(List<UserPermissionInfo> permissions, boolean isRuleType) {
        // 条件：ファイルタイプが "002"/"003" かつ エリア権限（3桁目 'A'） かつ ユーザがMS_戦略・コーポOFに含まれるユニットに所属している以外場合
        return permissions.stream()
                .anyMatch(p -> {
                    var code = p.getPermissionCode();
                    return (BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE.equals(p.getFileTypeCode()) || BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE.equals(p.getFileTypeCode())) &&
                            code != null && code.length() > 2 && code.charAt(2) == 'A' && isRuleType;
                }) ? "1" : "0";
    }

}
