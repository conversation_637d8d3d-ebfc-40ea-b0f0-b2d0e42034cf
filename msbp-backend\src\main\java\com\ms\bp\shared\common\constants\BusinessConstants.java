package com.ms.bp.shared.common.constants;

/**
 * ビジネス定数列挙型
 * 業務ドメインで使用される各種定数を定義する
 */
public class BusinessConstants {

    // ========== ファイル種別コード定数 ==========
    /** ファイル種別：次年度計画マスタのコード */
    public static final String FILE_TYPE_PLAN_MASTER_CODE = "1";

    /** ファイル種別：見通し・計画_採算管理単位C別＜本社＞のコード */
    public static final String FILE_TYPE_BUDGET_HONSHA_CODE = "2";

    /** ファイル種別：見通し・計画_採算管理単位C別＜エリア＞のコード */
    public static final String FILE_TYPE_BUDGET_AREA_CODE = "3";

    /** ファイル種別：間接利益計画_メーカー別のコード */
    public static final String FILE_TYPE_INDIRECT_PROFIT_CODE = "4";

    // ========== テータスコード定数 ==========
    /** バッチステータス：完了のコード */
    public static final String BATCH_STATUS_COMPLETED_CODE = "1";

    /** バッチステータス：処理中のコード */
    public static final String BATCH_STATUS_PROCESSING_CODE = "0";

    /** バッチステータス：失敗のコード */
    public static final String BATCH_STATUS_FAILED_CODE = "3";

    /** バッチステータス：一部失敗のコード */
    public static final String BATCH_STATUS_PARTIALLY_FAILED_CODE = "2";

    /** バッチステータス：システムエラーのコード */
    public static final String BATCH_STATUS_SYSTEM_ERROR_CODE = "4";

    // ========== 採算管理単位計画策定エリアコード定数 ==========
    /** 採算管理単位計画策定エリアのコード */
    public static final String PROFIT_MANAGEMENT_PLANNING_AREA_CODE = "SKSA";

    public static final String VALIDATION_ERROR_MSG = "Required Parameter Error";
    public static final String GENERAL_ERROR_MSG = "Failed";
    public static final String FILE_FORMAT_ERROR_MSG = "File Format Error";
    public static final String SUCCESS_MSG = "Success";
    // ========== 操作区分コード定数 ==========
    /** 操作区分：アップロードのコード */
    public static final String OPERATION_UPLOAD_CODE = "1";

    /** 操作区分：ダウンロードのコード */
    public static final String OPERATION_DOWNLOAD_CODE = "2";

    // ========== エリアパターンコード定数 ==========
    /** エリアパターン：エリア固有権限のコード */
    public static final String AREA_PATTERN_AREA_SPECIFIC = "1";

    /** システム運用企業コード */
    public static final String SYSTEM_OPERATION_COMPANY_CODE = "100001";

    // ========== 所属区分コード定数 ==========
    /** 所属区分：本社のコード */
    public static final String AFFILIATION_HEAD_OFFICE = "1";

    /** 所属区分：エリアのコード */
    public static final String AFFILIATION_AREA = "2";

    // ========== データ区分コード定数 ==========
    /** データ区分：移管前（当年度組織） */
    public static final String DATAKUBUN_IKO_BEFORE = "0";

    /** データ区分：移管後（次年度組織） */
    public static final String DATAKUBUN_IKO_AFTER = "1";

    // ========== エリアコード定数 ==========
    /** 本社エリアコード */
    public static final String HEAD_OFFICE_AREA_CODE = "0000";

    /** 戦略名 */
    public static final String PLAN_MASTER_DEFAULT = "PLAN_MASTER_DEFAULT";

    /** 戦略名: 見通し・計画_採算管理単位C別 */
    public static final String HEAD_OFFICE_OUTLOOK_PLAN_C = "HEAD_OFFICE_OUTLOOK_PLAN_C";

    /** 戦略名: 見通し・計画_採算管理単位企業別 */
    public static final String HEAD_OFFICE_OUTLOOK_PLAN_K = "HEAD_OFFICE_OUTLOOK_PLAN_K";

    /** 戦略名: エリア 見通し・計画_採算管理単位C別 */
    public static final String AREA_OUTLOOK_PLAN_C = "AREA_OUTLOOK_PLAN_C";

    /** 戦略名: エリア 見通し・計画_採算管理単位企業別 */
    public static final String AREA_OUTLOOK_PLAN_K = "AREA_OUTLOOK_PLAN_K";

    /** 次年度計画マスタ用S3ディレクトリパス */
    public static final String S3_DIR_PLAN_MASTER = "out/jinendoKkkMst/";

    /** 見通し・計画_採算管理単位C別＜本社＞用S3ディレクトリパス */
    public static final String S3_DIR_BUDGET_HONSHA = "out/mtshKkkHnsh/";

    /** 見通し・計画_採算管理単位C別＜エリア＞用S3ディレクトリパス */
    public static final String S3_DIR_BUDGET_AREA = "out/mtshKkkArea/";

    /** 間接利益計画_メーカー別用S3ディレクトリパス */
    public static final String S3_DIR_INDIRECT_PROFIT = "out/knstsRiekiMaker/";

    // ========== ファイル種別別S3入力ディレクトリパス定数 ==========
    /** 次年度計画マスタ用S3入力ディレクトリパス */
    public static final String S3_INPUT_DIR_PLAN_MASTER = "in/jinendoKkkMst/";

    /** 見通し・計画_採算管理単位C別＜本社＞用S3入力ディレクトリパス */
    public static final String S3_INPUT_DIR_BUDGET_HONSHA = "in/mtshKkkHnsh/";

    /** 見通し・計画_採算管理単位C別＜エリア＞用S3入力ディレクトリパス */
    public static final String S3_INPUT_DIR_BUDGET_AREA = "in/mtshKkkArea/";

    /** 間接利益計画_メーカー別用S3入力ディレクトリパス */
    public static final String S3_INPUT_DIR_INDIRECT_PROFIT = "in/knstsRiekiMaker/";

    /** 次年度計画マスタファイル名 */
    public static final String FILE_NAME_PLAN_MASTER = "次年度計画マスタ";

    /** 見通し・計画_採算管理単位C別＜本社＞ファイル名 */
    public static final String FILE_NAME_BUDGET_HONSHA = "見通し・計画_採算管理単位C別＜本社＞";

    /** 見通し・計画_採算管理単位C別＜エリア＞ファイル名 */
    public static final String FILE_NAME_BUDGET_AREA = "見通し・計画_採算管理単位C別＜エリア＞";

    /** 間接利益計画_メーカー別ファイル名 */
    public static final String FILE_NAME_INDIRECT_PROFIT = "間接利益計画_メーカー別";

    /** インポート履歴取得のデフォルト件数制限 */
    public static final int DEFAULT_HISTORY_LIMIT = 20;
    /** CSVファイルのバッチサイズ */
    public static final int CSV_DOWNLOAD_BATCH_SIZE = 5000;
    public static final int CSV_UPLOAD_BATCH_SIZE = 1000;

    /** 見通し・計画_採算管理単位C別 */
    public static final String MTSH_KKK_C = "見通し計画SK別";

    /** 見通し・計画_採算管理単位企業別 */
    public static final String MTSH_KKK_K = "見通し計画企業別";

    /** 移行前 */
    public static final String IKO_BEFORE = "移管前";

    /** 移行後 */
    public static final String IKO_AFTER = "移管後";

    /** ファイル種別：見通し・計画_採算管理単位C別 */
    public static final String MTSH_KKK_FILE_TYPE_C = "MTSH_KKK_FILE_TYPE_C";

    /** ファイル種別：見通し・計画_採算管理単位企業別 */
    public static final String MTSH_KKK_FILE_TYPE_K = "MTSH_KKK_FILE_TYPE_K";

    // 環境変数名
    public static final String ENV_PROFILE = "SPRING_PROFILES_ACTIVE";
    public static final String DEFAULT_PROFILE = "dev";
    public static final String LAMBDA_ID = "LAMBDA_ID";
    public static final String WORK_LAMBDA_ID = "WORKER_LAMBDA";
    // ========== 役職区分判定要否コード定数 ==========
    /** 役職区分判定要否：0:否 */
    public static final String NOT_REQUIRE_POSITION_SPECIAL_CHECK = "0";

    /** 役職区分判定要否：1:要 */
    public static final String REQUIRE_POSITION_SPECIAL_CHECK = "1";

    // ========== 役職区分コード定数 ==========
    /** ＵＬ、ＤＣ長 : 51 */
    public static final String POSITION_CODE_51 = "51";

    /** 非役職者 : 61 */
    public static final String POSITION_CODE_61 = "61";

    /** ＧＭ、ＡＭ、室長 : 41 */
    public static final String POSITION_CODE_41 = "41";

    // ========== 権限判定コード定数 ==========
    /** エリア担当者権限判定コード */
    public static final String AREA_TANTOSHA_HANT_CODE = "4";
}