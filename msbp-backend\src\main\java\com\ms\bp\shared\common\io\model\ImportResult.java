package com.ms.bp.shared.common.io.model;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * インポート結果クラス
 */
@Setter
@Getter
public class ImportResult {
    private int insertedCount;  // 挿入された件数
    private int updatedCount;   // 更新された件数
    private int failedCount;    // 失敗した件数
    private Map<String, Object> statistics;  // 統計情報

    public ImportResult() {
        this.insertedCount = 0;
        this.updatedCount = 0;
        this.failedCount = 0;
        this.statistics = new HashMap<>();
    }



    /**
     * 統計情報を追加する
     */
    public void addStatistic(String key, Object value) {
        this.statistics.put(key, value);
    }
}
