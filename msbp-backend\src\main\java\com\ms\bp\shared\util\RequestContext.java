package com.ms.bp.shared.util;

import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.request.ImportRequest;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;

/**
 * リクエストコンテキスト管理ユーティリティ
 * Lambda環境でのユーザー情報とインポートリクエストの統一管理
 */
public class RequestContext {
    private static final Logger logger = LoggerFactory.getLogger(RequestContext.class);

    // ThreadLocalでユーザー情報を管理
    private static final ThreadLocal<UserInfo> USER_INFO_HOLDER = new ThreadLocal<>();

    // ThreadLocalでインポートリクエストを管理
    private static final ThreadLocal<ImportRequest> IMPORT_REQUEST_HOLDER = new ThreadLocal<>();

    // ==================== ユーザー情報管理 ====================
    /**
     * 現在のスレッドにユーザー情報を設定
     * @param userInfo ユーザー情報
     */
    public static void setUserInfo(UserInfo userInfo) {
        USER_INFO_HOLDER.set(userInfo);
        if (userInfo != null) {
            logger.debug("ユーザー情報を設定: ユーザー={}, 社員コード={}",
                        userInfo.getShainCode(), userInfo.getShainCode());
        } else {
            logger.debug("ユーザー情報をクリアしました");
        }
    }

    /**
     * 現在のスレッドからユーザー情報を取得
     * @return ユーザー情報、設定されていない場合はnull
     */
    public static UserInfo getUserInfo() {
        UserInfo userInfo = USER_INFO_HOLDER.get();
        if (userInfo != null) {
            logger.trace("ユーザー情報を取得: ユーザー={}", userInfo.getShainCode());
        } else {
            logger.trace("ユーザー情報が設定されていません");
        }
        return userInfo;
    }

    // ==================== インポートリクエスト管理 ====================
    /**
     * 現在のスレッドにインポートリクエストを設定
     * 非同期処理でのインポートリクエスト情報の統一管理に使用
     * @param importRequest インポートリクエスト
     */
    public static void setImportRequest(ImportRequest importRequest) {
        IMPORT_REQUEST_HOLDER.set(importRequest);
        if (importRequest != null) {
            logger.debug("インポートリクエストを設定: dataType={}, s3Key={}",
                        importRequest.getDataType(), importRequest.getS3Key());
        } else {
            logger.debug("インポートリクエストをクリアしました");
        }
    }

    /**
     * 現在のスレッドからインポートリクエストを取得
     * @return インポートリクエスト、設定されていない場合はnull
     */
    public static ImportRequest getImportRequest() {
        ImportRequest importRequest = IMPORT_REQUEST_HOLDER.get();
        if (importRequest != null) {
            logger.trace("インポートリクエストを取得: dataType={}", importRequest.getDataType());
        } else {
            logger.trace("インポートリクエストが設定されていません");
        }
        return importRequest;
    }

    /**
     * インポートリクエストが設定されているかチェック
     * @return インポートリクエストが設定されている場合true
     */
    public static boolean hasImportRequest() {
        boolean hasRequest = IMPORT_REQUEST_HOLDER.get() != null;
        logger.trace("インポートリクエスト存在チェック: {}", hasRequest);
        return hasRequest;
    }


    /**
     * ThreadLocalリソースをクリア（メモリリーク防止）
     * Lambda関数の終了時に必ず呼び出すこと
     */
    public static void clear() {
        UserInfo userInfo = USER_INFO_HOLDER.get();
        ImportRequest importRequest = IMPORT_REQUEST_HOLDER.get();

        USER_INFO_HOLDER.remove();
        IMPORT_REQUEST_HOLDER.remove();

        logger.debug("RequestContextをクリアしました: hadUser={}, hadImportRequest={}",
                    userInfo != null, importRequest != null);
    }
}
