package com.ms.bp.interfaces.dto.response;

import lombok.Data;

/**
 * アップロードURL生成レスポンスDTO
 * インターフェース層のデータ転送オブジェクト
 * 署名付きアップロードURLとS3キーパスを含む
 */
@Data
public class UploadUrlResponse {
    /**
     * 署名付きアップロードURL
     * フロントエンドがファイルアップロードに使用する
     */
    private String uploadUrl;

    /**
     * URL有効期限（秒）
     */
    private int expiresIn;

    /**
     * S3オブジェクトキー（パス）
     * S3内でのファイル保存場所を示す完全パス
     * 後続のファイル参照や管理に使用される
     */
    private String s3Key;
}
