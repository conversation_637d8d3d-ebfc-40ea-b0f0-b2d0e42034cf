package com.ms.bp.infrastructure.external.email;

import com.ms.bp.shared.common.config.ConfigurationManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.ses.SesClient;
import software.amazon.awssdk.services.ses.model.*;

/**
 * メール送信サービス
 * Amazon SESを使用してメールを送信する
 */
public class EmailService implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(EmailService.class);
    
    private final SesClient sesClient;
    private final String fromEmail;
    private final ConfigurationManager config;

    // メールテンプレート
    private static final String ACTIVATION_SUBJECT = "アカウントアクティベーションのお知らせ";
    private static final String PASSWORD_RESET_SUBJECT = "パスワードリセットのお知らせ";

    /**
     * EmailServiceのコンストラクタ
     */
    public EmailService() {
        // 設定管理器を初期化
        this.config = ConfigurationManager.getInstance();

        // AWS SESクライアントを初期化
        String awsRegion = config.getProperty("aws.region", "ap-northeast-1");
        Region region = Region.of(awsRegion);

        this.sesClient = SesClient.builder()
                .region(region)
                .build();

        // 送信者メールアドレスを設定ファイルから取得
        this.fromEmail = config.getProperty("email.default.from", "<EMAIL>");

        logger.debug("EmailServiceが初期化されました。リージョン: {}, 送信者: {}", awsRegion, fromEmail);
    }
    
    /**
     * ユーザーアクティベーションメールを送信する
     * @param toEmail 送信先メールアドレス
     * @param userName ユーザー名
     * @param activationUrl アクティベーションURL
     * @return 送信結果
     */
    public boolean sendActivationEmail(String toEmail, String userName, String activationUrl) {
        if (toEmail == null || toEmail.isEmpty()) {
            logger.error("送信先メールアドレスが指定されていません");
            return false;
        }
        
        String htmlBody = buildActivationEmailHtml(userName, activationUrl);
        String textBody = buildActivationEmailText(userName, activationUrl);
        
        return sendEmail(toEmail, ACTIVATION_SUBJECT, htmlBody, textBody);
    }
    
    /**
     * パスワードリセットメールを送信する
     * @param toEmail 送信先メールアドレス
     * @param userName ユーザー名
     * @param resetUrl パスワードリセットURL
     * @return 送信結果
     */
    public boolean sendPasswordResetEmail(String toEmail, String userName, String resetUrl) {
        if (toEmail == null || toEmail.isEmpty()) {
            logger.error("送信先メールアドレスが指定されていません");
            return false;
        }
        
        String htmlBody = buildPasswordResetEmailHtml(userName, resetUrl);
        String textBody = buildPasswordResetEmailText(userName, resetUrl);
        
        return sendEmail(toEmail, PASSWORD_RESET_SUBJECT, htmlBody, textBody);
    }
    
    /**
     * メールを送信する（汎用メソッド）
     * @param toEmail 送信先メールアドレス
     * @param subject 件名
     * @param htmlBody HTMLメール本文
     * @param textBody テキストメール本文
     * @return 送信結果
     */
    private boolean sendEmail(String toEmail, String subject, String htmlBody, String textBody) {
        try {
            // メール送信リクエストを作成
            SendEmailRequest request = SendEmailRequest.builder()
                    .source(fromEmail)
                    .destination(Destination.builder()
                            .toAddresses(toEmail)
                            .build())
                    .message(Message.builder()
                            .subject(Content.builder()
                                    .charset("UTF-8")
                                    .data(subject)
                                    .build())
                            .body(Body.builder()
                                    .html(Content.builder()
                                            .charset("UTF-8")
                                            .data(htmlBody)
                                            .build())
                                    .text(Content.builder()
                                            .charset("UTF-8")
                                            .data(textBody)
                                            .build())
                                    .build())
                            .build())
                    .build();
            
            // メールを送信
            SendEmailResponse response = sesClient.sendEmail(request);
            
            logger.info("メール送信成功: MessageId={}, To={}", response.messageId(), toEmail);
            return true;
            
        } catch (Exception e) {
            logger.error("メール送信に失敗しました: To={}", toEmail, e);
            return false;
        }
    }
    
    /**
     * アクティベーションメールのHTML本文を作成
     */
    private String buildActivationEmailHtml(String userName, String activationUrl) {
        return String.format("""
            <html>
            <body>
                <h2>アカウントアクティベーション</h2>
                <p>%s 様</p>
                <p>アカウントのアクティベーションを完了するため、以下のリンクをクリックしてパスワードを設定してください。</p>
                <p><a href="%s" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">パスワードを設定する</a></p>
                <p>このリンクは24時間有効です。</p>
                <p>もしこのメールに心当たりがない場合は、このメールを無視してください。</p>
                <hr>
                <p><small>このメールは自動送信されています。返信はできません。</small></p>
            </body>
            </html>
            """, userName != null ? userName : "ユーザー", activationUrl);
    }
    
    /**
     * アクティベーションメールのテキスト本文を作成
     */
    private String buildActivationEmailText(String userName, String activationUrl) {
        return String.format("""
            アカウントアクティベーション
            
            %s 様
            
            アカウントのアクティベーションを完了するため、以下のURLにアクセスしてパスワードを設定してください。
            
            %s
            
            このリンクは24時間有効です。
            
            もしこのメールに心当たりがない場合は、このメールを無視してください。
            
            ---
            このメールは自動送信されています。返信はできません。
            """, userName != null ? userName : "ユーザー", activationUrl);
    }
    
    /**
     * パスワードリセットメールのHTML本文を作成
     */
    private String buildPasswordResetEmailHtml(String userName, String resetUrl) {
        return String.format("""
            <html>
            <body>
                <h2>パスワードリセット</h2>
                <p>%s 様</p>
                <p>パスワードリセットのリクエストを受け付けました。以下のリンクをクリックして新しいパスワードを設定してください。</p>
                <p><a href="%s" style="background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">パスワードをリセットする</a></p>
                <p>このリンクは1時間有効です。</p>
                <p>もしこのリクエストに心当たりがない場合は、このメールを無視してください。</p>
                <hr>
                <p><small>このメールは自動送信されています。返信はできません。</small></p>
            </body>
            </html>
            """, userName != null ? userName : "ユーザー", resetUrl);
    }
    
    /**
     * パスワードリセットメールのテキスト本文を作成
     */
    private String buildPasswordResetEmailText(String userName, String resetUrl) {
        return String.format("""
            パスワードリセット
            
            %s 様
            
            パスワードリセットのリクエストを受け付けました。以下のURLにアクセスして新しいパスワードを設定してください。
            
            %s
            
            このリンクは1時間有効です。
            
            もしこのリクエストに心当たりがない場合は、このメールを無視してください。
            
            ---
            このメールは自動送信されています。返信はできません。
            """, userName != null ? userName : "ユーザー", resetUrl);
    }
    
    /**
     * リソースをクリーンアップ
     */
    @Override
    public void close() {
        if (sesClient != null) {
            sesClient.close();
        }
    }
}
