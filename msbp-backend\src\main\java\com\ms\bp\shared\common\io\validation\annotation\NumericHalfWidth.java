package com.ms.bp.shared.common.io.validation.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 半角数字（0-9）のみを許可するアノテーション
 * GlobalCodeConstants.ERR_018と統合されたバリデーション機能を提供
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface NumericHalfWidth {

    /**
     * カスタム日本語フィールド名
     * 指定された場合、エラーメッセージでJavaフィールド名の代わりに使用される
     * 例：fieldName = "年度"
     */
    String fieldName() default "";

    /**
     * エラーコード（デフォルト：ERR_018）
     * GlobalCodeConstantsで定義されたエラーコードを指定
     */
    String errorCode() default "ERR_018";

    /**
     * カスタムエラーメッセージ
     * 空の場合はerrorCodeに対応するGlobalCodeConstantsのメッセージテンプレートを使用
     * 指定された場合は{field}占位符をサポート
     */
    String message() default "";
}
