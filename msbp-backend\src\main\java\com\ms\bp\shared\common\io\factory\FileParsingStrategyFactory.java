package com.ms.bp.shared.common.io.factory;

import com.ms.bp.shared.common.io.model.FileFormat;
import com.ms.bp.shared.common.io.strategy.FileParsingStrategy;
import com.ms.bp.shared.common.io.strategy.impl.CsvParsingStrategy;

/**
 * ファイル解析戦略ファクトリー
 * （ファクトリーパターン：具体的なファイル解析戦略を作成）
 */
public class FileParsingStrategyFactory {
    /**
     * CSV解析戦略を作成
     *
     * @param format ファイル形式
     * @return ファイル解析戦略
     */
    public static FileParsingStrategy createStrategy(FileFormat format) {
        // CSVのみをサポート
        if (format != FileFormat.CSV) {
            throw new IllegalArgumentException("サポートされていないファイル形式: " + format);
        }
        return new CsvParsingStrategy();
    }
}