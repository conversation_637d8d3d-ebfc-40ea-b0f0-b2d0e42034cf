package com.ms.bp.interfaces;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyResponseEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ms.bp.application.UserInfoApplicationService;
import com.ms.bp.application.dto.UserBusinessInfo;
import com.ms.bp.interfaces.rest.controller.AnnounceMessageController;
import com.ms.bp.shared.common.CommonResult;
import com.ms.bp.interfaces.rest.controller.DataController;
import com.ms.bp.interfaces.rest.controller.FileController;
import com.ms.bp.interfaces.rest.controller.PermissionController;
import com.ms.bp.shared.security.ApiAuthMiddleware;
import com.ms.bp.interfaces.rest.exception.GlobalExceptionHandler;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.config.ConfigurationInitializer;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.shared.util.RequestContext;
import com.ms.bp.shared.util.ResponseUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.util.*;

/**
 * すべてのAPIゲートウェイリクエストを処理するメインLambdaハンドラー
 */
public class LambdaHandler implements RequestHandler<APIGatewayProxyRequestEvent, APIGatewayProxyResponseEvent> {
    private static final Logger logger = LoggerFactory.getLogger(LambdaHandler.class);

    // APIパスの定数
    private static final String API_PATH_FILES = "/files/";
    private static final String API_PATH_FILES_UPLOAD_URL = "/files/uploadUrl";
    private static final String API_PATH_FILES_DOWNLOAD_URL = "/files/downloadUrl";
    // データ管理エンドポイント
    private static final String API_PATH_DATA = "/data/";
    private static final String API_PATH_DATA_IMPORT = "/data/import";
    private static final String API_PATH_DATA_EXPORT = "/data/export";
    private static final String API_PATH_DATA_EXPORT_STATUS = "/data/export/history";
    private static final String API_PATH_DATA_IMPORT_STATUS = "/data/import/history";
    // 認証エンドポイント
    private static final String API_PATH_AUTH_ACTIVATION_REQUEST = "/auth/activation/request";
    private static final String API_PATH_AUTH_ACTIVATION_COMPLETE = "/auth/activation/complete";
    private static final String API_PATH_AUTH_LOGIN = "/auth/login";
    private static final String API_PATH_AUTH_PASSWORD_RESET_REQUEST = "/auth/password-reset/request";
    private static final String API_PATH_AUTH_PASSWORD_RESET_COMPLETE = "/auth/password-reset/complete";

    // 権限エンドポイント
    private static final String API_PATH_PERMISSIONS = "/permissions/";
    private static final String API_PATH_PERMISSIONS_USER = "/permissions/user";

    // メッセージエンドポイント
    private static final String API_PATH_MESSAGE = "/message/";
    private static final String API_PATH_MESSAGE_ANNOUNCE = "/message/announce";

    // HTTPメソッド定数
    private static final String HTTP_GET = "GET";
    private static final String HTTP_POST = "POST";
    private static final String HTTP_OPTIONS = "OPTIONS";

    // CORSヘッダー定数
    private static final String HEADER_ORIGIN = "Access-Control-Allow-Origin";
    private static final String HEADER_METHODS = "Access-Control-Allow-Methods";
    private static final String HEADER_HEADERS = "Access-Control-Allow-Headers";
    private static final String HEADER_CREDENTIALS = "Access-Control-Allow-Credentials";
    private static final String HEADER_AUTH = "Authorization";
    private static final String BEARER_PREFIX = "Bearer ";

    private final FileController fileController;
    private final DataController dataController;
    private final PermissionController permissionController;
    private final AnnounceMessageController announceMessageController;
    private final UserInfoApplicationService businessInfoService;

    /**
     * コントローラーとサービスを初期化するコンストラクタ
     */
    public LambdaHandler() {
        // 設定を初期化
        ConfigurationInitializer.initialize();

        // コントローラーとサービスの初期化
        this.fileController = new FileController();
        this.dataController = new DataController();
        this.permissionController = new PermissionController();
        this.announceMessageController = new AnnounceMessageController();
        businessInfoService = new UserInfoApplicationService();
        // デバッグログ記録
        logger.info("""
            Lambda関数が初期化されました
            現在の作業ディレクトリ: {}
            """,
                System.getProperty("user.dir")
        );
    }

    /**
     * リクエストを処理するメインハンドラーメソッド
     * @param request APIゲートウェイリクエスト
     * @param context Lambda実行コンテキスト
     * @return APIゲートウェイレスポンス
     */
    @Override
    public APIGatewayProxyResponseEvent handleRequest(APIGatewayProxyRequestEvent request, Context context) {
        // トレーシング用のリクエストIDをセット
        if (context != null) {
            MDC.put("awsRequestId", context.getAwsRequestId());
        } else {
            MDC.put("awsRequestId", "unknown");
        }

        try {
            var path = request.getPath();
            var method = request.getHttpMethod();

            logger.info("リクエスト処理: {} {}", method, path);

            // リクエストタイプに基づいて処理を分岐
            if (HTTP_OPTIONS.equals(method)) {
                return handleCorsResponse();
            }

            // リクエストをルーティングして共通レスポンスを取得
            CommonResult<?> result = routeRequest(request, path, context);

            // 共通レスポンスをAPI Gatewayレスポンスに変換
            return ResponseUtil.toApiGatewayResponse(result);

        } catch (Exception e) {
            // 未処理の例外は共通レスポンスに変換
            CommonResult<?> result = GlobalExceptionHandler.handleException(e);
            return ResponseUtil.toApiGatewayResponse(result);
        } finally {
            // MDC クリーンアップ
            MDC.clear();
            // RequestContext クリーンアップ
            RequestContext.clear();
        }
    }

    /**
     * リクエストをエンドポイントにルーティングする
     * @param request APIゲートウェイリクエスト
     * @param path リクエストパス
     * @param context Lambda実行コンテキスト
     * @return 共通レスポンス
     */
    private CommonResult<?> routeRequest(APIGatewayProxyRequestEvent request, String path, Context context) {
        try {
            // 認証不要のエンドポイントをチェック
            if (isPublicEndpoint(path)) {
                logger.debug("認証不要エンドポイントにルーティング: {}", path);
//                return handleAuthEndpoint(request, path);
            }

            // 認証トークンの抽出
            // TODO
//            var token = extractBearerToken(request);
//            if (token == null) {
//                logger.warn("認証トークンが見つかりません: {} {}", request.getHttpMethod(), path);
//                return GlobalExceptionHandler.handleError(GlobalMessageConstants.UNAUTHORIZED);
//            }

            // JWTトークンの検証（複数の認証方式をサポート）
            UserInfo userInfo = new UserInfo();
            try {
                logger.debug("認証トークンの検証を開始...");
                // TODO
//                userInfo = ApiAuthMiddleware.validateJwtToken(token);
                Map<String, String> headers = request.getHeaders();
                if (headers != null) {
                    headers.forEach((key, value) ->
                        logger.info("Header: {} = {}", key, value)
                    );
                }
                userInfo.setShainCode(request.getHeaders().get("shaincode"));
                userInfo.setSystemOperationCompanyCode(request.getHeaders().get("companycode"));

                // UserBusinessInfoApplicationServiceを使用して完整な業務情報を取得
                logger.debug("ユーザー業務情報取得開始: 社員コード={}", userInfo.getShainCode());
                UserBusinessInfo businessInfo = businessInfoService.getUserBusinessInfo(userInfo);

                // 取得した業務情報をUserInfoに設定
                if (businessInfo != null) {
                    userInfo.setUnitCode(businessInfo.getUnitCode());
                    userInfo.setPositionCode(businessInfo.getPositionCode());
                    userInfo.setAreaCode(businessInfo.getAreaCode());
                    userInfo.setAreaName(businessInfo.getAreaName());
                    userInfo.setGroupCode(businessInfo.getGroupCode());
                }

                // RequestContextにもユーザー情報を設定
                RequestContext.setUserInfo(userInfo);
            } catch (Exception e) {
                logger.debug("JWT認証でエラー: {} ", e.getMessage());
                return handleAuthenticationException(e);
            }

            // ファイル関連エンドポイント
            if (path.startsWith(API_PATH_FILES)) {
                logger.debug("ファイルエンドポイントにルーティング: {}", path);
                return handleFileEndpoint(request, path, userInfo);
            }

            // データ管理エンドポイント
            if (path.startsWith(API_PATH_DATA)) {
                logger.debug("データ管理エンドポイントにルーティング: {}", path);
                return handleDataEndpoint(request, path, userInfo, context);
            }

            // 権限エンドポイント
            if (path.startsWith(API_PATH_PERMISSIONS)) {
                logger.debug("権限エンドポイントにルーティング: {}", path);
                return handlePermissionEndpoint(request, path, userInfo);
            }

            // メッセージエンドポイント
            if (path.startsWith(API_PATH_MESSAGE)) {
                logger.debug("メッセージエンドポイントにルーティング: {}", path);
                return handleMessageEndpoint(request, path, userInfo);
            }

            logger.warn("一致するルートが見つかりません: {} {}", request.getHttpMethod(), path);
            return GlobalExceptionHandler.handleError(GlobalMessageConstants.NOT_FOUND);

        } catch (Exception e) {
            return GlobalExceptionHandler.handleException(e);
        }
    }

    /**
     * 認証エラーを処理する
     * @param e 認証中に発生した例外
     * @return 共通レスポンス
     */
    private CommonResult<?> handleAuthenticationException(Exception e) {
        if (e instanceof ServiceException) {
            return GlobalExceptionHandler.handleException(e);
        } else {
            return GlobalExceptionHandler.handleError(GlobalMessageConstants.AUTH_TOKEN_INVALID);
        }
    }

    /**
     * ファイル関連エンドポイントのリクエストを処理する
     * @param request APIゲートウェイリクエスト
     * @param path リクエストパス
     * @param userInfo 認証されたユーザー情報
     * @return 共通レスポンス
     */
    private CommonResult<?> handleFileEndpoint(APIGatewayProxyRequestEvent request, String path, UserInfo userInfo) {
        try {
            var method = request.getHttpMethod();

            // アップロードURL生成エンドポイント
            if (path.equals(API_PATH_FILES_UPLOAD_URL) && HTTP_POST.equals(method)) {
                logger.info("アップロードURL生成エンドポイント: ユーザー={}", userInfo.getShainCode());
                return fileController.generateUploadUrl(request, userInfo);
            }

            // ダウンロードURL生成エンドポイント
            if (path.equals(API_PATH_FILES_DOWNLOAD_URL) && HTTP_POST.equals(method)) {
                logger.info("ダウンロードURL生成エンドポイント: ユーザー={}", userInfo.getShainCode());
                return fileController.generateDownloadUrl(request, userInfo);
            }

            // 見つからない場合は404エラーとして処理
            return GlobalExceptionHandler.handleError(GlobalMessageConstants.NOT_FOUND);
        } catch (Exception e) {
            return GlobalExceptionHandler.handleException(e);
        }
    }

    /**
     * データ管理エンドポイントのリクエストを処理する
     * @param request APIゲートウェイリクエスト
     * @param path リクエストパス
     * @param userInfo 認証されたユーザー情報
     * @param context Lambda実行コンテキスト
     * @return 共通レスポンス
     */
    private CommonResult<?> handleDataEndpoint(APIGatewayProxyRequestEvent request, String path,
                                               UserInfo userInfo, Context context) {
        try {
            var method = request.getHttpMethod();

            // データインポートエンドポイント
            if (path.equals(API_PATH_DATA_IMPORT) && HTTP_POST.equals(method)) {
                logger.info("データインポートエンドポイント: ユーザー={}", userInfo.getShainCode());
                return dataController.importData(request, userInfo, context);
            }

            // データエクスポートエンドポイント
            if (path.equals(API_PATH_DATA_EXPORT) && HTTP_POST.equals(method)) {
                logger.info("データエクスポートエンドポイント: ユーザー={}", userInfo.getShainCode());
                return dataController.exportData(request, userInfo,context);
            }

            // エクスポート履歴確認エンドポイント（ユーザーの全データ）
            if (path.equals(API_PATH_DATA_EXPORT_STATUS) && HTTP_GET.equals(method)) {
                logger.info("エクスポート履歴確認: ユーザー={}",  userInfo.getShainCode());
                return dataController.getExportHistory(request, userInfo);
            }

            // インポート履歴確認エンドポイント（ユーザーの全データ）
            if (path.equals(API_PATH_DATA_IMPORT_STATUS) && HTTP_GET.equals(method)) {
                logger.info("インポート履歴確認: ユーザー={}",  userInfo.getShainCode());
                return dataController.getImportHistory(request, userInfo);
            }

            // 見つからない場合は404エラーとして処理
            return GlobalExceptionHandler.handleError(GlobalMessageConstants.NOT_FOUND);
        } catch (Exception e) {
            return GlobalExceptionHandler.handleException(e);
        }
    }

    /**
     * 認証不要のエンドポイントかどうかを判定
     * @param path リクエストパス
     * @return 認証不要の場合true
     */
    private boolean isPublicEndpoint(String path) {
        return path.equals(API_PATH_AUTH_ACTIVATION_REQUEST) ||
               path.equals(API_PATH_AUTH_ACTIVATION_COMPLETE) ||
               path.equals(API_PATH_AUTH_LOGIN) ||
               path.equals(API_PATH_AUTH_PASSWORD_RESET_REQUEST) ||
               path.equals(API_PATH_AUTH_PASSWORD_RESET_COMPLETE);
    }



    /**
     * 権限エンドポイントのリクエストを処理する
     * @param request APIゲートウェイリクエスト
     * @param path リクエストパス
     * @param userInfo 認証されたユーザー情報
     * @return 共通レスポンス
     */
    private CommonResult<?> handlePermissionEndpoint(APIGatewayProxyRequestEvent request, String path, UserInfo userInfo) {
        try {
            var method = request.getHttpMethod();

            // ユーザー権限一覧取得
            if (path.equals(API_PATH_PERMISSIONS_USER) && HTTP_POST.equals(method)) {
                logger.info("ユーザー権限一覧取得エンドポイント: ユーザー={}", userInfo.getShainCode());
                APIGatewayProxyResponseEvent response = permissionController.getUserPermissions(request, userInfo);
                return convertApiGatewayResponseToCommonResult(response);
            }

            // 見つからない場合は404エラーとして処理
            return GlobalExceptionHandler.handleError(GlobalMessageConstants.NOT_FOUND);
        } catch (Exception e) {
            return GlobalExceptionHandler.handleException(e);
        }
    }

    /**
     * メッセージエンドポイントのリクエストを処理する
     * @param request APIゲートウェイリクエスト
     * @param path リクエストパス
     * @param userInfo 認証されたユーザー情報
     * @return 共通レスポンス
     */
    private CommonResult<?> handleMessageEndpoint(APIGatewayProxyRequestEvent request, String path, UserInfo userInfo) {
        try {
            var method = request.getHttpMethod();

            // アナウンスメッセージ一覧取得
            if (path.equals(API_PATH_MESSAGE_ANNOUNCE) && HTTP_POST.equals(method)) {
                logger.info("アナウンスメッセージ一覧取得エンドポイント: ユーザー={}", userInfo.getShainCode());
                return announceMessageController.getAnnounceMessage(request, userInfo);
            }

            // 見つからない場合は404エラーとして処理
            return GlobalExceptionHandler.handleError(GlobalMessageConstants.NOT_FOUND);
        } catch (Exception e) {
            return GlobalExceptionHandler.handleException(e);
        }
    }

    /**
     * APIGatewayProxyResponseEventをCommonResultに変換する
     * @param response APIGatewayレスポンス
     * @return 共通レスポンス
     */
    private CommonResult<?> convertApiGatewayResponseToCommonResult(APIGatewayProxyResponseEvent response) {
        try {
            if (response.getStatusCode() == 200) {
                // レスポンスボディをCommonResultとしてパース
                ObjectMapper objectMapper = new ObjectMapper();
                return objectMapper.readValue(response.getBody(), CommonResult.class);
            } else {
                // エラーレスポンスの場合
                return CommonResult.error(response.getStatusCode(), "リクエスト処理に失敗しました");
            }
        } catch (Exception e) {
            logger.error("APIGatewayレスポンスの変換に失敗しました", e);
            return CommonResult.error(GlobalMessageConstants.INTERNAL_SERVER_ERROR.getCode(), "レスポンス変換エラー");
        }
    }

    /**
     * CORSプリフライトリクエストのレスポンスを生成する
     * @return CORSヘッダー付きのレスポンス
     */
    private APIGatewayProxyResponseEvent handleCorsResponse() {
        var response = new APIGatewayProxyResponseEvent();
        response.setStatusCode(200);

        var headers = new HashMap<String, String>();
        headers.put(HEADER_ORIGIN, "*");
        headers.put(HEADER_METHODS, "GET, POST, PUT, DELETE, OPTIONS");
        headers.put(HEADER_HEADERS, "Content-Type, Authorization");
        headers.put(HEADER_CREDENTIALS, "true");

        response.setHeaders(headers);
        return response;
    }

    /**
     * リクエストからBearer認証トークンを抽出する
     * @param request APIゲートウェイリクエスト
     * @return 抽出されたトークン、または存在しない場合はnull
     */
    private String extractBearerToken(APIGatewayProxyRequestEvent request) {
        return Optional.ofNullable(request.getHeaders())
                .map(headers -> headers.get(HEADER_AUTH))
                .filter(auth -> auth.startsWith(BEARER_PREFIX))
                .map(auth -> auth.substring(BEARER_PREFIX.length()))
                .orElse(null);
    }
}