package com.ms.bp.shared.util;

import com.ms.bp.shared.common.exception.Message;
import com.ms.bp.shared.common.exception.ParameterizedErrorCode;
import com.ms.bp.shared.common.exception.ServiceException;
import com.ms.bp.shared.common.exception.ValidationException;

/**
 * エラーコード関連のユーティリティクラス
 * 通用的なパラメータ化エラー処理機能を提供
 */
public class MessageCodeUtil {

    /**
     * パラメータ化されたエラーコードを作成
     *
     * @param message ベースとなるエラーコード
     * @param parameters パラメータ
     * @return ParameterizedErrorCode
     */
    public static ParameterizedErrorCode createParameterizedError(Message message, Object... parameters) {
        return new ParameterizedErrorCode(message, parameters);
    }

    /**
     * パラメータ化されたエラーコードからServiceExceptionを作成
     *
     * @param message ベースとなるエラーコード
     * @param parameters パラメータ
     * @return ServiceException
     */
    public static ServiceException createParameterizedException(Message message, Object... parameters) {
        return new ServiceException(message, parameters);
    }

    /**
     * ValidationException
     *
     * @param message ベースとなるエラーコード
     * @param parameters パラメータ
     * @return ValidationException
     */
    public static ValidationException createValidationException(Message message, Object... parameters) {
        return new ValidationException(message, parameters);
    }

    /**
     * エラーコードとパラメータを使用してメッセージをフォーマット
     *
     * @param message エラーコード（メッセージテンプレートを含む）
     * @param parameters パラメータ
     * @return フォーマット済みメッセージ
     */
    public static String formatMessage(Message message, Object... parameters) {
        return MessageFormatter.format(message.getMsg(), parameters);
    }

}
