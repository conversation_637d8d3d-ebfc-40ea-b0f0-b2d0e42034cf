"""
RAGFlow Integration Configuration for GPT Researcher
"""

import os
from gpt_researcher.config import Config


class RAGFlowConfig(Config):
    """Extended configuration for RAGFlow integration"""
    
    def __init__(self, config_file: str = None):
        super().__init__(config_file)
        
        # RAGFlow specific settings
        self.ragflow_base_url = os.getenv("RAGFLOW_BASE_URL")
        self.ragflow_api_key = os.getenv("RAGFLOW_API_KEY")
        self.ragflow_dataset_ids = self._parse_comma_separated("RAGFLOW_DATASET_IDS")
        self.ragflow_document_ids = self._parse_comma_separated("RAGFLOW_DOCUMENT_IDS")
        
        # Search parameters
        self.ragflow_similarity_threshold = float(os.getenv("RAGFLOW_SIMILARITY_THRESHOLD", "0.2"))
        self.ragflow_vector_weight = float(os.getenv("RAGFLOW_VECTOR_WEIGHT", "0.3"))
        self.ragflow_top_k = int(os.getenv("RAGFLOW_TOP_K", "20"))
        self.ragflow_page_size = int(os.getenv("RAGFLOW_PAGE_SIZE", "30"))
        
        # Hybrid search configuration
        self.enable_hybrid_search = os.getenv("ENABLE_HYBRID_SEARCH", "true").lower() == "true"
        self.ragflow_weight = float(os.getenv("RAGFLOW_WEIGHT", "0.7"))  # Weight for RAGFlow results
        self.web_search_weight = float(os.getenv("WEB_SEARCH_WEIGHT", "0.3"))  # Weight for web results
    
    def _parse_comma_separated(self, env_var: str) -> list:
        """Parse comma-separated environment variable into list"""
        value = os.getenv(env_var)
        if value:
            return [item.strip() for item in value.split(',') if item.strip()]
        return []
    
    def validate_ragflow_config(self) -> bool:
        """Validate RAGFlow configuration"""
        if not self.ragflow_base_url:
            print("Warning: RAGFLOW_BASE_URL not set")
            return False
        
        if not self.ragflow_api_key:
            print("Warning: RAGFLOW_API_KEY not set")
            return False
        
        if not self.ragflow_dataset_ids and not self.ragflow_document_ids:
            print("Warning: Neither RAGFLOW_DATASET_IDS nor RAGFLOW_DOCUMENT_IDS are set")
            return False
        
        return True
    
    def get_ragflow_headers(self) -> dict:
        """Get headers for RAGFlow retriever"""
        return {
            "ragflow_base_url": self.ragflow_base_url,
            "ragflow_api_key": self.ragflow_api_key,
            "ragflow_dataset_ids": ",".join(self.ragflow_dataset_ids) if self.ragflow_dataset_ids else None,
            "ragflow_document_ids": ",".join(self.ragflow_document_ids) if self.ragflow_document_ids else None,
        }