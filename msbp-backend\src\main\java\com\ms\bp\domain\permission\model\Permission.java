package com.ms.bp.domain.permission.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 権限マスタ領域モデル
 * 権限に関するビジネスロジックを含む富ドメインモデル
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Permission {
    
    /**
     * 権限コード
     */
    private String permissionCode;
    
    /**
     * システム運用企業コード
     */
    private String systemOperationCompanyCode;
    
    /**
     * 判定区分
     */
    private String hantPttrn;
    
    /**
     * 判定コード
     */
    private String hantCode;

    /**
     * 権限開始日 (YYYYMMDD)
     */
    private String permissionStartDate;
    
    /**
     * 権限終了日 (YYYYMMDD)
     */
    private String permissionEndDate;

    /**
     * 対象コード
     * HANT_PTTRNが'1'の場合はunitCode、'2'の場合はareaCode
     * M_KEN_RULEテーブルのSHR_CODEに対応
     */
    private String targetCode;

}
