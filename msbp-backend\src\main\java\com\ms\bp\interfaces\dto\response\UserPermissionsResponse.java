package com.ms.bp.interfaces.dto.response;

import com.ms.bp.domain.master.model.AreaInfo;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

/**
 * ユーザー権限一覧応答DTO
 * ユーザー権限取得APIのレスポンスデータを表現する
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserPermissionsResponse {

    /**
     * 社員コード
     */
    private String shainCode;

    /**
     * エリアコード
     */
    private String areaCode;

    /**
     * エリア名称
     */
    private String areaName;
    /**
     * システム運用企業コード
     */
    private String systemOperationCompanyCode;

    /**
     * ユニットコード
     */
    private String unitCode;

    /**
     * 役職区分コード
     */
    private String positionCode;

    /**
     * グループコード
     */
    private String groupCode;

    /**
     * 役職区分判定要否 0:否 1:要
     */
    private String positionSpecialCheck;

    /**
     * 権限リスト
     */
    private List<PermissionInfo> permissions;

    /**
     * エリア情報リスト
     * areaPatternが"1"の権限が存在する場合のみ設定される
     */
    private List<AreaInfo> areaInfos;

    // ==================== 内部クラス ====================

    /**
     * 権限情報DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PermissionInfo {

        /**
         * 権限コード
         */
        private String permissionCode;

        /**
         * ファイル種類コード
         */
        private String fileTypeCode;

        /**
         * 操作区分 (1:アップロード 2:ダウンロード)
         */
        private String operationDivision;

        /**
         * エリアパターン
         */
        private String areaPattern;

        /**
         * 判定コード
         */
        private String hantCode;
    }
}
