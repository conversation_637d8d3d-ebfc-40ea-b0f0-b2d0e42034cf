package com.ms.bp.shared.common.io.converter;

import com.ms.bp.shared.common.io.options.ImportOptions;
import java.util.Map;

/**
 * データベースフィールドマッピング可能なオブジェクトのインターフェース
 * パフォーマンス最適化：反射を完全に回避し、直接メソッド呼び出しを可能にします
 *
 * すべてのインポート用DTOはこのインターフェースを実装する必要があります
 * テンプレートメソッドパターンを使用して固定値フィールド注入を統一処理
 */
public interface DatabaseMappable {

    /**
     * DTOをデータベースフィールドのMapに変換します（テンプレートメソッド）
     * 固定値フィールドの注入を統一処理
     *
     * @param isInsert 挿入操作であるかどうか
     * @param options インポートオプション（固定値フィールドを含む）
     * @return データベースフィールドのMap
     */
    default Map<String, Object> toDatabaseFields(boolean isInsert, ImportOptions options) {
        // 1. 各DTOの核心フィールドマッピングを実行
        Map<String, Object> fields = toDatabaseFieldsCore(isInsert);

        // 2. 固定値フィールドを注入（統一処理）
        if (options != null && options.getAdditionalFields() != null) {
            fields.putAll(options.getAdditionalFields());
        }

        return fields;
    }

    /**
     * DTOの核心フィールドをデータベースフィールドのMapに変換します（サブクラスで実装）
     * 各DTOは固定値フィールド以外の通常フィールドのみを処理
     *
     * @param isInsert 挿入操作であるかどうか
     * @return データベースフィールドのMap（固定値フィールドは含まない）
     */
    Map<String, Object> toDatabaseFieldsCore(boolean isInsert);
}
