package com.ms.bp.shared.common.io.validator;

import com.ms.bp.shared.common.exception.ValidationError;
import com.ms.bp.shared.common.io.options.ImportOptions;

import java.util.List;
import java.util.Map;

/**
 * データ検証用の函数式インターフェース
 * インポート時のデータ検証ロジックを定義する
 */
@FunctionalInterface
public interface DataValidator {
    /**
     * データを検証する
     * 
     * @param data 検証対象のデータ
     * @param options インポートオプション
     * @return 検証エラーのリスト（エラーがない場合は空のリスト）
     */
    List<ValidationError> validate(Map<String, Object> data, ImportOptions options);
}
