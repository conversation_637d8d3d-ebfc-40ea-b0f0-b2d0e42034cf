{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "./dist", "declaration": true, "declarationDir": "./dist", "emitDeclarationOnly": false, "sourceMap": true, "noEmit": false, "jsx": "react-jsx", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true}, "include": ["src/**/*.ts", "src/**/*.tsx", "components/**/*.jsx", "components/**/*.tsx"], "exclude": ["node_modules", "**/*.test.ts", "**/*.test.tsx"]}