package com.ms.bp.domain.file.repository;

import com.ms.bp.domain.file.model.ImportJobStatus;
import java.util.List;

/**
 * インポートジョブステータスリポジトリ
 * ファイル処理領域のインポートジョブステータス永続化を抽象化
 */
public interface ImportJobStatusRepository {

    /**
     * インポートジョブステータスを保存
     * @param jobStatus ジョブステータス
     */
    void save(ImportJobStatus jobStatus);

    /**
     * インポートジョブステータスを更新
     * @param jobStatus ジョブステータス
     */
    void update(ImportJobStatus jobStatus);

    /**
     * 履歴番号でインポートジョブステータスを取得
     * @param rrkBango 履歴番号
     * @return インポートジョブステータス
     */
    ImportJobStatus findByRrkBango(Long rrkBango);

    /**
     * 社員コードでインポートジョブステータスリストを取得
     * @param shainCode 社員コード
     * @param limit 取得件数上限
     * @param offset オフセット
     * @return インポートジョブステータスリスト
     */
    List<ImportJobStatus> findByShainCode(String shainCode,String systemOperationCompanyCode, int limit, int offset);

}
