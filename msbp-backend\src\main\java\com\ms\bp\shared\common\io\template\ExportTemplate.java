package com.ms.bp.shared.common.io.template;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.db.AuroraConnectionManager;
import com.ms.bp.shared.common.db.JdbcTemplate;
import com.ms.bp.shared.common.io.model.ExportResult;
import com.ms.bp.shared.common.io.options.ExportOptions;
import com.ms.bp.shared.common.io.strategy.DataWriteStrategy;
import com.ms.bp.shared.common.io.factory.DataWriteStrategyFactory;
import com.ms.bp.infrastructure.external.s3.S3Service;
import com.ms.bp.domain.file.base.DataExpander;
import com.ms.bp.domain.file.base.ExpansionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.sql.*;
import java.util.*;
import java.util.function.Function;

import static com.ms.bp.shared.util.MessageCodeUtil.formatMessage;

/**
 * エクスポートテンプレート (JDBC版)
 * （テンプレートメソッドパターン：エクスポートプロセスを定義し、特定のステップを拡張可能にする）
 * シングルスレッドで順次処理を実行
 */
public abstract class ExportTemplate<T> implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(ExportTemplate.class);
    private static final int DEFAULT_BUFFER_SIZE = 8192;
    private final S3Service s3Service = new S3Service();

    /**
     * リソースをクリーンアップ
     */
    @Override
    public void close() {
        try {
            s3Service.close();
        } catch (Exception e) {
            logger.error("リソースのクローズ中にエラーが発生しました", e);
        }
    }

    /**
     * テンプレートメソッド：統合エクスポートプロセスを定義
     * データ展開の有無に関わらず統一的に処理
     * @param expander データ展開処理器（nullの場合は展開なし）
     * @param expansionContext 展開処理のコンテキスト（expanderがnullの場合は無視）
     */
    public final ExportResult executeExport(
            String sqlTemplate,
            Map<String, Object> params,
            DataExpander expander,
            Function<Map<String, Object>, Map<String, Object>> dataFormatter,
            ExportOptions options,
            Context lambdaContext,
            ExpansionContext expansionContext) {

        ExportResult result = new ExportResult();
        long startTime = System.currentTimeMillis();

        logger.info("データエクスポート開始: params={}", params.values());

        Path tempFile = null;
        OutputStream outputStream = null;
        DataWriteStrategy<Map<String, Object>> writeStrategy;

        try {
            // ステップ1：一時ファイルと出力ストリームを作成
            tempFile = createTempFile(options);
            outputStream = Files.newOutputStream(tempFile);

            // パフォーマンス向上のためのバッファ追加
            outputStream = new BufferedOutputStream(outputStream, DEFAULT_BUFFER_SIZE);

            // データ書き込み戦略を作成
            writeStrategy = DataWriteStrategyFactory.createStrategy(options.getFormat());

            // ステップ2：列定義があるか確認
            List<String> columns = options.getColumns();
            if (columns == null || columns.isEmpty()) {
                throw new IllegalArgumentException("オプションに列リストが指定されていません");
            }

            // ステップ3：ヘッダー行を書き込む
            if (options.isIncludeHeader()) {
                writeStrategy.writeHeader(columns, outputStream, options);
            }

            // ステップ4：データ処理の実行（統一処理）
            processData(
                    sqlTemplate, params, expander, dataFormatter,
                    options, lambdaContext, expansionContext, result,
                    outputStream, writeStrategy
            );

            // ステップ5：フッターを書き込む
            writeStrategy.writeFooter(outputStream, options);

            // ステップ6：エクスポートを完了
            writeStrategy.finish(outputStream, options);

            outputStream.close();
            outputStream = null;

            long exportedCount = result.getExportedCount();
            if (exportedCount == 0) {
                // データがない場合はメッセージを書き込む
                writeErrorFile(tempFile,  formatMessage(GlobalMessageConstants.ERR_023), options, result);
            } else {
                // エクスポート結果を設定
                result.setFilePath(tempFile);
                result.addStatistic("completed", true);
            }

            logger.info("データエクスポート完了: レコード数={}", result.getExportedCount());

        } catch (Exception e) {
            logger.error("エクスポート失敗しました", e);
            // エラーメッセージをフォーマット
            String errorMessage = formatMessage(GlobalMessageConstants.ERR_011, options.getFileName(), e.getMessage());
            logger.error(errorMessage);
            writeErrorFile(tempFile, errorMessage, options, result);
        } finally {
            // リソースを閉じる
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    logger.error("出力ストリームを閉じる際にエラーが発生しました", e);
                }
            }

            // 実行統計情報を追加
            long endTime = System.currentTimeMillis();
            result.addStatistic("executionTimeMs", endTime - startTime);
        }

        return result;
    }

    /**
     * データがない場合のメッセージを書き込む
     */
    private void writeErrorFile(
            Path tempFile,
            String errorMessage,
            ExportOptions options,
            ExportResult result ) {

        // エラーファイル名を生成（元のファイル名の前に"error_"を付加）
        String errorFileName = "error_" + options.getFileName();

        // エラーファイルのパスを生成（元のファイルと同じディレクトリに配置）
        Path statusFile = Objects.requireNonNull(tempFile).getParent().resolve(errorFileName);

        // エラーファイルにエラーメッセージを書き込む
        try (PrintWriter errorWriter = new PrintWriter(Files.newBufferedWriter(statusFile, StandardCharsets.UTF_8))) {
            // エラーメッセージを書き込む
            errorWriter.println(errorMessage);
            // バッファを強制的にフラッシュして、確実にファイルに書き込む
            errorWriter.flush();
            // エクスポート結果にエラーファイルのパスを設定
            result.setFilePath(statusFile);
            // エクスポートが完了していないことを記録
            result.addStatistic("completed", false);
        } catch (IOException ex) {
            // エラーファイルの書き込みに失敗した場合のログ出力
            logger.error("エラーステータスファイルの書き込みに失敗しました", ex);
        }
    }

    /**
     * 一時ファイルを作成
     * ExportOptionsのfileNameが指定されている場合はそれを使用、
     * そうでなければシステムが自動生成
     */
    private Path createTempFile(ExportOptions options) throws IOException {
        if (options.getFileName() != null && !options.getFileName().trim().isEmpty()) {
            // 指定されたファイル名を使用
            String fileName = options.getFileName().trim();

            // 拡張子がない場合は.csvを追加
            if (!fileName.toLowerCase().endsWith(".csv")) {
                fileName += ".csv";
            }

            // 一時ディレクトリに指定されたファイル名でファイルを作成
            Path tempDir = Files.createTempDirectory("export_");
            Path tempFile = tempDir.resolve(fileName);

            // ファイルを作成
            Files.createFile(tempFile);

            logger.debug("指定されたファイル名で一時ファイルを作成: {}", tempFile.getFileName());
            return tempFile;
        } else {
            // デフォルトの一時ファイル作成
            return Files.createTempFile("export_", ".csv");
        }
    }

    /**
     * シングルスレッドによるデータ処理を実行
     *
     * データベース接続をページデータ查询毎に独立管理し、ファイル書き込み処理を事務範囲外で実行
     */
    private void processData(
            String sqlTemplate,
            Map<String, Object> params,
            DataExpander expander,
            Function<Map<String, Object>, Map<String, Object>> dataFormatter,
            ExportOptions options,
            Context lambdaContext,
            ExpansionContext expansionContext,
            ExportResult result,
            OutputStream outputStream,
            DataWriteStrategy<Map<String, Object>> writeStrategy) throws SQLException, IOException {

        boolean hasExpansion = expander != null;

        int pageIndex = 0;
        boolean hasMoreData = true;
        int totalExpandedRecords = 0;
        int totalOriginalRecords = 0;

        try {
            while (hasMoreData) {
                // タイムアウトチェック
                if (AuroraConnectionManager.isNearTimeout(lambdaContext)) {
                    logger.warn("Lambda実行がタイムアウトに近づいているため、エクスポートを停止します");
                    break;
                }

                int offset = pageIndex * options.getBatchSize();

                try {
                    // ページデータ查询は独立した事務で実行され、查询完了後即座に接続を解放
                    List<Map<String, Object>> pageData = fetchPageData(
                            sqlTemplate, params, options.getBatchSize(), offset
                    );

                    // これ以上データがない場合は終了
                    if (pageData.isEmpty()) {
                        break;
                    }

                    totalOriginalRecords += pageData.size();

                    // 【事務範囲外】ページデータの処理（展開の有無で分岐）
                    // データベース接続は既に解放済み、ファイル書き込み処理のみ実行
                    int pageRecordsWritten = 0;
                    if (hasExpansion) {
                        // データ展開ありの処理
                        for (Map<String, Object> rawData : pageData) {
                            try {
                                // ステップ1：データ展開処理
                                List<Map<String, Object>> expandedRows;
                                if (expander.needsExpansion(rawData)) {
                                    expandedRows = expander.expandData(rawData, expansionContext);
                                    logger.debug("レコードID {} を {} 行に展開",
                                               rawData.get("id"), expandedRows.size());
                                } else {
                                    expandedRows = Collections.singletonList(rawData);
                                }

                                // ステップ2：展開後の各行を格式化して書き込み
                                for (Map<String, Object> expandedRow : expandedRows) {
                                    // データ格式化処理
                                    Map<String, Object> formattedData = dataFormatter.apply(expandedRow);
                                    // ファイル書き込み（事務範囲外で実行）
                                    writeStrategy.writeRecord(formattedData, options.getColumns(), outputStream, options);
                                    pageRecordsWritten++;
                                }

                            } catch (Exception e) {
                                logger.error("レコード展開処理エラー: レコードID={}", rawData.get("id"), e);
                                // エラー時は処理を継続（continueOnError相当）
                            }
                        }
                    } else {
                        // データ展開なしの処理
                        for (Map<String, Object> rawData : pageData) {
                            try {
                                // データ格式化処理（必要に応じて）
                                Map<String, Object> formattedData = dataFormatter.apply(rawData);
                                // ファイル書き込み（事務範囲外で実行）
                                writeStrategy.writeRecord(formattedData, options.getColumns(), outputStream, options);
                                pageRecordsWritten++;

                            } catch (Exception e) {
                                logger.error("レコード処理エラー", e);
                                throw e;
                            }
                        }
                    }

                    totalExpandedRecords += pageRecordsWritten;

                    logger.debug("ページ {} のレコードを {} 件書き込みました", pageIndex, pageRecordsWritten);

                    // フルページでない場合は、これ以上データがない可能性が高い
                    if (pageData.size() < options.getBatchSize()) {
                        hasMoreData = false;
                    }

                    pageIndex++;

                } catch (Exception e) {
                    logger.error("ページ {} の処理中にエラーが発生しました", pageIndex, e);
                    throw e;
                }
            }

            // 統計情報を記録
            result.setExportedCount(totalExpandedRecords);
            if (hasExpansion) {
                // 展開統計情報を記録
                result.addStatistic("originalRecords", totalOriginalRecords);
                result.addStatistic("expandedRecords", totalExpandedRecords);

                logger.info("データ展開処理完了: 元レコード {} 件 → 展開後 {} 件", totalOriginalRecords, totalExpandedRecords);
            }

        } catch (Exception e) {
            logger.error("データ処理中に例外が発生しました", e);
            throw e;
        }
    }


    /**
     * ページデータ毎に新しいデータベース接続を作成し、查询完了後即座に解放
     *
     * @param sqlTemplate SQLテンプレート
     * @param params クエリパラメータ
     * @param pageSize ページサイズ
     * @param offset オフセット
     * @return ページデータのリスト
     * @throws SQLException データベースエラー
     */
    private List<Map<String, Object>> fetchPageData(
            String sqlTemplate,
            Map<String, Object> params,
            int pageSize,
            int offset) throws SQLException {

        // 独立した短期接続を使用してデータを取得
        try (Connection conn = AuroraConnectionManager.getConnection();
             JdbcTemplate template = new JdbcTemplate(conn)) {

            logger.debug("ページデータ用短期接続を作成: page={}, offset={}", pageSize, offset);

            // 既存のfetchPageDataメソッドを呼び出して互換性を保持
            List<Map<String, Object>> result = fetchPageData(sqlTemplate, params, template, pageSize, offset);

            logger.debug("ページデータ完了、接続解放: 取得件数={}", result.size());
            return result;

        } catch (SQLException e) {
            logger.error("ページデータ中にデータベースエラーが発生: page={}, offset={}", pageSize, offset, e);
            throw e;
        }
    }

    /**
     * 指定ページのデータを取得 (JDBC版) - 直接Mapデータを返す
     * 【互換性保持】既存のサブクラスでオーバーライド可能な従来メソッド
     * 新しい事務最適化アーキテクチャでは fetchPageDataWithOwnConnection から呼び出される
     */
    protected List<Map<String, Object>> fetchPageData(
            String sqlTemplate,
            Map<String, Object> params,
            JdbcTemplate template,
            int pageSize,
            int offset) throws SQLException {

        String paginatedSql = sqlTemplate + " LIMIT " + pageSize + " OFFSET " + offset;

        // パラメータを配列に変換
        Object[] paramArray = params != null ? params.values().toArray() : new Object[0];

        return template.query(paginatedSql, paramArray, rs -> {
            Map<String, Object> row = new HashMap<>();
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnLabel(i);
                Object value = rs.getObject(i);
                row.put(columnName, value);
            }

            return row;
        });
    }

}