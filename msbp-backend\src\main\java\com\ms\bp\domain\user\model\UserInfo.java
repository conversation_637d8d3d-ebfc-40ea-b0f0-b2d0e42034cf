package com.ms.bp.domain.user.model;

import com.ms.bp.domain.master.model.AreaInfo;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * ユーザー情報モデル
 * Azure AD認証情報を表現するドメインモデル
 */
@Data
public class UserInfo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 社員コード（権限チェック用）SHAIN_CODE
     */
    private String shainCode;

    /**
     * システム運用企業コード（権限チェック用）
     */
    private String systemOperationCompanyCode;

    /**
     * ユニットコード（権限チェック用）
     */
    private String unitCode;

    /**
     * エリアコード（権限チェック用）
     */
    private String areaCode;
    /**
     * エリア名称
     */
    private String areaName;

    /**
     * 役職区分コード（権限チェック用）
     */
    private String positionCode;

    /**
     * エリア情報リスト
     * areaPatternが"1"の権限が存在する場合のみ設定される
     */
    private List<AreaInfo> areaInfos;

    /**
     * グループコード
     */
    private String groupCode;

    /**
     * 役職区分判定要否 0:否 1:要
     */
    private String positionSpecialCheck;
}