# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This repository contains a specialized bash script for extracting file lists from SVN repositories. The main component is `svn_filelist_bash.sh`, a comprehensive tool for analyzing SVN repositories and exporting file metadata to CSV format.

## Core Architecture

### Main Script: svn_filelist_bash.sh

The script is structured around several key functional areas:

1. **Configuration Management**: Reads CSV configuration files to process multiple repositories
2. **Dual Processing Methods**: 
   - `svn list --xml`: Primary method using SVN client commands
   - `svnlook`: Fallback method for direct repository access
3. **Batch Processing**: Outputs results in configurable batch sizes to CSV files
4. **Resume Capability**: Can skip already processed repositories
5. **HTML Entity Decoding**: Handles internationalized file names properly

### Key Functions

- `main()`: Entry point and orchestration (svn_filelist_bash.sh:699)
- `process_single_repository()`: Core repository processing logic (svn_filelist_bash.sh:630)
- `get_svn_file_list()`: Primary file extraction method selection (svn_filelist_bash.sh:533)
- `process_svn_list_xml_result()`: XML parsing and file metadata extraction (svn_filelist_bash.sh:225)
- `get_svn_file_list_using_svnlook()`: Alternative extraction using svnlook (svn_filelist_bash.sh:374)
- `export_batch_to_csv()`: CSV output generation (svn_filelist_bash.sh:474)

## Usage Commands

### Basic Execution
```bash
./svn_filelist_bash.sh --config-file svn-config.txt
```

### Common Options
```bash
# Custom batch size
./svn_filelist_bash.sh --config-file svn-config.txt --batch-size 500

# Resume mode (skip processed repositories)
./svn_filelist_bash.sh --config-file svn-config.txt --resume-mode

# Verbose logging
./svn_filelist_bash.sh --config-file svn-config.txt --verbose-logging

# Custom output directory
./svn_filelist_bash.sh --config-file svn-config.txt --output-directory /custom/path

# Force specific method
./svn_filelist_bash.sh --config-file svn-config.txt --method svnlook
```

## Configuration Requirements

### Dependencies
The script requires these system dependencies:
- `svn` (Subversion client)
- `svnlook` (Repository examination tool)
- `xmllint` (XML processing)
- `bc` (Basic calculator for size calculations)
- `python3` or `python` (for HTML entity decoding)

### Configuration File Format
Create `svn-config.txt` (or specify custom file) in CSV format:
```
RepositoryName,RepositoryPath,SubDirectory,
MyRepo,/path/to/repo,,
ProjectX,/path/to/project,/trunk/src,
```

## Output Structure

The script generates CSV files with the following naming pattern:
`{RepositoryName}_{SubDirectory}__batch_{BatchNumber}_{Timestamp}_{UniqueID}.csv`

Each CSV contains columns:
- Path: File path within repository
- SizeMB: File size in megabytes  
- SizeBytes: File size in bytes
- LastModified: Last modification timestamp

## Development and Testing

### Running the Script
```bash
# Make script executable (on Unix-like systems)
chmod +x svn_filelist_bash.sh

# Basic execution
./svn_filelist_bash.sh --config-file svn-config.txt

# Test with small batch size for validation
./svn_filelist_bash.sh --config-file svn-config.txt --batch-size 10 --verbose-logging
```

### Testing Methods
The script can be tested with different repository configurations and methods:

```bash
# Test SVN list method specifically
./svn_filelist_bash.sh --config-file svn-config.txt --method svnlist --verbose-logging

# Test SVNLook method specifically  
./svn_filelist_bash.sh --config-file svn-config.txt --method svnlook --verbose-logging

# Test with custom output directory
./svn_filelist_bash.sh --config-file svn-config.txt --output-directory ./test-output
```

### Debugging and Validation
```bash
# Enable verbose logging for detailed progress
./svn_filelist_bash.sh --config-file svn-config.txt --verbose-logging

# Validate dependencies before running
svn --version
svnlook --version  
xmllint --version
bc --version
python3 --version || python --version
```

## Development Notes

- The script handles both Windows (`\`) and Unix (`/`) path separators
- Supports Unicode file names through proper HTML entity decoding
- Implements graceful fallback between SVN access methods
- Uses temporary files for XML processing to handle large repositories
- Provides detailed progress reporting and statistics
- Error handling includes XML validation and dependency checking
- Batch processing prevents memory issues with large repositories
- Resume functionality allows restarting interrupted processes