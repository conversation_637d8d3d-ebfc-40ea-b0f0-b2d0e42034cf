package com.ms.bp.domain.master.repository;

/**
 * ユニットマスタリポジトリ
 * ユニットマスタ領域のデータ永続化を抽象化
 * M_UNITMST（ユニットマスタ）に対応
 * UserXXX命名規則に従い、英文術語との一貫性を保持
 */
public interface UnitMasterRepository {

    /**
     * ユニットコードでユニットマスタの存在チェック
     * 指定されたユニットコードがユニットマスタに存在するかを確認する
     * 
     * @param unitCode ユニットコード（5桁）
     * @return 存在する場合true、存在しない場合false
     */
    boolean existsByUnitCode(String unitCode);
}
