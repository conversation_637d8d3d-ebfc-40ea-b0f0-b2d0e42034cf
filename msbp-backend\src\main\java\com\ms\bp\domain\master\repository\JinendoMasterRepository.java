package com.ms.bp.domain.master.repository;

/**
 * 次年度計画マスタリポジトリ
 * 次年度計画マスタ領域のデータ永続化を抽象化
 * T_JINENDO_KKK（次年度計画マスタ）に対応
 * UserXXX命名規則に従い、英文術語との一貫性を保持
 */
public interface JinendoMasterRepository {

    /**
     * 採算管理コードで次年度計画マスタの存在チェック
     * 指定された採算管理コードが次年度計画マスタに存在するかを確認する
     *
     * @param nendo 年度（4桁）
     * @param saisanCode 採算管理コード（7桁）
     * @param areaCode エリアコード（4桁）
     * @param groupCode グループコード（4桁）
     * @param unitCode ユニットコード（5桁）
     * @return 存在する場合true、存在しない場合false
     */
    boolean existsBySaisanCode(String nendo, String saisanCode, String areaCode, String groupCode, String unitCode);
    /**
     * 採算管理コードで次年度計画マスタの存在チェック
     * 指定された採算管理コードが次年度計画マスタに存在するかを確認する
     *
     * @param nendo 年度（4桁）
     * @param saisanCode 採算管理コード（7桁）
     * @param areaCode エリアコード（4桁）
     * @param groupCode グループコード（4桁）
     * @return 存在する場合true、存在しない場合false
     */
    boolean existsBySaisanCode(String nendo, String saisanCode, String areaCode, String groupCode);
}
