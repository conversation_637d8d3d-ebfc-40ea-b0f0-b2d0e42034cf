{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"id": "byPgKYhAE6gn"}, "outputs": [], "source": ["import os\n", "os.environ['OPENAI_API_KEY'] = 'your_openai_api_key'\n", "os.environ['TAVILY_API_KEY'] = 'your_tavily_api_key' # Get a free key here: https://app.tavily.com"]}, {"cell_type": "code", "source": ["!pip install -U gpt-researcher nest_asyncio"], "metadata": {"id": "-rXET3OZLxwH"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["import nest_asyncio # required for notebooks\n", "nest_asyncio.apply()\n", "\n", "from gpt_researcher import GPTResearcher\n", "import asyncio\n", "\n", "async def get_report(query: str, report_type: str) -> str:\n", "    researcher = GPTResearcher(query, report_type)\n", "    research_result = await researcher.conduct_research()\n", "    report = await researcher.write_report()\n", "    \n", "    # Get additional information\n", "    research_context = researcher.get_research_context()\n", "    research_costs = researcher.get_costs()\n", "    research_images = researcher.get_research_images()\n", "    research_sources = researcher.get_research_sources()\n", "    \n", "    return report, research_context, research_costs, research_images, research_sources\n", "\n", "if __name__ == \"__main__\":\n", "    query = \"Should I invest in Nvidia?\"\n", "    report_type = \"research_report\"\n", "\n", "    report, context, costs, images, sources = asyncio.run(get_report(query, report_type))\n", "    \n", "    print(\"Report:\")\n", "    print(report)\n", "    print(\"\\nResearch Costs:\")\n", "    print(costs)\n", "    print(\"\\nResearch Images:\")\n", "    print(images)\n", "    print(\"\\nResearch Sources:\")\n", "    print(sources)"], "metadata": {"id": "KWZe2InrL0ji"}, "execution_count": null, "outputs": []}]}