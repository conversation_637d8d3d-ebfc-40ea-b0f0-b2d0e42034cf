package com.ms.bp.shared.common.db;

import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicReference;

/**
 * SSH隧道管理クラス
 * AWS dev データベースへのSSH隧道接続を管理
 */
public class SSHTunnelManager {
    private static final Logger logger = LoggerFactory.getLogger(SSHTunnelManager.class);
    
    private static final AtomicReference<Session> sessionRef = new AtomicReference<>();
    private static volatile boolean tunnelEstablished = false;
    
    // SSH隧道設定
    private static String SSH_HOST;
    private static int SSH_PORT;
    private static String SSH_USERNAME;
    private static String SSH_PRIVATE_KEY_PATH;
    private static int LOCAL_PORT;
    private static String REMOTE_HOST;
    private static int REMOTE_PORT;
    private static int CONNECTION_TIMEOUT;
    private static int KEEP_ALIVE_INTERVAL;
    
    /**
     * SSH隧道設定を初期化
     */
    public static void initializeConfig(Properties config) {
        SSH_HOST = config.getProperty("ssh.tunnel.ssh.host");
        SSH_PORT = Integer.parseInt(config.getProperty("ssh.tunnel.ssh.port", "22"));
        SSH_USERNAME = config.getProperty("ssh.tunnel.ssh.username");
        SSH_PRIVATE_KEY_PATH = expandPath(config.getProperty("ssh.tunnel.ssh.private.key.path"));
        LOCAL_PORT = Integer.parseInt(config.getProperty("ssh.tunnel.local.port", "15432"));
        REMOTE_HOST = config.getProperty("ssh.tunnel.remote.host");
        REMOTE_PORT = Integer.parseInt(config.getProperty("ssh.tunnel.remote.port", "5432"));
        CONNECTION_TIMEOUT = Integer.parseInt(config.getProperty("ssh.tunnel.connection.timeout", "30000"));
        KEEP_ALIVE_INTERVAL = Integer.parseInt(config.getProperty("ssh.tunnel.keep.alive.interval", "60000"));
        
        logger.debug("SSH隧道設定が初期化されました: {}:{} -> {}:{}",
                   SSH_HOST, SSH_PORT, REMOTE_HOST, REMOTE_PORT);
    }
    
    /**
     * SSH隧道を確立
     * @return 隧道が正常に確立された場合true
     */
    public static synchronized boolean establishTunnel() {
        if (tunnelEstablished && isSessionActive()) {
            logger.debug("SSH隧道は既に確立されています");
            return true;
        }
        
        try {
            // 既存のセッションをクリーンアップ
            closeTunnel();
            
            // SSH設定の検証
            if (!validateConfig()) {
                logger.error("SSH隧道設定が無効です");
                return false;
            }
            
            // JSch セッションを作成
            JSch jsch = new JSch();
            
            // 秘密鍵を追加
            File keyFile = new File(SSH_PRIVATE_KEY_PATH);
            if (!keyFile.exists()) {
                logger.error("SSH秘密鍵ファイルが見つかりません: {}", SSH_PRIVATE_KEY_PATH);
                return false;
            }
            jsch.addIdentity(SSH_PRIVATE_KEY_PATH);
            
            // セッションを作成
            Session session = jsch.getSession(SSH_USERNAME, SSH_HOST, SSH_PORT);
            
            // SSH設定
            Properties sshConfig = new Properties();
            sshConfig.put("StrictHostKeyChecking", "no");  // 本番環境では "yes" に変更することを推奨
            sshConfig.put("ServerAliveInterval", String.valueOf(KEEP_ALIVE_INTERVAL / 1000));
            sshConfig.put("ServerAliveCountMax", "3");
            session.setConfig(sshConfig);
            session.setTimeout(CONNECTION_TIMEOUT);
            
            // 接続を確立
            logger.debug("SSH隧道を確立中... {}@{}:{}", SSH_USERNAME, SSH_HOST, SSH_PORT);
            session.connect();
            
            // ポートフォワーディングを設定
            int assignedPort = session.setPortForwardingL(LOCAL_PORT, REMOTE_HOST, REMOTE_PORT);
            logger.debug("ポートフォワーディングが設定されました: localhost:{} -> {}:{}",
                       assignedPort, REMOTE_HOST, REMOTE_PORT);
            
            // セッションを保存
            sessionRef.set(session);
            tunnelEstablished = true;
            
            logger.debug("SSH隧道が正常に確立されました");
            return true;
            
        } catch (Exception e) {
            logger.error("SSH隧道の確立に失敗しました", e);
            tunnelEstablished = false;
            return false;
        }
    }
    
    /**
     * SSH隧道を閉じる
     */
    public static synchronized void closeTunnel() {
        Session session = sessionRef.get();
        if (session != null) {
            try {
                if (session.isConnected()) {
                    session.disconnect();
                    logger.debug("SSH隧道が閉じられました");
                }
            } catch (Exception e) {
                logger.warn("SSH隧道のクローズ中にエラーが発生しました", e);
            } finally {
                sessionRef.set(null);
                tunnelEstablished = false;
            }
        }
    }
    
    /**
     * SSH隧道の状態を確認
     * @return 隧道が有効な場合true
     */
    public static boolean isTunnelActive() {
        return tunnelEstablished && isSessionActive();
    }
    
    /**
     * ローカルポート番号を取得
     * @return ローカルポート番号
     */
    public static int getLocalPort() {
        return LOCAL_PORT;
    }
    
    /**
     * セッションがアクティブかどうかを確認
     */
    private static boolean isSessionActive() {
        Session session = sessionRef.get();
        return session != null && session.isConnected();
    }
    
    /**
     * SSH隧道設定を検証
     */
    private static boolean validateConfig() {
        if (SSH_HOST == null || SSH_HOST.trim().isEmpty()) {
            logger.error("SSH_HOSTが設定されていません");
            return false;
        }
        if (SSH_USERNAME == null || SSH_USERNAME.trim().isEmpty()) {
            logger.error("SSH_USERNAMEが設定されていません");
            return false;
        }
        if (SSH_PRIVATE_KEY_PATH == null || SSH_PRIVATE_KEY_PATH.trim().isEmpty()) {
            logger.error("SSH_PRIVATE_KEY_PATHが設定されていません");
            return false;
        }
        if (REMOTE_HOST == null || REMOTE_HOST.trim().isEmpty()) {
            logger.error("REMOTE_HOSTが設定されていません");
            return false;
        }
        return true;
    }
    
    /**
     * パスを展開（~をホームディレクトリに変換、相対パスを絶対パスに変換）
     */
    private static String expandPath(String path) {
        if (path == null || path.trim().isEmpty()) {
            return path;
        }

        // ~をホームディレクトリに変換
        if (path.startsWith("~")) {
            return path.replace("~", System.getProperty("user.home"));
        }

        // 相対パスの場合、プロジェクトルートからの絶対パスに変換
        File file = new File(path);
        if (!file.isAbsolute()) {
            // プロジェクトルートディレクトリを取得
            String projectRoot = System.getProperty("user.dir");
            file = new File(projectRoot, path);
            path = file.getAbsolutePath();
            logger.debug("相対パスを絶対パスに変換しました: {} -> {}", path, file.getAbsolutePath());
        }

        return path;
    }
    
    /**
     * JVMシャットダウン時にSSH隧道を自動的に閉じる
     */
    static {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            logger.info("アプリケーション終了時にSSH隧道をクリーンアップしています...");
            closeTunnel();
        }));
    }
}
