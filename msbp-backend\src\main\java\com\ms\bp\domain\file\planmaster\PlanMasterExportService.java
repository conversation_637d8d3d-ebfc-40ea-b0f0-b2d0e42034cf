package com.ms.bp.domain.file.planmaster;

import com.ms.bp.application.data.strategy.FileSplitStrategy;
import com.ms.bp.domain.file.planmaster.strategy.PlanMasterDataStrategy;
import com.ms.bp.domain.file.planmaster.strategy.PlanMasterFileSplitStrategy;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.domain.file.base.AbstractExportService;
import com.ms.bp.domain.file.base.DataExpander;
import com.ms.bp.shared.common.io.model.FileFormat;
import com.ms.bp.shared.common.io.options.ExportOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 次年度計画マスタエクスポートサービスの実装クラス
 * 次年度計画マスタデータのCSV形式でのエクスポート機能を提供
 * SQL戦略パターンを使用してデータ取得とエクスポート処理を実現
 */
public class PlanMasterExportService extends AbstractExportService<Map<String, Object>> {
    private static final Logger logger = LoggerFactory.getLogger(PlanMasterExportService.class);

    // ==================== 定数定義 ====================

    /** CSV出力列名リスト（23列） */
    private static final List<String> CSV_COLUMNS = Arrays.asList(
        "area_cd",           // エリアCD
        "area_mei",          // エリア名
        "cate_cd",           // カテCD
        "category",          // カテゴリ
        "group_cd",          // グループ
        "unit_cd",           // ユニット
        "tantousha",         // 担当者
        "kigyo_cd",          // 企業CD
        "kigyo_mei",         // 企業名
        "gyoutai_shukei",    // 業態集計
        "gyoutai_mei",       // 業態名
        "sub_category_cd",   // サブカテゴリ
        "sub_category_mei",  // サブカテゴリ名
        "saisan_kanri_tani_cd",    // 採算管理単位CD
        "saisan_kanri_tani_mei",   // 採算管理単位名
        "henkou_mae_torikumi_kubun",  // 変更前取組区分
        "henkou_go_torikumi_kubun",   // 変更後取組区分
        "ikan_saki_area_cd",          // 移管先エリアCD
        "ikan_saki_area_mei",         // 移管先エリア名
        "ikan_saki_group_cd",         // 移管先グループCD
        "ikan_saki_unit_cd",          // 移管先ユニットCD
        "tounen_jisseki_ruikei",     // 当年度実績累計(参考)
        "tounen_keikaku_ruikei"      // 当年度計画累計(参考)

    );

    @Override
    protected void registerSqlStrategies() {
        logger.info("次年度計画マスタエクスポート用SQL戦略を登録中...");

        // 次年度計画マスタ専用のSQL戦略を登録
        sqlStrategyManager.registerStrategy(new PlanMasterDataStrategy());

        logger.info("次年度計画マスタエクスポート用SQL戦略登録完了");
        sqlStrategyManager.logStatistics();
    }

    @Override
    protected DataExpander getDataExpander() {
        // 次年度計画マスタデータは展開処理が不要
        // 複雑なJOINクエリで必要なデータを直接取得するため
        return null;
    }

    @Override
    public FileSplitStrategy getSplitStrategy() {
        // 次年度計画マスタ専用のファイル分割戦略を使用
        return new PlanMasterFileSplitStrategy();
    }

    @Override
    protected ExportOptions buildExportOptionsWithStrategy(String sqlStrategyKey) {
        // 次年度計画マスタは単一の戦略のみ使用
        return ExportOptions.builder()
                .format(FileFormat.CSV)
                .includeHeader(true)
                .columns(CSV_COLUMNS)
                .batchSize(BusinessConstants.CSV_DOWNLOAD_BATCH_SIZE)
                .countTotal(true)
                .enableFieldMapping(true)
                .fieldHeaderMapping(createPlanMasterFieldHeaderMapping())
                .build();
    }

    /**
     * 次年度計画マスタ用フィールドヘッダーマッピングを作成
     * データベース列名から日本語ヘッダー名への変換マップ
     */
    private Map<String, String> createPlanMasterFieldHeaderMapping() {
        Map<String, String> mapping = new HashMap<>();

        // 23個の列ヘッダーを順序通りに設定
        mapping.put("area_cd", "エリアCD");
        mapping.put("area_mei", "エリア名");
        mapping.put("cate_cd", "カテCD");
        mapping.put("category", "カテゴリ");
        mapping.put("group_cd", "グループ");
        mapping.put("unit_cd", "ユニット");
        mapping.put("tantousha", "担当者");
        mapping.put("kigyo_cd", "企業CD");
        mapping.put("kigyo_mei", "企業名");
        mapping.put("gyoutai_shukei", "業態集計");
        mapping.put("gyoutai_mei", "業態名");
        mapping.put("sub_category_cd", "サブカテゴリ");
        mapping.put("sub_category_mei", "サブカテゴリ名");
        mapping.put("saisan_kanri_tani_cd", "採算管理単位CD");
        mapping.put("saisan_kanri_tani_mei", "採算管理単位名");
        mapping.put("henkou_mae_torikumi_kubun", "変更前取組区分");
        mapping.put("henkou_go_torikumi_kubun", "変更後取組区分");
        mapping.put("ikan_saki_area_cd", "移管先エリアCD");
        mapping.put("ikan_saki_area_mei", "移管先エリア名");
        mapping.put("ikan_saki_group_cd", "移管先グループCD");
        mapping.put("ikan_saki_unit_cd", "移管先ユニットCD");
        mapping.put("tounen_jisseki_ruikei", "当年度実績累計(参考)");
        mapping.put("tounen_keikaku_ruikei", "当年度計画累計(参考)");

        return mapping;
    }
}
