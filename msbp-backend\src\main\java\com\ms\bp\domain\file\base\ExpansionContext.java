package com.ms.bp.domain.file.base;

import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import lombok.Data;
import lombok.Builder;

import java.util.List;
import java.util.Map;

/**
 * データ展開処理のコンテキスト情報
 * 展開処理に必要な核心情報のみを保持
 */
@Data
@Builder
public class ExpansionContext {

    /**
     * 認証されたユーザー情報
     * 権限ベースの展開処理で使用
     */
    private UserInfo userInfo;

    /**
     * エクスポートリクエストのパラメータ
     * 展開条件の制御に使用
     */
    private Map<String, Object> requestParams;

    /**
     * 出力対象の列リスト
     * 展開後のデータ構造の決定に使用
     */
    private List<String> targetColumns;

    /**
     * エクスポートリクエスト
     * 展開データのフィルタリングに使用
     */
    private ExportRequest exportRequest;

    /**
     * 特定のリクエストパラメータを取得
     *
     * @param key パラメータキー
     * @param defaultValue デフォルト値
     * @param <T> 戻り値の型
     * @return パラメータ値またはデフォルト値
     */
    @SuppressWarnings("unchecked")
    public <T> T getRequestParam(String key, T defaultValue) {
        if (requestParams == null || !requestParams.containsKey(key)) {
            return defaultValue;
        }
        try {
            return (T) requestParams.get(key);
        } catch (ClassCastException e) {
            return defaultValue;
        }
    }

    /**
     * エクスポートリクエストからエリア情報を取得
     *
     * @return エリア情報リスト
     */
    public List<String> getAreaList() {
        return exportRequest != null ? exportRequest.getArea() : null;
    }



    /**
     * エクスポートリクエストから本社場所区分を取得
     *
     * @return 本社場所区分
     */
    public String getHnshBashoKubun() {
        return exportRequest != null ? exportRequest.getHnshBashoKubun() : null;
    }

    /**
     * エクスポートリクエストからデータ区分を取得
     *
     * @return データ区分
     */
    public String getDataKubun() {
        return exportRequest != null ? exportRequest.getDataKubunString() : null;
    }
}
