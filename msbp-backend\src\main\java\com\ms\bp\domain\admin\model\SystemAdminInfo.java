package com.ms.bp.domain.admin.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * システム管理者情報ドメインモデル
 * システム管理者の権限情報を表現するドメインモデル
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SystemAdminInfo {
    
    /**
     * システム運用企業コード
     */
    private String systemOperationCompanyCode;
    
    /**
     * 社員コード
     */
    private String shainCode;
    
    /**
     * 使用禁止区分
     * "0": 使用許可, "1": 使用禁止
     */
    private String prohibitionFlag;
    
    /**
     * 管理者が有効かどうかを判定
     * 
     * @return 有効な管理者の場合true
     */
    public boolean isValidAdmin() {
        return "0".equals(prohibitionFlag);
    }
    
    /**
     * 管理者の権限レベルを判定
     * システム管理者は本社権限（H）のみアクセス可能
     * 
     * @return 常にtrue（システム管理者は本社権限を持つ）
     */
    public boolean hasHeadOfficeAuthority() {
        return isValidAdmin();
    }
}