# 開発ガイド - インポート・エクスポート機能

## 目次
1. [概要](#概要)
2. [アーキテクチャ原理](#アーキテクチャ原理)
3. [実装パターン](#実装パターン)
4. [拡張ポイント](#拡張ポイント)
5. [新規業務ドメイン追加手順](#新規業務ドメイン追加手順)
6. [実装チェックリスト](#実装チェックリスト)

## アーキテクチャ原理

### 1. レイヤー構成

```
┌─────────────────────────────────────────┐
│ Interfaces Layer (Controller)           │ ← HTTP リクエスト処理
├─────────────────────────────────────────┤
│ Application Layer (Orchestrator)        │ ← ビジネス協調・タスク管理
├─────────────────────────────────────────┤
│ Domain Layer (Service/Strategy)          │ ← ビジネスロジック・戦略実装
├─────────────────────────────────────────┤
│ Infrastructure Layer (Repository)       │ ← データアクセス・外部連携
└─────────────────────────────────────────┘
```

### 2. 処理フロー

#### エクスポート処理フロー
```
DataController → FileExportOrchestrator → AbstractExportService
                                        ↓
FileSplitStrategy → ExportTask生成 → ExportTemplate実行
                                        ↓
DataAccessStrategy → SQL構築 → データ取得 → CSV生成
```

#### インポート処理フロー
```
DataController → FileImportOrchestrator → AbstractImportService
                                        ↓
ImportTemplate → ファイル解析 → DTO変換 → バリデーション → DB保存
```

### 3. 核心コンポーネント

#### 3.1 テンプレートクラス
- **ImportTemplate**: インポート処理の共通フレームワーク
- **ExportTemplate**: エクスポート処理の共通フレームワーク

#### 3.2 戦略インターフェース
- **FileSplitStrategy**: ファイル分割戦略
- **DataAccessStrategy**: SQL構築戦略

#### 3.3 オーケストレーター
- **FileExportOrchestrator**: エクスポート処理の協調
- **FileImportOrchestrator**: インポート処理の協調

## 実装パターン

### 1. 戦略パターンの実装

#### FileSplitStrategy（ファイル分割戦略）
```java
public interface FileSplitStrategy {
    /**
     * エクスポートタスクを作成
     * @param exportRequest エクスポートリクエスト
     * @param userInfo ユーザー情報
     * @return エクスポートタスクリスト
     */
    List<ExportTask> createExportTasks(ExportRequest exportRequest, UserInfo userInfo);

    /**
     * この戦略がリクエストをサポートするかチェック
     * @param exportRequest エクスポートリクエスト
     * @return サポート可否
     */
    boolean supports(ExportRequest exportRequest);
}
```

#### DataAccessStrategy（SQL戦略）
```java
public interface DataAccessStrategy {
    /**
     * SQLクエリとパラメータを構築
     * @param exportRequest エクスポートリクエスト
     * @param userInfo ユーザー情報
     * @return SQL文とパラメータ
     */
    SqlWithParams buildQuery(ExportRequest exportRequest, UserInfo userInfo);

    /**
     * 戦略名を取得
     * @return 戦略名
     */
    String getStrategyName();

    /**
     * この戦略が条件をサポートするかチェック
     * @param exportRequest エクスポートリクエスト
     * @param userInfo ユーザー情報
     * @return サポート可否
     */
    boolean supports(ExportRequest exportRequest, UserInfo userInfo);
}
```


### 2. DTOとデータベースマッピング

#### DatabaseMappable インターフェース（最新版）
```java
public interface DatabaseMappable {
    /**
     * DTOをデータベースフィールドのMapに変換します（テンプレートメソッド）
     * 固定値フィールドの注入を統一処理
     *
     * @param isInsert 挿入操作であるかどうか
     * @param options インポートオプション（固定値フィールドを含む）
     * @return データベースフィールドのMap
     */
    default Map<String, Object> toDatabaseFields(boolean isInsert, ImportOptions options) {
        // 1. 各DTOの核心フィールドマッピングを実行
        Map<String, Object> fields = toDatabaseFieldsCore(isInsert);

        // 2. 固定値フィールドを注入（統一処理）
        if (options != null && options.getAdditionalFields() != null) {
            fields.putAll(options.getAdditionalFields());
        }

        return fields;
    }

    /**
     * DTOの核心フィールドをデータベースフィールドのMapに変換します（サブクラスで実装）
     * 各DTOは固定値フィールド以外の通常フィールドのみを処理
     *
     * @param isInsert 挿入操作であるかどうか
     * @return データベースフィールドのMap（固定値フィールドは含まない）
     */
    Map<String, Object> toDatabaseFieldsCore(boolean isInsert);
}
```

#### DTO実装例（最新版）
```java
@Data
public class UserImportData implements DatabaseMappable {
    @NotNull(message = "ユーザーIDは必須です")
    private String id;

    @NotBlank(message = "ユーザー名は必須です")
    @Size(max = 50, message = "ユーザー名は50文字以内で入力してください")
    private String username;

    @Email(message = "有効なメールアドレスを入力してください")
    private String email;

    @NotBlank(message = "氏名は必須です")
    private String fullName;

    // DatabaseMappableの実装（核心フィールドのみ）
    @Override
    public Map<String, Object> toDatabaseFieldsCore(boolean isInsert) {
        Map<String, Object> fields = new HashMap<>();

        // 基本フィールド
        fields.put("username", username);
        fields.put("email", email);
        fields.put("full_name", fullName);

        // 自動生成フィールド
        Date currentTime = new Date();
        if (isInsert) {
            fields.put("created_at", currentTime);
        }
        fields.put("updated_at", currentTime);

        return fields;
        // 注意：固定値フィールドは親インターフェースのテンプレートメソッドで自動注入される
    }
}
```

### 5. 固定値フィールド

#### 概要
CSVファイルに存在しないフィールドを固定値として追加する機能です。追加されたフィールドは複合主キーの一部として使用することも可能です。

#### 使用例：年度フィールドの追加
```java
@Override
protected ImportOptions buildImportOptions() {
    return ImportOptions.builder()
            .format(FileFormat.CSV)
            .hasHeader(true)
            .targetTable("users")
            // 複合主キー：id + fiscal_year（固定値フィールドも含む）
            .keyColumns("id", "fiscal_year")
            .upsertMode(true)
            .headerFieldMapping(Map.of(
                    "ユーザーID", "id",
                    "ユーザー名", "username",
                    "メールアドレス", "email"
            ))
            // 固定値フィールドの設定
            .additionalFields(Map.of(
                    "fiscal_year", "2024",  // 年度フィールド（固定値）
                    "import_batch_id", UUID.randomUUID().toString(),  // インポートバッチID
                    "data_source", "CSV_IMPORT",  // データソース識別子
                    "created_by", "SYSTEM"  // 作成者（システム）
            ))
            .build();
}
```

#### データフロー
```
CSVファイル → Map<String,Object> → DTO → toDatabaseFieldsCore() → 固定値注入 → Database
                                              ↓
                                    核心フィールドのみ処理
                                              ↓
                                    テンプレートメソッドで固定値自動追加
```

#### 実装のポイント

**1. DTOでは核心フィールドのみ処理**
```java
@Override
public Map<String, Object> toDatabaseFieldsCore(boolean isInsert) {
    Map<String, Object> fields = new HashMap<>();

    // 通常のフィールドマッピングのみ
    fields.put("username", username);
    fields.put("email", email);
    // ...

    return fields;
    // 固定値フィールドは自動で注入される
}
```

## 拡張ポイント

### 1. 新規ファイル分割戦略の追加

新しい分割ロジックが必要な場合：

```java
public class CustomFileSplitStrategy implements FileSplitStrategy {
    @Override
    public List<ExportTask> createExportTasks(ExportRequest exportRequest, UserInfo userInfo) {
        // カスタム分割ロジックを実装
        List<ExportTask> tasks = new ArrayList<>();

        // 例：部門別分割
        if (isDepartmentBasedSplit(exportRequest)) {
            List<String> departments = getDepartmentsFromRequest(exportRequest);
            for (String dept : departments) {
                // 部門別のExportRequestを作成
                ExportRequest deptExportRequest = new ExportRequest();
                deptExportRequest.setDataType(exportRequest.getDataType());
                deptExportRequest.setArea(exportRequest.getArea());
                deptExportRequest.setHnshBashoKubun(dept); // 部門を本社場所区分として設定
                deptExportRequest.setDataKubun(exportRequest.getDataKubun());

                ExportTask task = new ExportTask(
                    generateTaskId(),
                    deptExportRequest,
                    determineSqlStrategy(userInfo, dept)
                );
                task.setFileName(String.format("data_%s_%s.csv", dept, getTimestamp()));
                tasks.add(task);
            }
        }

        return tasks;
    }

    @Override
    public boolean supports(ExportRequest exportRequest) {
        return "CUSTOM_DATA".equalsIgnoreCase(exportRequest.getDataType());
    }
}
```

### 2. 新規SQL戦略の追加

新しいデータアクセスパターンが必要な場合：

```java
public class CustomDataStrategy implements DataAccessStrategy {
    @Override
    public SqlWithParams buildQuery(ExportRequest exportRequest, UserInfo userInfo) {
        StringBuilder sql = new StringBuilder();
        Map<String, Object> params = new HashMap<>();

        // カスタムSQL構築ロジック
        sql.append("SELECT c.*, d.name as department_name ");
        sql.append("FROM custom_table c ");
        sql.append("LEFT JOIN departments d ON c.dept_id = d.id ");

        // 条件構築
        List<String> conditions = new ArrayList<>();
        if (exportRequest != null) {
            // エリア条件（複数値対応）
            addListCondition(conditions, params, "c.area", exportRequest.getArea(), "=");

            // 本社場所区分条件
            if (exportRequest.getHnshBashoKubun() != null) {
                conditions.add("c.hnsh_basho_kubun = :hnshBashoKubun");
                params.put("hnshBashoKubun", exportRequest.getHnshBashoKubun());
            }

            // データ区分条件（複数値対応）
            addListCondition(conditions, params, "c.data_kubun", exportRequest.getDataKubun(), "=");
        }
    }

    /**
     * リスト値用のWHERE句構築ヘルパー
     * 単一値の場合は等値比較、複数値の場合はIN句を使用
     */
    private void addListCondition(List<String> conditions, Map<String, Object> params,
                                  String column, List<String> values, String operator) {
        if (values == null || values.isEmpty()) {
            return;
        }

        // 空文字列を除外
        List<String> filteredValues = values.stream()
                .filter(v -> v != null && !v.trim().isEmpty())
                .collect(Collectors.toList());

        if (filteredValues.isEmpty()) {
            return;
        }

        if (filteredValues.size() == 1) {
            // 単一値の場合は等値比較
            conditions.add(column + " " + operator + " :" + column.replace(".", "_"));
            params.put(column.replace(".", "_"), filteredValues.get(0));
        } else {
            // 複数値の場合はIN句を使用
            conditions.add(column + " IN (:" + column.replace(".", "_") + ")");
            params.put(column.replace(".", "_"), filteredValues);
        }

        if (!conditions.isEmpty()) {
            sql.append("WHERE ").append(String.join(" AND ", conditions));
        }

        return new SqlWithParams(sql.toString(), params);
    }

    @Override
    public String getStrategyName() {
        return "CUSTOM_STRATEGY";
    }

    @Override
    public boolean supports(ExportRequest exportRequest, UserInfo userInfo) {
        // サポート条件を定義
        return exportRequest != null && exportRequest.getDataType() != null;
    }

}
```

### 3. データ展開器の追加

1対多のデータ展開が必要な場合：

```java
public class CustomDataExpander implements DataExpander {
    @Override
    public List<Map<String, Object>> expandData(Map<String, Object> rawData, ExpansionContext context) {
        List<Map<String, Object>> expandedRows = new ArrayList<>();

        // 例：注文データから注文明細への展開
        String orderId = (String) rawData.get("order_id");
        List<Map<String, Object>> orderItems = getOrderItems(orderId);

        for (Map<String, Object> item : orderItems) {
            Map<String, Object> expandedRow = new HashMap<>(rawData);
            expandedRow.putAll(item);
            expandedRows.add(expandedRow);
        }

        return expandedRows;
    }

    @Override
    public boolean needsExpansion(Map<String, Object> rawData) {
        // 展開が必要かどうかの判定ロジック
        return rawData.containsKey("order_id") && rawData.get("order_id") != null;
    }

    @Override
    public String getDescription() {
        return "注文明細展開器";
    }
}
```

## 新規業務ドメイン追加手順

新しい業務ドメイン（例：商品管理）をインポート・エクスポート機能に追加する場合の詳細手順を説明します。

### ステップ1: ドメインモデルの作成

#### 1.1 DTOクラスの作成
**ファイル**: `src/main/java/com/ms/bp/domain/file/model/ProductImportData.java`

```java
package com.ms.bp.domain.file.model;

import com.ms.bp.shared.common.io.converter.DatabaseMappable;
import lombok.Data;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 商品インポートデータDTO
 * 商品データのインポート処理で使用
 */
@Data
public class ProductImportData implements DatabaseMappable {

    @NotNull(message = "商品IDは必須です")
    private String productId;

    @NotBlank(message = "商品名は必須です")
    @Size(max = 100, message = "商品名は100文字以内で入力してください")
    private String productName;

    @NotNull(message = "価格は必須です")
    @DecimalMin(value = "0.0", message = "価格は0以上で入力してください")
    private BigDecimal price;

    @NotNull(message = "カテゴリIDは必須です")
    private String categoryId;

    private String description;

    @NotNull(message = "ステータスは必須です")
    private String status;

    // DatabaseMappableの実装（最新版）
    @Override
    public Map<String, Object> toDatabaseFieldsCore(boolean isInsert) {
        Map<String, Object> fields = new HashMap<>();

        // 基本フィールド
        fields.put("product_id", this.productId);
        fields.put("product_name", this.productName);
        fields.put("price", this.price);
        fields.put("category_id", this.categoryId);
        fields.put("description", this.description);
        fields.put("status", this.status);

        // 自動生成フィールド
        Date currentTime = new Date();
        if (isInsert) {
            fields.put("created_at", currentTime);
        }
        fields.put("updated_at", currentTime);

        return fields;
        // 固定値フィールドは親インターフェースで自動注入される
    }
}
```

### ステップ2: インポートサービスの作成

#### 2.1 ProductImportService の実装
**ファイル**: `src/main/java/com/ms/bp/domain/file/product/ProductImportService.java`

```java
package com.ms.bp.domain.file.product;

import com.ms.bp.domain.file.model.ProductImportData;
import com.ms.bp.domain.file.base.AbstractImportService;
import com.ms.bp.shared.common.exception.ValidationError;
import com.ms.bp.shared.common.io.model.FileFormat;
import com.ms.bp.shared.common.io.options.ImportOptions;
import com.ms.bp.shared.common.io.validator.DTODataValidator;
import com.ms.bp.shared.common.io.validator.DataValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 商品インポートサービス
 * 商品データのインポート処理を担当
 */
public class ProductImportService extends AbstractImportService<ProductImportData> {
    private static final Logger logger = LoggerFactory.getLogger(ProductImportService.class);

    @Override
    protected String getDataType() {
        return "PRODUCTS";
    }

    @Override
    protected DataValidator getDataValidator() {
        return new DTODataValidator<>(ProductImportData.class, this::validateCustomLogic);
    }

    @Override
    protected Class<ProductImportData> getDTOClass() {
        return ProductImportData.class;
    }

    @Override
    protected ImportOptions buildImportOptions() {
        return ImportOptions.builder()
                .format(FileFormat.CSV)
                .hasHeader(true)
                .batchSize(100)
                .targetTable("products")
                .keyColumns(Arrays.asList("product_id"))
                .upsertMode(true)
                .skipValidation(false)
                .continueOnError(true)
                .enableFieldMapping(true)
                .headerFieldMapping(Map.of(
                        "商品ID", "productId",
                        "商品名", "productName",
                        "価格", "price",
                        "カテゴリID", "categoryId",
                        "説明", "description",
                        "ステータス", "status"
                )).build();
    }

    /**
     * カスタムビジネスロジック検証
     */
    protected List<ValidationError> validateCustomLogic(Map<String, Object> data, ImportOptions options) {
        List<ValidationError> errors = new ArrayList<>();

        // 商品ID重複チェック（将来実装）
        // カテゴリID存在チェック（将来実装）
        // 価格範囲チェック
        Object priceObj = data.get("price");
        if (priceObj != null) {
            try {
                double price = Double.parseDouble(priceObj.toString());
                if (price < 0) {
                    errors.add(new ValidationError("price", priceObj, "価格は0以上で入力してください"));
                }
                if (price > 1000000) {
                    errors.add(new ValidationError("price", priceObj, "価格は1,000,000以下で入力してください"));
                }
            } catch (NumberFormatException e) {
                errors.add(new ValidationError("price", priceObj, "価格は数値で入力してください"));
            }
        }

        return errors;
    }
}
```

### ステップ3: エクスポートサービスの作成

#### 3.1 SQL戦略の実装

**ファイル**: `src/main/java/com/ms/bp/domain/file/product/strategy/DefaultProductStrategy.java`

```java
package com.ms.bp.domain.file.product.strategy;

import com.ms.bp.application.data.strategy.DataAccessStrategy;
import com.ms.bp.domain.user.model.UserInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * デフォルト商品SQL戦略
 * 基本的な商品データエクスポート用のSQL構築
 */
public class DefaultProductStrategy implements DataAccessStrategy {
    private static final Logger logger = LoggerFactory.getLogger(DefaultProductStrategy.class);

    @Override
    public SqlWithParams buildQuery(ExportRequest exportRequest, UserInfo userInfo) {
        StringBuilder sql = new StringBuilder();
        List<Object> params = new ArrayList<>();
        List<String> conditions = new ArrayList<>();

        // 基本SQL構築
        sql.append("SELECT ");
        sql.append("p.product_id, ");
        sql.append("p.product_name, ");
        sql.append("p.price, ");
        sql.append("c.category_name, ");
        sql.append("p.description, ");
        sql.append("p.status, ");
        sql.append("p.created_at, ");
        sql.append("p.updated_at ");
        sql.append("FROM products p ");
        sql.append("LEFT JOIN categories c ON p.category_id = c.category_id ");

        // 検索条件の構築
        if (exportRequest != null) {
            // エリア検索
            if (exportRequest.getArea() != null) {
                conditions.add("p.area = ?");
                params.add(exportRequest.getArea());
            }

            // 本社場所区分検索
            if (exportRequest.getHnshBashoKubun() != null) {
                conditions.add("p.hnsh_basho_kubun = ?");
                params.add(exportRequest.getHnshBashoKubun());
            }

            // データ区分検索
            if (exportRequest.getDataKubun() != null) {
                conditions.add("p.data_kubun = ?");
                params.add(exportRequest.getDataKubun());
            }
        }

        // WHERE句の追加
        if (!conditions.isEmpty()) {
            sql.append("WHERE ").append(String.join(" AND ", conditions)).append(" ");
        }

        // ORDER BY句
        sql.append("ORDER BY p.product_id ");

        // パラメータマップ変換
        Map<String, Object> paramMap = new HashMap<>();
        for (int i = 0; i < params.size(); i++) {
            paramMap.put("param" + i, params.get(i));
        }

        logger.debug("デフォルト商品SQL構築完了: 条件数={}", conditions.size());
        return new SqlWithParams(sql.toString(), paramMap);
    }

    @Override
    public String getStrategyName() {
        return "DEFAULT_PRODUCT";
    }

    @Override
    public boolean supports(ExportRequest exportRequest, UserInfo userInfo) {
        return true; // デフォルト戦略として常にサポート
    }
}
```

#### 3.2 ファイル分割戦略の実装

**ファイル**: `src/main/java/com/ms/bp/domain/file/product/strategy/ProductFileSplitStrategy.java`

```java
package com.ms.bp.domain.file.product.strategy;

import com.ms.bp.application.data.strategy.FileSplitStrategy;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.shared.common.io.model.ExportRequest;
import com.ms.bp.shared.common.io.model.ExportTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 商品エクスポート専用ファイル分割戦略
 * 商品データのエクスポートに特化した分割ロジックを提供
 */
public class ProductFileSplitStrategy implements FileSplitStrategy {
    private static final Logger logger = LoggerFactory.getLogger(ProductFileSplitStrategy.class);

    @Override
    public List<ExportTask> createExportTasks(ExportRequest exportRequest, UserInfo userInfo) {
        logger.info("商品エクスポートタスク作成開始: エクスポートリクエスト={}", exportRequest);

        // カテゴリ別分割が指定されている場合
        if (isCategoryBasedSplit(exportRequest)) {
            return createCategoryBasedTasks(exportRequest, userInfo);
        }

        // デフォルト: 単一ファイル
        return createSingleFileTasks(exportRequest, userInfo);
    }

    @Override
    public boolean supports(ExportRequest exportRequest) {
        return "PRODUCTS".equalsIgnoreCase(exportRequest.getDataType());
    }

    /**
     * カテゴリ別分割タスクを作成
     */
    private List<ExportTask> createCategoryBasedTasks(ExportRequest exportRequest, UserInfo userInfo) {
        List<ExportTask> tasks = new ArrayList<>();
        List<String> categories = getCategoriesFromRequest(exportRequest);

        for (String category : categories) {
            // カテゴリ別のExportRequestを作成
            ExportRequest categoryExportRequest = new ExportRequest();
            categoryExportRequest.setDataType(exportRequest.getDataType());
            categoryExportRequest.setArea(exportRequest.getArea());
            categoryExportRequest.setHnshBashoKubun(exportRequest.getHnshBashoKubun());
            categoryExportRequest.setDataKubun(category); // カテゴリをデータ区分として設定

            String sqlStrategy = determineSqlStrategy(userInfo, categoryExportRequest);
            String fileName = String.format("products_%s_%s.csv", category, getTimestamp());

            ExportTask task = new ExportTask(generateTaskId(), categoryExportRequest, sqlStrategy);
            task.setFileName(fileName);
            tasks.add(task);

            logger.debug("カテゴリ別タスク作成: カテゴリ={}, ファイル名={}", category, fileName);
        }

        return tasks;
    }

    /**
     * 単一ファイルタスクを作成
     */
    private List<ExportTask> createSingleFileTasks(ExportRequest exportRequest, UserInfo userInfo) {
        String sqlStrategy = determineSqlStrategy(userInfo, exportRequest);
        String fileName = String.format("products_all_%s.csv", getTimestamp());

        ExportTask task = new ExportTask("default", exportRequest, sqlStrategy);
        task.setFileName(fileName);

        logger.debug("単一ファイルタスク作成: ファイル名={}", fileName);
        return List.of(task);
    }

    // ヘルパーメソッド
    private boolean isCategoryBasedSplit(ExportRequest exportRequest) {
        // データ区分が複数指定されている場合はカテゴリ別分割とみなす
        List<String> dataKubun = exportRequest.getDataKubun();
        return dataKubun != null && dataKubun.size() > 1;
    }

    private List<String> getCategoriesFromRequest(ExportRequest exportRequest) {
        List<String> dataKubun = exportRequest.getDataKubun();
        if (dataKubun != null && !dataKubun.isEmpty()) {
            return dataKubun;
        }
        return Arrays.asList("ELECTRONICS", "CLOTHING", "BOOKS"); // デフォルト
    }

    private String determineSqlStrategy(UserInfo userInfo, ExportRequest exportRequest) {
        // ユーザー権限や条件に基づいてSQL戦略を決定
        return "DEFAULT_PRODUCT";
    }

    private String generateTaskId() {
        return "TASK_" + System.currentTimeMillis();
    }

    private String getTimestamp() {
        return new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
    }
}
```

#### 3.3 ProductExportService の実装

**ファイル**: `src/main/java/com/ms/bp/domain/file/product/ProductExportService.java`

```java
package com.ms.bp.domain.file.product;

import com.ms.bp.application.data.strategy.FileSplitStrategy;
import com.ms.bp.domain.file.product.strategy.DefaultProductStrategy;
import com.ms.bp.domain.file.product.strategy.ProductFileSplitStrategy;
import com.ms.bp.domain.file.base.AbstractExportService;
import com.ms.bp.domain.file.base.DataExpander;
import com.ms.bp.shared.common.config.ConfigurationManager;
import com.ms.bp.shared.common.io.model.FileFormat;
import com.ms.bp.shared.common.io.options.ExportOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 商品エクスポートサービス
 * 商品データのエクスポート処理を担当
 */
public class ProductExportService extends AbstractExportService<Map<String, Object>> {
    private static final Logger logger = LoggerFactory.getLogger(ProductExportService.class);
    private final ConfigurationManager config = ConfigurationManager.getInstance();

    @Override
    protected void registerSqlStrategies() {
        logger.info("商品エクスポート用SQL戦略を登録中...");

        // SQL戦略を登録（優先度順）
        sqlStrategyManager.registerStrategy(new DefaultProductStrategy());
        // 将来的に他の戦略も追加可能
        // sqlStrategyManager.registerStrategy(new CategoryLimitedProductStrategy());
        // sqlStrategyManager.registerStrategy(new PremiumProductStrategy());

        logger.info("商品エクスポート用SQL戦略登録完了");
        sqlStrategyManager.logStatistics();
    }

    @Override
    protected DataExpander getDataExpander() {
        // 商品データは通常1対1なので展開不要
        // 将来的に商品バリエーション展開が必要な場合はここで実装
        return null;
    }

    @Override
    public FileSplitStrategy getSplitStrategy() {
        return new ProductFileSplitStrategy();
    }

    @Override
    protected Map<String, Object> formatData(Map<String, Object> rawData) {
        Map<String, Object> formattedData = new HashMap<>(rawData);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 日付フィールドの格式化
        formatDateField(formattedData, "created_at", dateFormat);
        formatDateField(formattedData, "updated_at", dateFormat);

        // 価格フィールドの格式化
        if (formattedData.containsKey("price")) {
            Object priceObj = formattedData.get("price");
            if (priceObj != null) {
                formattedData.put("price", String.format("%.2f", Double.parseDouble(priceObj.toString())));
            }
        }

        // ステータスの日本語変換
        if (formattedData.containsKey("status")) {
            String status = (String) formattedData.get("status");
            formattedData.put("status", translateStatus(status));
        }

        return formattedData;
    }

    @Override
    protected ExportOptions buildExportOptionsWithStrategy(String sqlStrategyKey, String customFileName) {
        return ExportOptions.builder()
                .format(FileFormat.CSV)
                .includeHeader(true)
                .columns(Arrays.asList(
                        "product_id", "product_name", "price", "category_name",
                        "description", "status", "created_at", "updated_at"
                ))
                .fieldHeaderMapping(Map.of(
                        "product_id", "商品ID",
                        "product_name", "商品名",
                        "price", "価格",
                        "category_name", "カテゴリ名",
                        "description", "説明",
                        "status", "ステータス",
                        "created_at", "作成日時",
                        "updated_at", "更新日時"
                ))
                .build();
    }

    /**
     * ステータスの日本語変換
     */
    private String translateStatus(String status) {
        if (status == null) return "";

        switch (status.toUpperCase()) {
            case "ACTIVE": return "有効";
            case "INACTIVE": return "無効";
            case "DISCONTINUED": return "販売終了";
            default: return status;
        }
    }

    /**
     * 日付フィールドの格式化ヘルパー
     */
    private void formatDateField(Map<String, Object> data, String fieldName, SimpleDateFormat formatter) {
        Object dateObj = data.get(fieldName);
        if (dateObj instanceof Date) {
            data.put(fieldName, formatter.format((Date) dateObj));
        } else if (dateObj != null) {
            // 文字列の場合はそのまま保持
            data.put(fieldName, dateObj.toString());
        }
    }
}
```

### ステップ4: ファクトリクラスの更新

#### 4.1 DomainServiceFactory の更新

**ファイル**: `src/main/java/com/ms/bp/application/factory/DomainServiceFactory.java`

既存のファクトリクラスに商品サービスの作成メソッドを追加：

```java
// ==================== ファイル処理領域サービス ====================

/**
 * 商品インポートサービスを作成
 * @return 商品インポートサービス
 */
public ProductImportService createProductImportService() {
    ProductImportService service = new ProductImportService();
    // 必要に応じて依存関係を注入
    return service;
}

/**
 * 商品エクスポートサービスを作成
 * @return 商品エクスポートサービス
 */
public ProductExportService createProductExportService() {
    ProductExportService service = new ProductExportService();
    // 必要に応じて依存関係を注入
    return service;
}
```

#### 4.2 FileImportOrchestrator の更新

**ファイル**: `src/main/java/com/ms/bp/application/data/FileImportOrchestrator.java`

`getImportService` メソッドに商品サービスのケースを追加：

```java
private AbstractImportService<?> getImportService(String dataType) {
    switch (dataType.toUpperCase()) {
        case "USERS":
            return domainServiceFactory.createUserImportService();
        case "PRODUCTS":  // 新規追加
            return domainServiceFactory.createProductImportService();
        default:
            throw new IllegalArgumentException("サポートされていないデータタイプ: " + dataType);
    }
}
```

#### 4.3 FileExportOrchestrator の更新

**ファイル**: `src/main/java/com/ms/bp/application/data/FileExportOrchestrator.java`

`getExportService` メソッドに商品サービスのケースを追加：

```java
private AbstractExportService<?> getExportService(String dataType) {
    switch (dataType.toUpperCase()) {
        case "USERS":
            return domainServiceFactory.createUserExportService();
        case "PRODUCTS":  // 新規追加
            return domainServiceFactory.createProductExportService();
        default:
            throw new IllegalArgumentException("サポートされていないデータタイプ: " + dataType);
    }
}
```

## 実装チェックリスト

新規業務ドメインを追加する際の実装チェックリストです：

### □ ドメインモデル
- [ ] DTOクラスの作成（DatabaseMappable実装）
- [ ] バリデーションアノテーションの設定
- [ ] toDatabaseFieldsCore(boolean isInsert)メソッドの実装
- [ ] カスタムバリデーションロジックの実装

### □ インポートサービス
- [ ] AbstractImportServiceを継承したサービスクラス作成
- [ ] getDataType()メソッドの実装
- [ ] getDataValidator()メソッドの実装
- [ ] getDTOClass()メソッドの実装
- [ ] buildImportOptions()メソッドの実装
- [ ] validateCustomLogic()メソッドの実装
- [ ] ヘッダーフィールドマッピングの設定

### □ エクスポートサービス
- [ ] AbstractExportServiceを継承したサービスクラス作成
- [ ] registerSqlStrategies()メソッドの実装
- [ ] getSplitStrategy()メソッドの実装
- [ ] getDataExpander()メソッドの実装（必要な場合）
- [ ] formatData()メソッドの実装
- [ ] buildExportOptions()メソッドの実装

### □ 戦略クラス
- [ ] DataAccessStrategy実装クラスの作成
- [ ] buildQuery()メソッドでのSQL構築ロジック
- [ ] supports()メソッドでのサポート条件定義
- [ ] FileSplitStrategy実装クラスの作成
- [ ] createExportTasks()メソッドでの分割ロジック
- [ ] supports()メソッドでのデータタイプ判定

### □ ファクトリ更新
- [ ] DomainServiceFactoryにサービス作成メソッド追加
- [ ] FileImportOrchestratorのgetImportService()更新
- [ ] FileExportOrchestratorのgetExportService()更新
