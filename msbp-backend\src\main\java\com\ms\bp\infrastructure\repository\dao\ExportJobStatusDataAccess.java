package com.ms.bp.infrastructure.repository.dao;


import com.ms.bp.shared.common.db.JdbcTemplate;
import com.ms.bp.domain.file.model.ExportJobStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * エクスポートジョブステータスデータアクセス
 * T_DWNLD_RRK（ダウンロード履歴テーブル）へのデータアクセスを管理
 */
public class ExportJobStatusDataAccess {
    private static final Logger logger = LoggerFactory.getLogger(ExportJobStatusDataAccess.class);

    private final JdbcTemplate jdbcTemplate;
    
    public ExportJobStatusDataAccess(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
    
    /**
     * エクスポートジョブステータスを挿入し、自動生成された履歴番号を返す
     * @param jobStatus エクスポートジョブステータス
     * @return 自動生成された履歴番号
     * @throws SQLException SQL実行エラー
     */
    public Long insert(ExportJobStatus jobStatus) throws SQLException {
        String sql = """
            INSERT INTO T_DWNLD_RRK (
                 SYSTM_UNYO_KIGYO_CODE, SHAIN_CODE, FILE_SHBTS, AREA,
                HNSH_BASHO_KUBUN, DATA_KUBUN, FILE_SKS_KSH_NCHJ, FILE_SKS_KNR_NCHJ,
                STTS, ZIP_FILE_SIZE, ZIP_FILE_MEI, TRK_PRGRM_ID, TRK_SYSTM_UNYO_KIGYO_CODE,
                TRK_SHAIN_CODE, KSHN_PRGRM_ID, KSHN_SYSTM_UNYO_KIGYO_CODE, KSHN_SHAIN_CODE,
                VRSN, RCRD_TRK_NCHJ, RCRD_KSHN_NCHJ,CTGRY_KUBUN
            ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;

        Object[] params = {
            jobStatus.getSystmUnyoKigyoCode(),
            jobStatus.getShainCode(),
            jobStatus.getFileShbts(),
            jobStatus.getArea(),
            jobStatus.getHnshBashoKubun(),
            jobStatus.getDataKubun(),
            jobStatus.getFileSksKshNchj(),
            jobStatus.getFileSksKnrNchj(),
            jobStatus.getStts(),
            jobStatus.getZipFileSize(),
            jobStatus.getZipFileMei(),
            jobStatus.getTrkPrgrmId(),
            jobStatus.getTrkSystmUnyoKigyoCode(),
            jobStatus.getTrkShainCode(),
            jobStatus.getKshnPrgrmId(),
            jobStatus.getKshnSystmUnyoKigyoCode(),
            jobStatus.getKshnShainCode(),
            jobStatus.getVrsn(),
            jobStatus.getRcrdTrkNchj(),
            jobStatus.getRcrdKshnNchj(),
            jobStatus.getCtgryKubun()
        };

        // 自動生成された履歴番号を取得
        Long generatedRrkBango = jdbcTemplate.insertWithGeneratedKey(sql, params, "rrk_bango");

        logger.debug("エクスポートジョブステータスを挿入しました: rrkBango={}", generatedRrkBango);
        return generatedRrkBango;
    }
    
    /**
     * エクスポートジョブステータスを更新
     */
    public void update(ExportJobStatus jobStatus) throws SQLException {
        String sql = """
            UPDATE T_DWNLD_RRK SET
                SYSTM_UNYO_KIGYO_CODE = ?, SHAIN_CODE = ?, FILE_SHBTS = ?, AREA = ?,
                HNSH_BASHO_KUBUN = ?, DATA_KUBUN = ?, FILE_SKS_KSH_NCHJ = ?, FILE_SKS_KNR_NCHJ = ?,
                STTS = ?, ZIP_FILE_SIZE = ?, ZIP_FILE_MEI = ?, KSHN_PRGRM_ID = ?,
                KSHN_SYSTM_UNYO_KIGYO_CODE = ?, KSHN_SHAIN_CODE = ?, VRSN = ?, RCRD_KSHN_NCHJ = ?
            WHERE RRK_BANGO = ?
            """;

        Object[] params = {
            jobStatus.getSystmUnyoKigyoCode(),
            jobStatus.getShainCode(),
            jobStatus.getFileShbts(),
            jobStatus.getArea(),
            jobStatus.getHnshBashoKubun(),
            jobStatus.getDataKubun(),
            jobStatus.getFileSksKshNchj(),
            jobStatus.getFileSksKnrNchj(),
            jobStatus.getStts(),
            jobStatus.getZipFileSize(),
            jobStatus.getZipFileMei(),
            jobStatus.getKshnPrgrmId(),
            jobStatus.getKshnSystmUnyoKigyoCode(),
            jobStatus.getKshnShainCode(),
            jobStatus.getVrsn(),
            jobStatus.getRcrdKshnNchj(),
            jobStatus.getRrkBango()
        };

        int result = jdbcTemplate.update(sql, params);
        if (result != 1) {
            throw new SQLException("エクスポートジョブステータスの更新に失敗しました: " + jobStatus.getRrkBango());
        }

        logger.debug("エクスポートジョブステータスを更新しました: rrkBango={}", jobStatus.getRrkBango());
    }
    
    /**
     * 履歴番号でエクスポートジョブステータスを取得
     */
    public ExportJobStatus findByRrkBango(Long rrkBango) throws SQLException {
        String sql = """
            SELECT RRK_BANGO, SYSTM_UNYO_KIGYO_CODE, SHAIN_CODE, FILE_SHBTS, AREA,
                   HNSH_BASHO_KUBUN, DATA_KUBUN, FILE_SKS_KSH_NCHJ, FILE_SKS_KNR_NCHJ,
                   STTS, ZIP_FILE_SIZE, ZIP_FILE_MEI, TRK_PRGRM_ID, TRK_SYSTM_UNYO_KIGYO_CODE,
                   TRK_SHAIN_CODE, KSHN_PRGRM_ID, KSHN_SYSTM_UNYO_KIGYO_CODE, KSHN_SHAIN_CODE,
                   VRSN, RCRD_TRK_NCHJ, RCRD_KSHN_NCHJ,CTGRY_KUBUN
            FROM T_DWNLD_RRK
            WHERE RRK_BANGO = ?
            """;

        return jdbcTemplate.queryForObject(sql, new Object[]{rrkBango}, this::mapRowToExportJobStatusBasic);
    }


    
    /**
     * 社員コードでエクスポートジョブステータスリストを取得
     */
    public List<ExportJobStatus> findByShainCode(String shainCode,String systemOperationCompanyCode, int limit, int offset) throws SQLException {
        String sql = """
                SELECT
                        t.RRK_BANGO,
                        t.SYSTM_UNYO_KIGYO_CODE,
                        t.SHAIN_CODE,
                        t.FILE_SHBTS,
                        t.AREA,
                        t.HNSH_BASHO_KUBUN,
                        t.DATA_KUBUN,
                        t.CTGRY_KUBUN,
                        t.FILE_SKS_KSH_NCHJ,
                        t.FILE_SKS_KNR_NCHJ,
                        t.STTS,
                        COALESCE(t.ZIP_FILE_SIZE, '') AS ZIP_FILE_SIZE,
                        COALESCE(t.ZIP_FILE_MEI, '') AS ZIP_FILE_MEI,
                        t.TRK_PRGRM_ID,
                        t.TRK_SYSTM_UNYO_KIGYO_CODE,
                        t.TRK_SHAIN_CODE,
                        t.KSHN_PRGRM_ID,
                        t.KSHN_SYSTM_UNYO_KIGYO_CODE,
                        t.KSHN_SHAIN_CODE,
                        t.VRSN,
                        t.RCRD_TRK_NCHJ,
                        t.RCRD_KSHN_NCHJ,
                        -- ファイル種別をSQL内でCASE WHEN文により変換
                        CASE
                            WHEN t.FILE_SHBTS = '1' THEN '次年度計画マスタ'
                            WHEN t.FILE_SHBTS = '2' THEN '見通し・計画_採算管理単位C別 / 見通し・計画_企業別＜本社＞'
                            WHEN t.FILE_SHBTS = '3' THEN '見通し・計画_採算管理単位C別 / 見通し・計画_企業別＜エリア＞'
                            WHEN t.FILE_SHBTS = '4' THEN '間接利益計画_メーカー別'
                            ELSE COALESCE(t.FILE_SHBTS, '')
                        END AS FILE_SHBTS_NAME,
                        t.AREA AS AREA_NAME,
                        -- 本社場所区分をSQL内でCASE WHEN文により変換
                        CASE
                            WHEN t.HNSH_BASHO_KUBUN = '0' THEN '本社'
                            WHEN t.HNSH_BASHO_KUBUN = '1' THEN '場所'
                            WHEN t.HNSH_BASHO_KUBUN = '2' THEN '本社・場所'
                            ELSE COALESCE(t.HNSH_BASHO_KUBUN, '')
                        END AS HNSH_BASHO_KUBUN_NAME,
                        -- データ区分をSQL内でCASE WHEN文により変換（単一値のみ）
                        CASE
                            WHEN t.DATA_KUBUN = '0' THEN '移管前'
                            WHEN t.DATA_KUBUN = '1' THEN '移管後'
                            WHEN t.DATA_KUBUN = '0,1' THEN '移管前,移管後'
                            ELSE COALESCE(t.DATA_KUBUN, '')
                        END AS DATA_KUBUN_NAME,
                        -- ステータスをSQL内でCASE WHEN文により変換
                        CASE
                            WHEN t.STTS = '0' THEN '処理中'
                            WHEN t.STTS = '1' THEN '完了'
                            WHEN t.STTS = '2' THEN '一部失敗'
                            WHEN t.STTS = '3' THEN '失敗'
                            WHEN t.STTS = '4' THEN '失敗'
                            ELSE ''
                        END AS STTS_NAME,
                        CASE
                            WHEN t.CTGRY_KUBUN = '1' THEN '加食'
                            WHEN t.CTGRY_KUBUN = '2' THEN '低温'
                            WHEN t.CTGRY_KUBUN = '3' THEN '酒類'
                            WHEN t.CTGRY_KUBUN = '4' THEN '菓子'
                            WHEN t.CTGRY_KUBUN = '5' THEN '全カテゴリー'
                            ELSE ''
                        END AS CTGRY_KUBUN_NAME
                    FROM
                        T_DWNLD_RRK t
                    WHERE
                        t.SHAIN_CODE = ?
                        AND t.RCRD_TRK_NCHJ >= TO_CHAR(CURRENT_DATE - INTERVAL '10 days', 'YYYYMMDD') || '000000'
                        AND t.SYSTM_UNYO_KIGYO_CODE = ?
                    ORDER BY
                        t.RCRD_TRK_NCHJ DESC
                    LIMIT ? OFFSET ?
            """;

        return jdbcTemplate.query(sql, new Object[]{shainCode, systemOperationCompanyCode, limit, offset}, this::mapRowToExportJobStatus);
    }


    /**
     * ResultSetからExportJobStatusオブジェクトにマッピング（完全版）
     * findByShainCode用：変換された名称フィールドを含む完全なマッピング
     */
    private ExportJobStatus mapRowToExportJobStatus(ResultSet rs) throws SQLException {
        ExportJobStatus jobStatus = mapRowToExportJobStatusBasic(rs);
        // 名称フィールド（完全版のみ）
        jobStatus.setFileShbtsName(rs.getString("FILE_SHBTS_NAME")); // SQL内でCASE WHEN変換されたファイル種別名
        jobStatus.setAreaName(rs.getString("AREA_NAME")); // SQL内でCASE WHEN変換されたエリア名
        jobStatus.setHnshBashoKubunName(rs.getString("HNSH_BASHO_KUBUN_NAME")); // SQL内でCASE WHEN変換された本社場所区分名
        jobStatus.setDataKubunName(rs.getString("DATA_KUBUN_NAME")); // SQL内でCASE WHEN変換されたデータ区分名
        jobStatus.setSttsName(rs.getString("STTS_NAME")); // SQL内でCASE WHEN変換されたステータス名
        jobStatus.setCtgryKubunName(rs.getString("CTGRY_KUBUN_NAME"));
        return jobStatus;
    }

    /**
     * ResultSetからExportJobStatusオブジェクトにマッピング（共通処理）
     * @param rs ResultSet
     * @return ExportJobStatus
     * @throws SQLException SQL例外
     */
    private ExportJobStatus mapRowToExportJobStatusBasic(ResultSet rs) throws SQLException {
        ExportJobStatus jobStatus = new ExportJobStatus();

        // 主キー
        jobStatus.setRrkBango(rs.getLong("RRK_BANGO"));

        // 基本情報
        jobStatus.setSystmUnyoKigyoCode(rs.getString("SYSTM_UNYO_KIGYO_CODE"));
        jobStatus.setShainCode(rs.getString("SHAIN_CODE"));
        jobStatus.setFileShbts(rs.getString("FILE_SHBTS"));
        jobStatus.setArea(rs.getString("AREA"));
        jobStatus.setHnshBashoKubun(rs.getString("HNSH_BASHO_KUBUN"));
        jobStatus.setDataKubun(rs.getString("DATA_KUBUN"));
        jobStatus.setCtgryKubun(rs.getString("CTGRY_KUBUN"));

        // 処理日時
        jobStatus.setFileSksKshNchj(rs.getString("FILE_SKS_KSH_NCHJ"));
        jobStatus.setFileSksKnrNchj(rs.getString("FILE_SKS_KNR_NCHJ"));

        // ステータス・結果
        jobStatus.setStts(rs.getString("STTS"));

        jobStatus.setZipFileSize(rs.getString("ZIP_FILE_SIZE"));
        jobStatus.setZipFileMei(rs.getString("ZIP_FILE_MEI"));

        // 登録情報
        jobStatus.setTrkPrgrmId(rs.getString("TRK_PRGRM_ID"));
        jobStatus.setTrkSystmUnyoKigyoCode(rs.getString("TRK_SYSTM_UNYO_KIGYO_CODE"));
        jobStatus.setTrkShainCode(rs.getString("TRK_SHAIN_CODE"));

        // 更新情報
        jobStatus.setKshnPrgrmId(rs.getString("KSHN_PRGRM_ID"));
        jobStatus.setKshnSystmUnyoKigyoCode(rs.getString("KSHN_SYSTM_UNYO_KIGYO_CODE"));
        jobStatus.setKshnShainCode(rs.getString("KSHN_SHAIN_CODE"));

        // システム情報
        Integer vrsn = rs.getInt("VRSN");
        jobStatus.setVrsn(rs.wasNull() ? null : vrsn);
        jobStatus.setRcrdTrkNchj(rs.getString("RCRD_TRK_NCHJ"));
        jobStatus.setRcrdKshnNchj(rs.getString("RCRD_KSHN_NCHJ"));

        return jobStatus;
    }

}
