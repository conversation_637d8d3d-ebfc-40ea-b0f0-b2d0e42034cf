AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: <PERSON>.

Parameters:
  ProjectPrefix:
    Type: String
    Default: ms-bp
    Description: "The project prefix"
  Env:
    Type: String
    Default: prod
    Description: "The deployment environment"
  SubPrefix:
    Type: String
    Default: standard
    Description: "The project sub-prefix"

Resources:
  MsBpMainFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "${ProjectPrefix}-main-lambda-${Env}-${SubPrefix}"
      Handler: com.ms.bp.interfaces.LambdaHandler::handleRequest
      Runtime: java21
      CodeUri: ./
      MemorySize: 1024
      Timeout: 900
      VpcConfig:
        SecurityGroupIds:
          - {"Fn::ImportValue": !Sub "${ProjectPrefix}-sg-lambda-${Env}-${SubPrefix}"}
        SubnetIds:
          - {"Fn::ImportValue": !Sub "${ProjectPrefix}-private-subnet-a1-${Env}-${SubPrefix}"}
          - {"Fn::ImportValue": !Sub "${ProjectPrefix}-private-subnet-c1-${Env}-${SubPrefix}"}
      Policies:
        - S3WritePolicy:
            BucketName: !Sub "${ProjectPrefix}-files-${Env}-${SubPrefix}"
        - S3ReadPolicy:
            BucketName: !Sub "${ProjectPrefix}-files-${Env}-${SubPrefix}"
        - LambdaInvokePolicy:
            FunctionName: !Ref MsBpWorkerFunction
        - VPCAccessPolicy: {}
        - Statement:
            - Effect: Allow
              Action:
                - ssm:GetParameter
                - ssm:GetParameters
                - ssm:GetParametersByPath
              Resource: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/ms-bp/${Env}/${SubPrefix}/db/*"
      Environment:
        Variables:
          SPRING_PROFILES_ACTIVE: !Sub "${Env}"
          WORKER_FUNCTION_NAME: !Ref MsBpWorkerFunction

  MsBpWorkerFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "${ProjectPrefix}-worker-lambda-${Env}-${SubPrefix}"
      Handler: com.ms.bp.interfaces.WorkerHandler::handleRequest
      Runtime: java21
      CodeUri: ./
      MemorySize: 1024
      Timeout: 900
      ReservedConcurrentExecutions: 10
      VpcConfig:
        SecurityGroupIds:
          - {"Fn::ImportValue": !Sub "${ProjectPrefix}-sg-lambda-${Env}-${SubPrefix}"}
        SubnetIds:
          - {"Fn::ImportValue": !Sub "${ProjectPrefix}-private-subnet-a1-${Env}-${SubPrefix}"}
          - {"Fn::ImportValue": !Sub "${ProjectPrefix}-private-subnet-c1-${Env}-${SubPrefix}"}
      Policies:
        - S3WritePolicy:
            BucketName: !Sub "${ProjectPrefix}-files-${Env}-${SubPrefix}"
        - S3ReadPolicy:
            BucketName: !Sub "${ProjectPrefix}-files-${Env}-${SubPrefix}"
        - VPCAccessPolicy: {}
        - Statement:
            - Effect: Allow
              Action:
                - ssm:GetParameter
                - ssm:GetParameters
                - ssm:GetParametersByPath
              Resource: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/ms-bp/${Env}/${SubPrefix}/db/*"
      Environment:
        Variables:
          SPRING_PROFILES_ACTIVE: !Sub "${Env}"

  ApiResourceApi:
    Type: AWS::ApiGateway::Resource
    Properties:
      ParentId: {"Fn::ImportValue": !Sub "${ProjectPrefix}-RootResourceId-${Env}-${SubPrefix}"}
      PathPart: "{proxy+}"
      RestApiId: {"Fn::ImportValue": !Sub "${ProjectPrefix}-RestApiId-${Env}-${SubPrefix}"}

  ApiMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      HttpMethod: ANY
      ResourceId: !Ref ApiResourceApi
      RestApiId: {"Fn::ImportValue": !Sub "${ProjectPrefix}-RestApiId-${Env}-${SubPrefix}"}
      ApiKeyRequired: true
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub
          - 'arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${LambdaArn}/invocations'
          - LambdaArn: !GetAtt MsBpMainFunction.Arn

  LambdaPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunction
      FunctionName: !GetAtt MsBpMainFunction.Arn
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub
        - 'arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${RestApiId}/*/*/*'
        - RestApiId: {"Fn::ImportValue": !Sub "${ProjectPrefix}-RestApiId-${Env}-${SubPrefix}"}

  ApiDeployment:
    Type: AWS::ApiGateway::Deployment
    Properties:
      RestApiId: {"Fn::ImportValue": !Sub "${ProjectPrefix}-RestApiId-${Env}-${SubPrefix}"}
    DependsOn: ApiMethod

Outputs:
  NewMsBpApiDeploymentId:
    Description: "The ID of the new API Gateway Deployment"
    Value: !Ref ApiDeployment