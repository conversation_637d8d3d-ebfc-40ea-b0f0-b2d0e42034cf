package com.ms.bp.shared.common.exception;
/**
 * バリデーションエラー用のカスタム例外
 */
public class ValidationException extends ServiceException {
    public ValidationException(Message message, Object... parameters) {
        ParameterizedErrorCode parameterizedErrorCode = new ParameterizedErrorCode(message, parameters);
        this.setCode(parameterizedErrorCode.getCode());
        this.setMessage(parameterizedErrorCode.getFormattedMessage());
    }

    public ValidationException(String message) {
        super();
        this.setMessage(message);
    }
}
