package com.ms.bp.interfaces.dto.request;

import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.exception.ServiceException;
import lombok.Data;

/**
 * ダウンロードURL生成リクエストDTO
 * 履歴番号ベースのファイルダウンロード用リクエストパラメータ
 */
@Data
public class DownloadUrlRequest {
    
    /**
     * 履歴番号
     * エクスポート履歴またはインポート履歴のプライマリキー
     */
    private Long rrkBango;
    
    /**
     * 操作タイプ
     */
    private String operationType;
    

    /**
     * URL有効期限（秒）
     * デフォルト: 3600秒（1時間）
     */
    private Integer expiresInSeconds = 3600;

    /**
     * リクエストパラメータの妥当性を検証
     * 必須項目チェックと値の妥当性を確認する
     * 
     * @throws ServiceException バリデーションエラーの場合
     */
    public void validate() {
        // 履歴番号の必須チェック
        if (rrkBango == null) {
            throw new ServiceException(GlobalMessageConstants.BAD_REQUEST.getCode(),
                    "履歴番号（rrkBango）は必須です");
        }

        // 履歴番号の値チェック
        if (rrkBango <= 0) {
            throw new ServiceException(GlobalMessageConstants.BAD_REQUEST.getCode(),
                    "履歴番号（rrkBango）は正の数値である必要があります");
        }

        // 操作タイプの必須チェック
        if (operationType == null || operationType.trim().isEmpty()) {
            throw new ServiceException(GlobalMessageConstants.BAD_REQUEST.getCode(),
                    "操作タイプ（operationType）は必須です");
        }

        // 操作タイプの値チェック
        if (!BusinessConstants.OPERATION_DOWNLOAD_CODE.equals(operationType) &&
            !BusinessConstants.OPERATION_UPLOAD_CODE.equals(operationType)) {
            throw new ServiceException(GlobalMessageConstants.BAD_REQUEST.getCode(),
                    "操作タイプ（operationType）は 'export' または 'import' である必要があります");
        }
        // URL有効期限の値チェック
        if (expiresInSeconds != null && expiresInSeconds <= 0) {
            throw new ServiceException(GlobalMessageConstants.BAD_REQUEST.getCode(),
                    "URL有効期限（expiresInSeconds）は正の数値である必要があります");
        }

        // URL有効期限の上限チェック（24時間以内）
        if (expiresInSeconds != null && expiresInSeconds > 86400) {
            throw new ServiceException(GlobalMessageConstants.BAD_REQUEST.getCode(),
                    "URL有効期限（expiresInSeconds）は86400秒（24時間）以内である必要があります");
        }
    }


    /**
     * リクエスト情報の文字列表現
     * ログ出力用
     * @return リクエスト情報の文字列
     */
    @Override
    public String toString() {
        return String.format("DownloadUrlRequest{履歴番号=%d, 操作タイプ='%s',  有効期限=%d秒}",
                           rrkBango, operationType,  expiresInSeconds);
    }
}
