package com.ms.bp.domain.unit.repository;

/**
 * ユニット-エリア マッピングリポジトリ領域インターフェース
 * ユニットコードとエリアコードの対応関係を取得する
 */
public interface UnitAreaMappingRepository {
    
    /**
     * 指定ユニットコードに対応するエリアコードを取得
     * 
     * @param unitCode ユニットコード
     * @param systemOperationCompanyCode システム運用企業コード
     * @return エリアコード（見つからない場合は空文字列）
     */
    String findAreaCodeByUnitCode(String unitCode, String systemOperationCompanyCode);
}