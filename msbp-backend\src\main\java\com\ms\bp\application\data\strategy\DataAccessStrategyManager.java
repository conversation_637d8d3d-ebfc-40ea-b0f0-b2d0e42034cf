package com.ms.bp.application.data.strategy;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SQL戦略管理器
 * 業務レベルでSQL構築戦略の登録と選択を管理
 * 各業務サービスが独立して戦略を管理できるように設計
 */
public class DataAccessStrategyManager {
    private static final Logger logger = LoggerFactory.getLogger(DataAccessStrategyManager.class);

    private final Map<String, DataAccessStrategy> strategies = new ConcurrentHashMap<>();

    /**
     * 戦略を登録
     * @param strategy 登録する戦略
     */
    public void registerStrategy(DataAccessStrategy strategy) {
        String strategyKey = strategy.getStrategyName();
        strategies.put(strategyKey.toUpperCase(), strategy);

        logger.info("SQL戦略登録: 戦略={}", strategy.getStrategyName());
    }
    
    /**
     * 戦略キーで戦略を取得
     * @param strategyKey 戦略キー
     * @return SQL構築戦略、見つからない場合はnull
     */
    public DataAccessStrategy getStrategy(String strategyKey) {
        DataAccessStrategy strategy = strategies.get(strategyKey.toUpperCase());

        if (strategy == null) {
            logger.warn("SQL戦略が見つかりません: 戦略キー={}", strategyKey);
        }

        return strategy;
    }



    /**
     * 登録されている戦略の統計情報をログ出力
     */
    public void logStatistics() {
        logger.info("SQL戦略管理器統計: 総戦略数={}", strategies.size());
        strategies.values().forEach(strategy ->
            logger.info("  - {}", strategy.getStrategyName()));
    }
}
