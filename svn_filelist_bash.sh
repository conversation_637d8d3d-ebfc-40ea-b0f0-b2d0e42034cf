#!/bin/bash
# SVNリポジトリファイル一覧取得スクリプト - bash版

# デフォルト値
CONFIG_FILE="svn-config.txt"
BATCH_SIZE=1000
OUTPUT_DIRECTORY="/output"
RESUME_MODE=false
VERBOSE_LOGGING=false
METHOD="auto"

# グローバル変数
TOTAL_FILES=0
TOTAL_SIZE_BYTES=0
PROCESSED_REPOSITORIES=()
CURRENT_BATCH_NUMBER=1
START_TIME=$(date +%s)

# HTML実体デコード関数
decode_html_entities() {
    local input="$1"
    local output="$input"

    # Python3が利用可能な場合は使用
    if command -v python3 >/dev/null 2>&1; then
        output=$(echo "$input" | python3 -c "import sys, html; print(html.unescape(sys.stdin.read().strip()))")
    # Python2が利用可能な場合は使用
    elif command -v python >/dev/null 2>&1; then
        output=$(echo "$input" | python -c "import sys, HTMLParser; print HTMLParser.HTMLParser().unescape(sys.stdin.read().strip())")
    # Pythonが利用できない場合は基本的な変換のみ
    else
        # 数値文字参照の基本的な変換（16進数）
        output=$(echo "$input" | sed 's/&#x\([0-9A-Fa-f]\{4\}\);/\\u\1/g')
        # printf でUnicode変換を試行（環境によっては動作しない場合がある）
        if printf '\u5468' >/dev/null 2>&1; then
            output=$(printf "$output" 2>/dev/null || echo "$input")
        fi
    fi

    echo "$output"
}

# 引数解析
while [[ $# -gt 0 ]]; do
    case $1 in
        --config-file)
            CONFIG_FILE="$2"
            shift 2
            ;;
        --batch-size)
            BATCH_SIZE="$2"
            shift 2
            ;;
        --output-directory)
            OUTPUT_DIRECTORY="$2"
            shift 2
            ;;
        --resume-mode)
            RESUME_MODE=true
            shift
            ;;
        --verbose-logging)
            VERBOSE_LOGGING=true
            shift
            ;;
        --method)
            METHOD="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# 依存関係のチェック
check_dependencies() {
    local missing_deps=()
    
    if ! command -v svn >/dev/null 2>&1; then
        missing_deps+=("svn")
    fi
    
    if ! command -v svnlook >/dev/null 2>&1; then
        missing_deps+=("svnlook")
    fi
    
    if ! command -v xmllint >/dev/null 2>&1; then
        missing_deps+=("xmllint")
    fi
    
    if ! command -v bc >/dev/null 2>&1; then
        missing_deps+=("bc")
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        echo "Error: Missing required dependencies: ${missing_deps[*]}"
        echo "Please install the missing dependencies first."
        exit 1
    fi
    
    local svn_version=$(svn --version --quiet 2>/dev/null)
    echo "SVN client found: version $svn_version"
}

# 設定ファイル読み込み
read_configuration_file() {
    if [[ ! -f "$CONFIG_FILE" ]]; then
        echo "Error: Configuration file not found: $CONFIG_FILE"
        exit 1
    fi

    echo "Loading CSV configuration from: $CONFIG_FILE"

    local repositories=()
    local count=0

    while IFS= read -r line || [[ -n "$line" ]]; do
        line=$(echo "$line" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')  # trim

        # コメント行と空行をスキップ
        if [[ -n "$line" && ! "$line" =~ ^# ]]; then
            # CSV形式: RepositoryName,RepositoryPath,SubDirectory,
            IFS=',' read -ra fields <<< "$line"
            if [[ ${#fields[@]} -ge 3 ]]; then
                local repository_name="${fields[0]// /}"  # trim spaces
                local repository_path="${fields[1]// /}"  # trim spaces
                local sub_directory="${fields[2]// /}"    # trim spaces

                # 空のsub_directoryの場合は空文字列に設定
                if [[ -z "$sub_directory" ]]; then
                    sub_directory=""
                fi

                # リポジトリパスの検証
                if [[ ! -d "$repository_path" ]]; then
                    echo "Warning: Repository path does not exist: $repository_path"
                    if [[ "$VERBOSE_LOGGING" == "true" ]]; then
                        echo "  Skipping invalid path: $repository_path"
                    fi
                    continue
                fi

                local full_path="$repository_path"
                if [[ -n "$sub_directory" ]]; then
                    full_path="$repository_path$sub_directory"
                fi

                local sub_path=""
                if [[ -n "$sub_directory" ]]; then
                    sub_path="${sub_directory#/}"  # 先頭の/を削除
                fi

                # リポジトリ情報を配列に追加
                repositories+=("$full_path|$repository_path|$sub_path|$repository_name|$sub_directory")

                if [[ "$VERBOSE_LOGGING" == "true" ]]; then
                    echo "  Added repository: $repository_name"
                    echo "    Repository path: $repository_path"
                    if [[ -n "$sub_directory" ]]; then
                        echo "    Sub directory: $sub_directory"
                    fi
                fi

                ((count++))
            else
                echo "Warning: Invalid CSV format line (expected: RepositoryName,RepositoryPath,SubDirectory,): $line"
            fi
        fi
    done < "$CONFIG_FILE"

    if [[ $count -eq 0 ]]; then
        echo "Error: No valid repositories found in configuration file. Please ensure the file uses CSV format: RepositoryName,RepositoryPath,SubDirectory,"
        exit 1
    fi

    echo "Loaded $count repositories from CSV configuration file"

    # グローバル配列に設定
    REPOSITORIES_TO_PROCESS=("${repositories[@]}")
}

# 処理済みリポジトリ検出（再開機能）
test_repository_processed() {
    local repository_name="$1"
    
    if [[ "$RESUME_MODE" != "true" ]]; then
        return 1  # false
    fi
    
    echo "Checking if repository '$repository_name' is already processed..."
    
    # 既存のCSVファイルパターンを検索
    local existing_files=()
    
    # パターン1: *repository_name*_batch_*.csv
    while IFS= read -r -d '' file; do
        existing_files+=("$file")
    done < <(find "$OUTPUT_DIRECTORY" -name "*${repository_name}*_batch_*.csv" -print0 2>/dev/null)
    
    # パターン2: *repository_name*.csv
    while IFS= read -r -d '' file; do
        existing_files+=("$file")
    done < <(find "$OUTPUT_DIRECTORY" -name "*${repository_name}*.csv" -print0 2>/dev/null)
    
    if [[ ${#existing_files[@]} -gt 0 ]]; then
        echo "Skip: Repository '$repository_name' (already processed - found ${#existing_files[@]} existing files)"
        
        if [[ "$VERBOSE_LOGGING" == "true" ]]; then
            echo "  Existing files:"
            for file in "${existing_files[@]}"; do
                echo "    $(basename "$file")"
            done
        fi
        
        return 0  # true
    fi
    
    echo "No existing files found for repository '$repository_name' - will process"
    return 1  # false
}

# SVN List XML結果処理
process_svn_list_xml_result() {
    local xml_file="$1"
    local repository_name="$2"
    local sub_path="$3"
    local sub_directory="${4:-}"
    
    # XMLファイルの存在と内容を確認
    if [[ ! -f "$xml_file" ]]; then
        echo "Error: XML file does not exist: $xml_file"
        return 1
    fi
    
    if [[ ! -s "$xml_file" ]]; then
        echo "Error: XML file is empty: $xml_file"
        return 1
    fi

    # echo -e "\nDebug: Contents of xml_file:"
    # cat "$xml_file"
    # echo -e "\n--- End of file ---"
    
    # XMLからファイルエントリを抽出 
    local file_entries   
    local all_entries
    all_entries=$(cat "$xml_file" | grep -A 10 '<entry' | grep -v '^--$' || echo "")
    
    if [[ -z "$all_entries" ]]; then
        echo "No entries found in XML result"
        return 1
    fi
    
    # kind="file"のエントリのみを抽出
    file_entries=""
    local in_file_entry=false
    local current_entry=""
    local entry_kind=""
    
    while IFS= read -r line; do
        if [[ "$line" =~ \<entry ]]; then
            # 前のエントリを処理
            if [[ "$current_entry" != "" && "$in_file_entry" == true ]]; then
                file_entries="${file_entries}${current_entry}</entry>"$'\n'
            fi
            current_entry="$line"$'\n'
            entry_kind=""
            in_file_entry=false
            
            # 同じ行にkind属性がある場合
            if [[ "$line" =~ kind=\"file\" ]]; then
                in_file_entry=true
                entry_kind="file"
            elif [[ "$line" =~ kind=\"dir\" ]]; then
                in_file_entry=false
                entry_kind="dir"
            fi
        elif [[ "$line" =~ kind=\"file\" ]] && [[ "$entry_kind" == "" ]]; then
            # kind属性が別の行にある場合
            in_file_entry=true
            entry_kind="file"
            current_entry="${current_entry}${line}"$'\n'
        elif [[ "$line" =~ kind=\"dir\" ]] && [[ "$entry_kind" == "" ]]; then
            # kind属性が別の行にある場合
            in_file_entry=false
            entry_kind="dir"
            current_entry="${current_entry}${line}"$'\n'
        elif [[ "$line" =~ \</entry\> ]]; then
            if [[ "$in_file_entry" == true ]]; then
                current_entry="${current_entry}${line}"
                file_entries="${file_entries}${current_entry}"$'\n'
            fi
            current_entry=""
            in_file_entry=false
            entry_kind=""
        elif [[ "$current_entry" != "" ]]; then
            current_entry="${current_entry}${line}"$'\n'
        fi
    done < "$xml_file"
    
    # 最後のエントリを処理
    if [[ "$current_entry" != "" && "$in_file_entry" == true ]]; then
        file_entries="${file_entries}${current_entry}</entry>"$'\n'
    fi
    
    # 結果の検証
    if [[ -z "$file_entries" ]]; then
        # デバッグ情報を表示
        local total_entries
        total_entries=$(grep -c '<entry' "$xml_file" 2>/dev/null || echo "0")
        local dir_entries  
        dir_entries=$(grep -c 'kind="dir"' "$xml_file" 2>/dev/null || echo "0")
        local file_entry_count
        file_entry_count=$(grep -c 'kind="file"' "$xml_file" 2>/dev/null || echo "0")
        
        echo "No files found in XML result (only directories found)"
        echo "Total entries: $total_entries, Directory entries: $dir_entries, File entries: $file_entry_count"
        
        return 1
    fi
    
    echo "Debug: file_entries-----: $file_entries"
    # ファイル数をカウント
    local total_files
    total_files=$(echo "$file_entries" | grep -c '<entry' || echo "0")
    
    echo "Found $total_files files in repository"
    if [[ -n "$sub_path" ]]; then
        echo "Processing subpath: $sub_path"
    fi
    
    # 有効なファイルが見つからない場合は処理を中止
    if [[ "$total_files" -eq 0 ]]; then
        echo "No valid files found to process"
        return 1
    fi
    
    local file_count=0
    local batch_data=()
    
    # XMLエントリを一つずつ処理
    local temp_file=$(mktemp)
    echo "$file_entries" > "$temp_file"

    # XMLの妥当性を再確認してからデータ抽出
    # if ! xmllint --noout "$temp_file" >/dev/null 2>&1; then
    #     echo "Error: Cannot parse XML entries - invalid XML format"
    #     rm -f "$temp_file"
    #     return 1
    # fi

    local file_names file_sizes file_dates

    # ファイル名を抽出
    file_names=$(sed -n 's/.*<name>\(.*\)<\/name>.*/\1/p' "$temp_file")

    # ファイルサイズを抽出
    file_sizes=$(sed -n 's/.*<size>\(.*\)<\/size>.*/\1/p' "$temp_file")

    # 更新日時を抽出
    file_dates=$(sed -n 's/.*<date>\(.*\)<\/date>.*/\1/p' "$temp_file")

    # 抽出結果の検証
    if [[ -z "$file_names" ]]; then
        echo "Error: Failed to extract file names from XML"
        rm -f "$temp_file"
        return 1
    fi

    rm -f "$temp_file"

    # 抽出したデータを配列に変換
    local names_array=()
    local sizes_array=()
    local dates_array=()

    if [[ -n "$file_names" ]]; then
        while IFS= read -r name; do
            [[ -n "$name" ]] && names_array+=("$name")
        done <<< "$file_names"
    fi

    if [[ -n "$file_sizes" ]]; then
        while IFS= read -r size; do
            [[ -n "$size" ]] && sizes_array+=("$size")
        done <<< "$file_sizes"
    fi

    if [[ -n "$file_dates" ]]; then
        while IFS= read -r date; do
            [[ -n "$date" ]] && dates_array+=("$date")
        done <<< "$file_dates"
    fi

    # 各ファイルを処理
    for ((i=0; i<${#names_array[@]}; i++)); do
        ((file_count++))

        local file_path="${names_array[i]}"
        local size_bytes="${sizes_array[i]:-0}"
        local last_modified="${dates_array[i]:-}"

        # HTML実体をデコード
        file_path=$(decode_html_entities "$file_path")

        # サブパスがある場合はパスにプレフィックスを追加
        if [[ -n "$sub_path" ]]; then
            file_path="$sub_path/$file_path"
        fi

        local size_mb
        # bc を使用する前に size_bytes が数値であることを確認する
        if [[ "$size_bytes" =~ ^[0-9]+$ ]]; then
            size_mb=$(echo "scale=3; $size_bytes / 1048576" | bc)
            # 小数点まで0が表示
            if [[ "$size_mb" =~ ^\. ]]; then
                size_mb="0$size_mb"
            fi
        else
            echo "Warning: Invalid size_bytes value: $size_bytes"
            size_bytes=0
            size_mb="0.000"
        fi

        # 進行状況表示
        if [[ $((file_count % 100)) -eq 0 ]] || [[ "$VERBOSE_LOGGING" == "true" ]]; then
            local percent_complete
            if [[ ${#names_array[@]} -gt 0 ]]; then
                percent_complete=$(echo "scale=1; $file_count * 100 / ${#names_array[@]}" | bc)
            else
                percent_complete="0.0"
            fi
            echo "[$percent_complete%] Processing: $file_count/${#names_array[@]} files"
        fi

        if [[ "$VERBOSE_LOGGING" == "true" ]]; then
            echo "  File: $file_path ($size_mb MB)"
        fi

        # バッチデータに追加
        batch_data+=("\"$file_path\",$size_mb,$size_bytes,$last_modified")

        ((TOTAL_FILES++))
        TOTAL_SIZE_BYTES=$((TOTAL_SIZE_BYTES + size_bytes))

        # バッチ出力
        if [[ ${#batch_data[@]} -ge $BATCH_SIZE ]]; then
            export_batch_to_csv batch_data "$repository_name" "$sub_directory"
            batch_data=()
        fi
    done

    # 残りのバッチ出力
    if [[ ${#batch_data[@]} -gt 0 ]]; then
        export_batch_to_csv batch_data "$repository_name" "$sub_directory"
    fi
    
    echo "SVN list method completed: $file_count files processed"
    return 0
}

# SVNLook使用ファイル一覧取得
get_svn_file_list_using_svnlook() {
    local repository_path="$1"
    local repository_root="$2"
    local sub_path="$3"
    local repository_name="$4"
    local sub_directory="${5:-}"
    
    echo "Using svnlook for repository analysis..."
    
    # リポジトリルートを使用
    local repo_root="${repository_root:-$repository_path}"
    
    # svnlook tree で全ファイルパスを取得
    local tree_cmd
    if [[ -n "$sub_path" ]]; then
        tree_cmd="svnlook tree \"$repo_root\" \"$sub_path\" --full-paths"
    else
        tree_cmd="svnlook tree \"$repo_root\" --full-paths"
    fi
    
    if [[ "$VERBOSE_LOGGING" == "true" ]]; then
        echo "Command: $tree_cmd"
    fi
    
    local tree_result
    tree_result=$(eval "$tree_cmd" 2>/dev/null)
    
    if [[ $? -ne 0 ]]; then
        echo "Error: SVNLook tree command failed"
        return 1
    fi
    
    # ファイルパスのフィルタリング（ディレクトリを除外）
    local file_paths=()
    while IFS= read -r line; do
        line=$(echo "$line" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')  # trim
        if [[ -n "$line" && ! "$line" =~ /$ ]]; then
            file_paths+=("$line")
        fi
    done <<< "$tree_result"
    
    echo "Found ${#file_paths[@]} files using svnlook"
    if [[ -n "$sub_path" ]]; then
        echo "Processing subpath: $sub_path"
    fi
    
    local file_count=0
    local batch_data=()
    
    for file_path in "${file_paths[@]}"; do
        ((file_count++))
        
        # 進行状況表示
        if [[ $((file_count % 50)) -eq 0 ]] || [[ "$VERBOSE_LOGGING" == "true" ]]; then
            local percent_complete
            if [[ ${#file_paths[@]} -gt 0 ]]; then
                percent_complete=$(echo "scale=1; $file_count * 100 / ${#file_paths[@]}" | bc)
            else
                percent_complete="0.0"
            fi
            echo "[$percent_complete%] Processing: $file_count/${#file_paths[@]} files"
        fi
        
        # ファイルサイズ取得
        local size_bytes
        size_bytes=$(svnlook filesize "$repo_root" "$file_path" 2>/dev/null | head -n1 | sed 's/^[[:space:]]*//;s/[[:space:]]*$//' || echo "0")
        [[ -z "$size_bytes" ]] && size_bytes=0
        
        # Validate size_bytes is numeric before using bc
        if [[ ! "$size_bytes" =~ ^[0-9]+$ ]]; then
            echo "Warning: Invalid size_bytes value for $file_path: $size_bytes"
            size_bytes=0
        fi
        
        local size_mb
        size_mb=$(echo "scale=3; $size_bytes / 1048576" | bc)
        # 小数点前に0を強制表示する
        if [[ "$size_mb" =~ ^\. ]]; then
            size_mb="0$size_mb"
        fi
        local last_modified
        last_modified=$(date '+%Y-%m-%d %H:%M:%S')
        
        if [[ "$VERBOSE_LOGGING" == "true" ]]; then
            echo "  File: $file_path ($size_mb MB)"
        fi
        
        local clean_file_path="${file_path//\\//}"
        batch_data+=("\"$clean_file_path\",$size_mb,$size_bytes,$last_modified")
        
        ((TOTAL_FILES++))
        TOTAL_SIZE_BYTES=$((TOTAL_SIZE_BYTES + size_bytes))
        
        # バッチ出力
        if [[ ${#batch_data[@]} -ge $BATCH_SIZE ]]; then
            export_batch_to_csv batch_data "$repository_name" "$sub_directory"
            batch_data=()
        fi
    done

    # 残りのバッチ出力
    if [[ ${#batch_data[@]} -gt 0 ]]; then
        export_batch_to_csv batch_data "$repository_name" "$sub_directory"
    fi
    
    echo "SVNLook method completed: $file_count files processed"
    return 0
}

# バッチCSV出力
export_batch_to_csv() {
    local data_ref_name="$1"
    eval "local -a data_ref=(\"\${${data_ref_name}[@]}\")"
    local repository_name="$2"
    local sub_directory="${3:-}"

    if [[ ${#data_ref[@]} -eq 0 ]]; then
        return
    fi

    if [[ ! -d "$OUTPUT_DIRECTORY" ]]; then
        mkdir -p "$OUTPUT_DIRECTORY"
    fi

    local timestamp
    timestamp=$(date '+%Y%m%d_%H%M%S_%3N')
    local unique_id=$((RANDOM % 9000 + 1000))

    # RepositoryNameをクリーンアップ
    local safe_repo_name
    safe_repo_name=$(echo "$repository_name" | sed 's/[\\/:*?"<>|]/_/g')

    local sub_dir_part=""
    if [[ -n "$sub_directory" ]]; then
        # SubDirectoryから先頭の/を削除し、/を_に置換
        local clean_sub_dir="${sub_directory#/}"  # 先頭の/を削除
        clean_sub_dir=$(echo "$clean_sub_dir" | sed 's/\//_/g')  # /を_に置換
        clean_sub_dir=$(echo "$clean_sub_dir" | sed 's/[\\:*?"<>|]/_/g')  # 特殊文字を_に置換
        sub_dir_part="_$clean_sub_dir"
    fi

    # CSV文件命名格式：{RepositoryName}{_SubDirectory}__batch_{批次号}_{时间戳}_{唯一ID}.csv
    local filename="${safe_repo_name}${sub_dir_part}__batch_${CURRENT_BATCH_NUMBER}_${timestamp}_${unique_id}.csv"
    local full_path="$OUTPUT_DIRECTORY/$filename"

    # CSV出力
    {
        echo "Path,SizeMB,SizeBytes,LastModified"
        for item in "${data_ref[@]}"; do
            echo "$item"
        done
    } > "$full_path"

    echo "Batch CSV output completed: $full_path (${#data_ref[@]} records)"

    if [[ "$VERBOSE_LOGGING" == "true" ]]; then
        echo "  File details: $filename"
        echo "  Repository: $repository_name"
        if [[ -n "$sub_directory" ]]; then
            echo "  Sub directory: $sub_directory"
        fi
    fi

    ((CURRENT_BATCH_NUMBER++))

    # 配列をクリア
    data_ref=()
}

# ファイル一覧取得
get_svn_file_list() {
    local repository_path="$1"
    local repository_root="$2"
    local sub_path="$3"
    local repository_name="$4"
    local sub_directory="${5:-}"
    
    echo "File list retrieval from: $repository_path"
    
    # UTF-8エンコーディング設定
    export LC_ALL=en_US.UTF-8
    export LANG=en_US.UTF-8
    
    # 方法1: svn list --xml
    if [[ "$METHOD" == "svnlist" || "$METHOD" == "auto" ]]; then
        local file_url
        if [[ -n "$sub_path" ]]; then
            # repository_rootパスをクリーンアップ、余分なスラッシュを削除
            local clean_root="${repository_root//\\//}"  # バックスラッシュをスラッシュに置換
            clean_root="${clean_root#/}"                 # 先頭のスラッシュを削除
            clean_root="${clean_root#/}"                 # 2番目のスラッシュも削除（もしあれば）

            file_url="file:///$clean_root/$sub_path"
            echo "Method 1: Using 'svn list --xml' with subpath (most efficient)"
            echo "Repository root: $repository_root"
            echo "Sub path: $sub_path"
        else
            # repository_pathパスをクリーンアップ、余分なスラッシュを削除
            local clean_path="${repository_path//\\//}"
            clean_path="${clean_path#/}"
            clean_path="${clean_path#/}"

            file_url="file:///$clean_path"
            echo "Method 1: Using 'svn list --xml' (most efficient)"
        fi
        
        local list_cmd="svn list -R --xml \"$file_url\" --non-interactive"
        if [[ "$VERBOSE_LOGGING" == "true" ]]; then
            echo "Command: $list_cmd"
        fi

        local temp_xml_file
        temp_xml_file=$(mktemp)

        # SVN listコマンドを実行してエラーをキャプチャ
        local svn_exit_code=0
        echo "Executing SVN command: $list_cmd"
        
        # SVNコマンドを実行して結果とエラーの両方をキャプチャ
        eval "$list_cmd" > "$temp_xml_file" 2>"$temp_xml_file.err"
        svn_exit_code=$?
        
        # 終了コードが0以外の場合はエラー
        if [[ $svn_exit_code -ne 0 ]]; then
            echo "Error: SVN command failed with exit code: $svn_exit_code"
            if [[ -s "$temp_xml_file.err" ]]; then
                echo "Error output:"
                head -n 5 "$temp_xml_file.err"
            fi
            rm -f "$temp_xml_file" "$temp_xml_file.err"
            return 1
        fi
        
        # 出力ファイルが存在し、空でないことを確認
        if [[ -s "$temp_xml_file" ]]; then
            # 出力の最初の行をチェックして、エラーメッセージでないことを確認
            local first_line
            first_line=$(head -n 1 "$temp_xml_file" 2>/dev/null || echo "")
            
            if [[ "$first_line" =~ ^Usage ]] || [[ "$first_line" =~ ^svn: ]] || [[ "$first_line" =~ ^xmllint: ]]; then
                echo "Error: SVN command output contains error message instead of XML:"
                head -n 3 "$temp_xml_file"
                rm -f "$temp_xml_file" "$temp_xml_file.err"
                return 1
            fi
            
            # XMLの妥当性をチェック
            if ! xmllint --noout "$temp_xml_file" 2>/dev/null; then
                echo "Error: SVN command produced invalid XML output"
                echo "First few lines of output:"
                head -n 5 "$temp_xml_file"
                rm -f "$temp_xml_file" "$temp_xml_file.err"
                return 1
            fi

            # echo -e "\nDebug: Contents of temp_xml_file:111"
            # cat "$temp_xml_file"
            # echo -e "\n--- End of file ---"
            
            if process_svn_list_xml_result "$temp_xml_file" "$repository_name" "$sub_path" "$sub_directory"; then
                rm -f "$temp_xml_file" "$temp_xml_file.err"
                return 0
            fi
        else
            echo "Warning: SVN command succeeded but produced no output"
        fi

        rm -f "$temp_xml_file" "$temp_xml_file.err"
        echo "Warning: SVN list method failed, trying alternative method..."
    fi
    
    # 方法2: svnlook
    if [[ "$METHOD" == "svnlook" || "$METHOD" == "auto" ]]; then
        echo "Method 2: Using 'svnlook' commands"
        get_svn_file_list_using_svnlook "$repository_path" "$repository_root" "$sub_path" "$repository_name" "$sub_directory"
        return $?
    fi
    
    echo "Error: All methods failed to retrieve file list"
    return 1
}

# 統計表示
show_statistics() {
    local repository_path="$1"
    
    echo ""
    echo "=== High Performance SVN File List Results ==="
    echo "Repository: $repository_path"
    echo "Total files: $TOTAL_FILES"
    
    local total_mb
    total_mb=$(echo "scale=2; $TOTAL_SIZE_BYTES / 1048576" | bc)
    # 小数点前に0を強制表示する
    if [[ "$total_mb" =~ ^\. ]]; then
        total_mb="0$total_mb"
    fi
    echo "Total size: $total_mb MB"
}

# 単一リポジトリ処理
process_single_repository() {
    local repository_path="$1"
    local repository_root="$2"
    local sub_path="$3"
    local repository_name="$4"
    local sub_directory="${5:-}"

    echo ""
    echo "=== Processing started: $repository_path ==="

    # 再開機能：処理済みリポジトリをスキップ
    if [[ -n "$repository_name" ]] && test_repository_processed "$repository_name"; then
        return 0
    fi

    # リポジトリパスの検証
    local path_to_check="${repository_root:-$repository_path}"
    if [[ ! -d "$path_to_check" ]]; then
        echo "Error: Repository path does not exist: $path_to_check"
        return 1
    fi

    # 各リポジトリごとに統計変数をリセット
    local repo_total_files=0
    local repo_total_size_bytes=0

    local start_time
    start_time=$(date +%s)
    CURRENT_BATCH_NUMBER=1

    # グローバル統計変数を一時保存
    local global_total_files=$TOTAL_FILES
    local global_total_size_bytes=$TOTAL_SIZE_BYTES

    # このリポジトリ用に統計変数をリセット
    TOTAL_FILES=0
    TOTAL_SIZE_BYTES=0
    
    # メインのファイル一覧取得処理
    if [[ -n "$sub_path" ]]; then
        get_svn_file_list "$repository_path" "$repository_root" "$sub_path" "$repository_name" "$sub_directory"
    else
        get_svn_file_list "$repository_path" "" "" "$repository_name" "$sub_directory"
    fi
    
    local end_time
    end_time=$(date +%s)
    local duration=$((end_time - start_time))

    # このリポジトリの統計を保存
    repo_total_files=$TOTAL_FILES
    repo_total_size_bytes=$TOTAL_SIZE_BYTES

    # 統計情報表示
    show_statistics "$repository_path"

    local hours=$((duration / 3600))
    local minutes=$(((duration % 3600) / 60))
    local seconds=$((duration % 60))
    printf "Processing time: %02d:%02d:%02d\n" $hours $minutes $seconds

    # グローバル統計に加算
    TOTAL_FILES=$((global_total_files + repo_total_files))
    TOTAL_SIZE_BYTES=$((global_total_size_bytes + repo_total_size_bytes))

    PROCESSED_REPOSITORIES+=("$repository_name")
}

# メイン実行関数
main() {
    echo "=== SVN File List Extraction ==="
    echo "Method: $METHOD"
    echo "Batch size: $BATCH_SIZE files per CSV"
    echo "Output directory: $OUTPUT_DIRECTORY"
    
    if [[ "$VERBOSE_LOGGING" == "true" ]]; then
        echo "Verbose logging: Enabled"
    fi
    
    if [[ "$RESUME_MODE" == "true" ]]; then
        echo "Resume mode: Enabled (will skip already processed repositories)"
    fi
    
    # 依存関係をチェック
    check_dependencies

    if [[ ! -d "$OUTPUT_DIRECTORY" ]]; then
        mkdir -p "$OUTPUT_DIRECTORY"
        echo "Created output directory: $OUTPUT_DIRECTORY"
    fi

    local overall_start_time
    overall_start_time=$(date +%s)
    echo "Processing start time: $(date '+%Y-%m-%d %H:%M:%S')"

    # 設定ファイルから読み込み
    if [[ -f "$CONFIG_FILE" ]]; then
        read_configuration_file
    else
        echo "Error: Please ensure ConfigFile exists: $CONFIG_FILE"
        exit 1
    fi

    echo "Processing targets: ${#REPOSITORIES_TO_PROCESS[@]} repositories"

    # 各リポジトリの処理
    local processed_count=0
    for repo_info in "${REPOSITORIES_TO_PROCESS[@]}"; do
        ((processed_count++))
        echo ""
        echo "[$processed_count/${#REPOSITORIES_TO_PROCESS[@]}] Processing repository..."

        local start_time
        start_time=$(date +%s)
        echo "Start time: $(date '+%Y-%m-%d %H:%M:%S')"

        # リポジトリ情報を分割（区切り文字|を使用）
        IFS='|' read -r repo_path repo_root repo_sub_path repo_name repo_sub_directory <<< "$repo_info"

        # リポジトリ情報に基づいて処理
        if [[ -n "$repo_sub_path" ]]; then
            process_single_repository "$repo_path" "$repo_root" "$repo_sub_path" "$repo_name" "$repo_sub_directory"
        else
            process_single_repository "$repo_path" "" "" "$repo_name" "$repo_sub_directory"
        fi

        local end_time
        end_time=$(date +%s)
        echo "End time: $(date '+%Y-%m-%d %H:%M:%S')"

        local duration=$((end_time - start_time))
        local hours=$((duration / 3600))
        local minutes=$(((duration % 3600) / 60))
        local seconds=$((duration % 60))
        printf "Duration: %02d:%02d:%02d\n" $hours $minutes $seconds
    done
    
    # 全体統計の表示
    local overall_end_time
    overall_end_time=$(date +%s)
    local overall_duration=$((overall_end_time - overall_start_time))

    echo ""
    echo "=== Overall Processing Statistics ==="
    echo "Processed repositories: ${#REPOSITORIES_TO_PROCESS[@]}"
    echo "Total files extracted: $TOTAL_FILES"

    local total_mb total_gb
    total_mb=$(echo "scale=2; $TOTAL_SIZE_BYTES / 1048576" | bc)
    total_gb=$(echo "scale=3; $TOTAL_SIZE_BYTES / 1073741824" | bc)
    # 小数点まで0が表示
    if [[ "$total_mb" =~ ^\. ]]; then
        total_mb="0$total_mb"
    fi
    if [[ "$total_gb" =~ ^\. ]]; then
        total_gb="0$total_gb"
    fi
    echo "Total size: $total_mb MB ($total_gb GB)"

    echo "Processing start time: $(date -d "@$overall_start_time" '+%Y-%m-%d %H:%M:%S')"
    echo "Processing end time: $(date -d "@$overall_end_time" '+%Y-%m-%d %H:%M:%S')"

    local hours=$((overall_duration / 3600))
    local minutes=$(((overall_duration % 3600) / 60))
    local seconds=$((overall_duration % 60))
    printf "Overall processing time: %02d:%02d:%02d\n" $hours $minutes $seconds

    echo ""
    echo "SVN file list extraction completed successfully"
    echo "CSV files are saved in: $OUTPUT_DIRECTORY"
}

# シグナルハンドラー
cleanup_on_exit() {
    echo ""
    echo "スクリプト実行が中断されました"
    exit 1
}

# シグナルハンドラーを設定
trap cleanup_on_exit INT TERM

# スクリプト実行
main "$@"
