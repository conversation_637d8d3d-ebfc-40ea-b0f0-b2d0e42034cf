package com.ms.bp.shared.common;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.exception.Message;
import com.ms.bp.shared.common.exception.ServiceException;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

/**
 * 共通レスポンス
 *
 * @param <T> データの型
 */
@Setter
@Getter
public class CommonResult<T> implements Serializable {

    // Getters & Setters
    /**
     * エラーコード
     *
     * @see Message#getCode()
     */
    private Integer code;
    /**
     * 返却データ
     */
    private T data;
    /**
     * エラーメッセージ、ユーザーが読める形式
     *
     * @see Message#getMsg()
     */
    private String msg;

    public static <T> CommonResult<T> error(Integer code, String message) {
        CommonResult<T> result = new CommonResult<>();
        result.code = code;
        result.msg = message;
        return result;
    }

    public static <T> CommonResult<T> error(Message message) {
        return error(message.getCode(), message.getMsg());
    }

    public static <T> CommonResult<T> success(T data) {
        CommonResult<T> result = new CommonResult<>();
        result.code = GlobalMessageConstants.SUCCESS.getCode();
        result.data = data;
        result.msg = "";
        return result;
    }

    public static boolean isSuccess(Integer code) {
        return Objects.equals(code, GlobalMessageConstants.SUCCESS.getCode());
    }

    @JsonIgnore // Jackson シリアル化を避ける
    public boolean isSuccess() {
        return isSuccess(code);
    }

    @JsonIgnore // Jackson シリアル化を避ける
    public boolean isError() {
        return !isSuccess();
    }

    // ========= 例外システムとの統合 =========
    public static <T> CommonResult<T> error(ServiceException serviceException) {
        return error(serviceException.getCode(), serviceException.getMessage());
    }

}