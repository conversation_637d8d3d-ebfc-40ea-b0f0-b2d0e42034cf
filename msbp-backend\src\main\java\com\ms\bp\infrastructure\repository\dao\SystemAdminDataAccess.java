package com.ms.bp.infrastructure.repository.dao;

import com.ms.bp.domain.admin.model.SystemAdminInfo;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.List;
import java.util.Optional;

/**
 * システム管理者マスタデータアクセス実装
 * JDK21のText Blocks、Optional等を活用した最適化実装
 */
public class SystemAdminDataAccess {
    private static final Logger logger = LoggerFactory.getLogger(SystemAdminDataAccess.class);

    private final JdbcTemplate jdbcTemplate;

    public SystemAdminDataAccess(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 指定社員がシステム管理者かどうかを確認
     * JDK21のText Blocksを活用したSQL構築
     *
     * @param shainCode 社員コード
     * @param systemOperationCompanyCode システム運用企業コード
     * @return システム管理者の場合true
     * @throws SQLException データベースアクセスエラー
     */
    public boolean isValidSystemAdmin(String shainCode, String systemOperationCompanyCode) throws SQLException {
        // JDK21: Text BlocksでCOUNTクエリを構築（存在確認のみ）
        var sql = """
            SELECT COUNT(1) as admin_count
            FROM M_SYSTM_KANRISHA
            WHERE SHAIN_CODE = ?
            AND SYSTM_UNYO_KIGYO_CODE = ?
            AND KYOKA_FLAG = '0'  -- 使用許可のみ
            """;

        logger.debug("システム管理者確認SQL実行: shainCode={}, systemOperationCompanyCode={}", 
                    shainCode, systemOperationCompanyCode);

        var params = new Object[]{shainCode, systemOperationCompanyCode};

        var results = jdbcTemplate.query(sql, params, (rs) -> rs.getInt("admin_count"));
        
        var adminCount = results.isEmpty() ? 0 : results.getFirst();
        var isAdmin = adminCount > 0;

        // JDK21: Switch Expressionでログ出力
        var logMessage = switch (adminCount) {
            case 0 -> STR."社員コード\{shainCode}はシステム管理者ではありません";
            case 1 -> STR."社員コード\{shainCode}は有効なシステム管理者です";
            default -> {
                logger.warn("社員コード{}に対して複数のシステム管理者レコードが見つかりました: {}件", 
                           shainCode, adminCount);
                yield STR."社員コード\{shainCode}に複数のレコードが存在します（管理者として処理）";
            }
        };
        
        logger.debug("システム管理者確認結果: {}", logMessage);
        
        return isAdmin;
    }
}