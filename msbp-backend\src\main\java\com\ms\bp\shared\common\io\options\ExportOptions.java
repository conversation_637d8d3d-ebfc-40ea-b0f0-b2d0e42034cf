package com.ms.bp.shared.common.io.options;

import com.ms.bp.shared.common.io.model.FileFormat;
import lombok.Builder;
import lombok.Getter;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * エクスポートオプションクラス
 * Lombokの@Builderを使用してBuilderパターンを実装
 */
@Builder
@Getter
public class ExportOptions {
    /**
     * ファイル形式
     */
    @Builder.Default
    private final FileFormat format = FileFormat.CSV;

    /**
     * 区切り文字
     */
    @Builder.Default
    private final String delimiter = ",";

    /**
     * ヘッダー行を含めるかどうか
     */
    @Builder.Default
    private final boolean includeHeader = true;

    /**
     * 列のリスト
     */
    private final List<String> columns;

    /**
     * エンティティマッピング関数
     */
    private final Function<Object, Map<String, Object>> entityMapper;

    /**
     * バッチサイズ
     */
    @Builder.Default
    private final int batchSize = 1000;

    /**
     * クエリタイムアウト
     */
    @Builder.Default
    private final String queryTimeout = "60s";

    /**
     * 合計レコード数を計算するかどうか
     */
    @Builder.Default
    private final boolean countTotal = true;

    /**
     * スレッド数
     */
    private final int threadCount;

    /**
     * フィールドマッピング設定（Java字段名 -> CSV header）
     */
    private final Map<String, String> fieldHeaderMapping;

    /**
     * フィールドマッピングを有効にするかどうか
     */
    @Builder.Default
    private final boolean enableFieldMapping = false;

    /**
     * ローカル一時ファイル名
     * 指定されない場合はシステムが自動生成
     */
    private final String fileName;
}