---
slug: stepping-into-the-story
title: Stepping Into the Story of GPT Researcher
authors: [elishakay]
tags: [ai, gpt-researcher, prompts, dreams, community]
image: https://github.com/user-attachments/assets/f6e8a6b5-12f8-4faa-ae99-6a2fbaf23cc1
---
![GPTR reflecting ourselves](https://github.com/user-attachments/assets/f6e8a6b5-12f8-4faa-ae99-6a2fbaf23cc1)

## The Barnes & Noble Dream

As a teenager, I remember stepping into Barnes & Noble, the scent of fresh pages filling the air, my fingers tracing the spines of books that had shaped minds and captured hearts. I'd whisper to myself: One day, my name will be here.

To me, books weren't just stories—they were reflections of the human experience, ways for people to see themselves more clearly. <PERSON> once said, “The purpose of art is to hold a mirror up to nature.” That idea stuck with me. Art, writing, and storytelling weren't just about entertainment; they were about understanding ourselves in new ways.

But the world changed. The bookstores faded, attention shifted, and the novel—once the pinnacle of deep thought and reflection—gave way to new forms of engagement. The long, immersive experience of reading was replaced with something more dynamic, more interactive.

## The Journey into Coding: A Simba Moment

About 9 years ago, [much like <PERSON><PERSON> in The Lion King](https://open.spotify.com/track/3BUT32qmBXmlqp3EJkgRfp?si=0935ef6eedf247ed), I embarked on a new journey filled with doubt and uncertainty. Leaving my known world of writing, I stepped into the unknown realm of coding. It was a foreign language at first—endless lines of syntax, debugging errors that made no sense, and moments of frustration where I felt like an imposter in a world of developers.

The journey was tough—I struggled to find my place, faced canceled contracts, and got my butt handed to me more times than I could count. Every rejection, every missed opportunity made me question if I had taken the wrong path. Maybe I wasn't meant to build—maybe I was meant to stay in the world of stories.

Even when I finally landed a job at Fiverr, working with JavaScript, MySQL, HTML, and CSS, I still felt like I had abandoned my identity as a writer.

## Discovering GPT Researcher

One night, about a year ago, deep into a rabbit hole of AI research, I stumbled upon GPT Researcher. The concept struck me instantly—AI wasn't just a tool; it was a means of expanding human knowledge, refining our questions, and reshaping how we approach research itself.

I reached out to Assaf, not expecting much. But instead of a polite acknowledgment, he welcomed me in. That moment—seeing my first commit merged—felt like an echo of my old dream. Only this time, I wasn't just writing stories. I was building something that helped others uncover their own.

## The Wicked Witch of the Researcher's Mirror

Around that time, I found myself repeatedly asking GPT Researcher the same question:

"Who is Elisha Kramer?"

At first, it was like the Magic Mirror in Snow White, responding with something generic like, "Elisha Kramer is a software engineer with experience in web development." It pulled information from my LinkedIn, GitHub, and Udemy profiles, painting a picture of who I was professionally. But then, things got weird.

I made more commits to GPT Researcher. More contributions. And as I coded, I asked a different question.

"Who is ElishaKay on Github?"

As time went on, the answer changed since the Researcher was pulling new sources fresh off web search results.

"ElishaKay is an active open source contributor with multiple repositories and over 500 commits in the past year."

Holy Shnikes! It was learning. Another commit. Another feature. Another line of documentation. Time to get more specific.

"Who is ElishaKay of gpt-researcher?"

"ElishaKay is a core contributor of GPT Researcher, improving research workflows and enhancing AI retrieval through significant code and documentation contributions."

Now we were talking. But I wasn't done. Like the Wicked Witch, I kept coming back. More commits. More improvements. More features.

Until finally, I asked:

"Tell me about gpt-researcher and tips to improve it"

And GPT Researcher looked back at me and said:

"GPTR is a thriving open-source community. The best path forward is to continue investing in that community - through code contributions, documentation improvements, and helping new contributors get started. The project's strength lies in its collaborative nature."

And that's when I knew—I wasn't just using GPT Researcher. I was becoming part of its story.

## AI as a mirror of ourselves

This evolving feedback helped me frame my own self-narrative. GPT Researcher wasn't just reflecting what was already known—it was pulling in context from both my work and the broader internet.

It was reflecting back my own journey, refining it with each step, blurring the illusion of a fixed identity, and embracing an evolving one.

Every query, every commit, every improvement shaped the tool—and in turn, it shaped me.

## Building as a Community

GPT Researcher isn't just a tool. It's a reflection of the open-source spirit, a living, evolving ecosystem where knowledge isn't static but constantly refined. It isn't just answering questions; it's engaging in a dialogue, shaping and reshaping narratives based on the latest contributions, research, and discoveries.
It isn't just about me anymore. It's about us.
A network of 138 contributors. An open-source project watched by 20,000 stars. A collective movement pushing the boundaries of AI-driven research.

Every researcher, every developer, every curious mind who refines their questions, contributes a feature, or engages with the tool is part of something bigger. AI isn't just some black box spitting out answers—it's a tool that helps us refine our own thinking, challenge assumptions, and expand our understanding.
It's an iterative process, just like life itself.
The more context we provide, the better the insights we get. The more we engage, the more it reflects back not just who we were but who we are becoming.

## A Story Still Being Written

So while I once dreamed of seeing my name on a book spine in Barnes & Noble, I now see something even greater.
My words aren't bound to a single book—they live within every line of code, every contribution, every researcher refining their questions.
We are not just users. We are builders.
And this isn't just my story.
It's our story.
And it's still being written.