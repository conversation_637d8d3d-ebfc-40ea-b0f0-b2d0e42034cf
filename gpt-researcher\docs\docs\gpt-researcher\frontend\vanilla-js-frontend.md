# Vanilla JS Frontend

The VanillaJS frontend is a lightweight solution leveraging FastAPI to serve static files.

### Demo
<iframe height="400" width="700" src="https://github.com/assafelovic/gpt-researcher/assets/13554167/dd6cf08f-b31e-40c6-9907-1915f52a7110" frameborder="0" allow="autoplay; encrypted-media" allowfullscreen></iframe>

#### Prerequisites
- Python 3.11+
- pip

#### Setup and Running

1. Install required packages:
   ```
   pip install -r requirements.txt
   ```

2. Start the server:
   ```
   python -m uvicorn main:app
   ```

3. Access at `http://localhost:8000`
