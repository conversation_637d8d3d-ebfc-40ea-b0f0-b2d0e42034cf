"""
RAGFlow + GPT Researcher Integration Examples
Complete examples showing how to use RAGFlow as a data source for GPT Researcher
"""

import asyncio
import os
from gpt_researcher import GP<PERSON>esearcher
from gpt_researcher.utils.enum import ReportType, ReportSource
from config.ragflow_config import RAGFlowConfig


async def basic_ragflow_research():
    """Basic example using RAGFlow as the primary data source"""
    
    # Configure RAGFlow settings
    config = RAGFlowConfig()
    
    if not config.validate_ragflow_config():
        print("❌ RAGFlow configuration invalid. Please check environment variables.")
        return
    
    # Create researcher with RAGFlow integration
    researcher = GPTResearcher(
        query="What are the latest developments in artificial intelligence?",
        report_type=ReportType.ResearchReport.value,
        report_source="ragflow",  # Use RAGFlow as source
        headers=config.get_ragflow_headers(),
        verbose=True
    )
    
    print("🔍 Starting research with RAGFlow...")
    
    # Conduct research
    research_result = await researcher.conduct_research()
    print(f"📊 Research completed. Found {len(research_result)} sources.")
    
    # Generate report
    report = await researcher.write_report()
    print("📝 Report generated successfully!")
    print("\n" + "="*50)
    print(report[:500] + "...")
    print("="*50)
    
    return report


async def hybrid_ragflow_web_research():
    """Example combining RAGFlow data with web search"""
    
    config = RAGFlowConfig()
    
    # Custom hybrid research class
    class HybridRAGFlowResearcher(GPTResearcher):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.ragflow_config = RAGFlowConfig()
        
        async def conduct_research(self):
            """Conduct hybrid research using both RAGFlow and web sources"""
            print("🔍 Starting hybrid research...")
            
            # 1. Search RAGFlow first
            from gpt_researcher.retrievers.ragflow import RAGFlowRetriever
            ragflow_retriever = RAGFlowRetriever(
                query=self.query,
                headers=self.ragflow_config.get_ragflow_headers()
            )
            ragflow_results = ragflow_retriever.search(max_results=10)
            print(f"📚 Found {len(ragflow_results)} results from RAGFlow")
            
            # 2. Supplement with web search if needed
            if len(ragflow_results) < 5:
                print("🌐 Supplementing with web search...")
                self.report_source = ReportSource.Web.value
                web_results = await super().conduct_research()
                
                # Combine results (simplified approach)
                combined_results = ragflow_results + web_results[:5]
                return combined_results
            
            return ragflow_results
    
    # Use hybrid researcher
    researcher = HybridRAGFlowResearcher(
        query="How does machine learning impact business productivity?",
        report_type=ReportType.ResearchReport.value,
        headers=config.get_ragflow_headers(),
        verbose=True
    )
    
    research_result = await researcher.conduct_research()
    report = await researcher.write_report()
    
    print("🎯 Hybrid research completed!")
    print(f"📊 Total sources: {len(research_result)}")
    return report


async def domain_specific_ragflow_research():
    """Example for domain-specific research using specific datasets"""
    
    # Configure for specific domain (e.g., medical research)
    os.environ["RAGFLOW_DATASET_IDS"] = "medical_papers_dataset,clinical_trials_dataset"
    os.environ["RAGFLOW_SIMILARITY_THRESHOLD"] = "0.15"  # Lower threshold for more results
    os.environ["RAGFLOW_TOP_K"] = "50"  # More comprehensive search
    
    config = RAGFlowConfig()
    
    researcher = GPTResearcher(
        query="What are the latest treatments for cardiovascular disease?",
        report_type="medical_research",  # Custom report type
        report_source="ragflow",
        headers=config.get_ragflow_headers(),
        verbose=True
    )
    
    print("🏥 Starting medical research with RAGFlow...")
    
    research_result = await researcher.conduct_research()
    report = await researcher.write_report()
    
    print("✅ Medical research completed!")
    return report


async def custom_business_analysis():
    """Example for business analysis using internal documents"""
    
    # Configure for business documents
    os.environ["RAGFLOW_DATASET_IDS"] = "company_reports,market_analysis,competitor_intel"
    os.environ["RAGFLOW_VECTOR_WEIGHT"] = "0.4"  # Higher weight for semantic similarity
    
    config = RAGFlowConfig()
    
    researcher = GPTResearcher(
        query="Analyze our company's competitive position in the AI market",
        report_type="business_analysis",
        report_source="ragflow",
        headers=config.get_ragflow_headers(),
        tone="analytical",
        verbose=True
    )
    
    print("📈 Starting business analysis with RAGFlow...")
    
    research_result = await researcher.conduct_research()
    report = await researcher.write_report()
    
    print("💼 Business analysis completed!")
    return report


def setup_environment():
    """Setup example environment variables"""
    print("🔧 Setting up RAGFlow environment...")
    
    # Example configuration (replace with your actual values)
    example_config = {
        "RAGFLOW_BASE_URL": "http://localhost:9380",  # Your RAGFlow instance URL
        "RAGFLOW_API_KEY": "your_api_key_here",
        "RAGFLOW_DATASET_IDS": "dataset1,dataset2",
        # Optional document-specific search
        # "RAGFLOW_DOCUMENT_IDS": "doc1,doc2",
        
        # Search parameters
        "RAGFLOW_SIMILARITY_THRESHOLD": "0.2",
        "RAGFLOW_VECTOR_WEIGHT": "0.3",
        "RAGFLOW_TOP_K": "20",
        "RAGFLOW_PAGE_SIZE": "30",
        
        # GPT Researcher settings
        "OPENAI_API_KEY": "your_openai_key",
        "TAVILY_API_KEY": "your_tavily_key",  # For hybrid search
    }
    
    print("Please set these environment variables:")
    for key, value in example_config.items():
        print(f"export {key}='{value}'")
    
    print("\n🔗 Or create a .env file with these values")


async def main():
    """Run all examples"""
    print("🚀 RAGFlow + GPT Researcher Integration Examples")
    print("=" * 60)
    
    # Check if environment is configured
    if not os.getenv("RAGFLOW_API_KEY") or not os.getenv("RAGFLOW_BASE_URL"):
        print("⚠️  Environment not configured. Running setup...")
        setup_environment()
        return
    
    try:
        # Run examples
        print("\n1️⃣  Basic RAGFlow Research")
        await basic_ragflow_research()
        
        print("\n2️⃣  Hybrid RAGFlow + Web Research")
        await hybrid_ragflow_web_research()
        
        print("\n3️⃣  Domain-Specific Research")
        await domain_specific_ragflow_research()
        
        print("\n4️⃣  Business Analysis")
        await custom_business_analysis()
        
    except Exception as e:
        print(f"❌ Error running examples: {str(e)}")
        print("Please check your RAGFlow configuration and ensure the service is running.")


if __name__ == "__main__":
    asyncio.run(main())