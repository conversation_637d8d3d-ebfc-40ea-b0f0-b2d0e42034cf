package com.ms.bp.interfaces.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.Date;

/**
 * エクスポート履歴レスポンスDTO
 * エクスポート履歴取得API専用のレスポンス形式
 * インターフェース層のデータ転送オブジェクト
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExportHistoryResponse {

    /**
     * 履歴番号
     * T_DWNLD_RRK.RRK_BANGO
     */
    private Long historyNumber;

    /**
     * ファイル種別
     * ファイル種別コードから日本語名に変換済み
     * 例：1→次年度計画マスタ、2→見通し・計画_採算管理単位C別＜本社＞
     */
    private String fileType;

    /**
     * エリア
     * エリアコードから日本語名に変換済み
     * 複数エリアの場合はカンマ区切り
     */
    private String area;

    /**
     * 本社場所区分
     * 0:本社、1:場所
     */
    private String headquartersLocationDivision;

    /**
     * データ区分
     * 0:移管前（当年度組織）、1:移管後（次年度組織）
     * 複数データ区分の場合はカンマ区切り
     */
    private String dataDivision;

    /**
     * ファイル作成開始日時
     * T_DWNLD_RRK.FILE_SKS_KSH_NCHJ
     */
    private String fileCreationStartDateTime;

    /**
     * ファイル作成完了日時
     * T_DWNLD_RRK.FILE_SKS_KNR_NCHJ
     */
    private String fileCreationCompletionDateTime;

    /**
     * ステータス
     * ステータスコードから日本語名に変換済み
     * 0:処理中、1:完了、2:一部失敗、3:失敗、4:システムエラー
     */
    private String status;
    /** カテゴリー区分 */
    private String ctgryKubun;
    /**
     * ZIPサイズ
     * T_DWNLD_RRK.ZIP_FILE_SIZE
     */
    private String zipSize;

    /**
     * ZIPファイル名
     * T_DWNLD_RRK.ZIP_FILE_MEI
     */
    private String zipFileName;
}
