package com.ms.bp.shared.util;

import java.text.MessageFormat;
import java.util.Locale;

/**
 * メッセージフォーマッターユーティリティクラス
 * パラメータ化されたエラーメッセージの処理を担当
 */
public class MessageFormatter {

    /**
     * デフォルトロケール（日本語）
     */
    private static final Locale DEFAULT_LOCALE = Locale.JAPAN;

    /**
     * メッセージテンプレートをパラメータで置換してフォーマット
     * 
     * @param template メッセージテンプレート（例：「パラメータ：{0}、エラー内容：{1}」）
     * @param parameters 置換パラメータ
     * @return フォーマット済みメッセージ
     */
    public static String format(String template, Object... parameters) {
        if (template == null) {
            return "";
        }
        
        if (parameters == null || parameters.length == 0) {
            return template;
        }
        
        try {
            // MessageFormatを使用してパラメータを置換
            MessageFormat formatter = new MessageFormat(template, DEFAULT_LOCALE);
            return formatter.format(parameters);
        } catch (Exception e) {
            // フォーマットエラーの場合は元のテンプレートを返す
            return template + " [フォーマットエラー: " + e.getMessage() + "]";
        }
    }

    /**
     * 単一パラメータ用の便利メソッド
     * 
     * @param template メッセージテンプレート
     * @param parameter 置換パラメータ
     * @return フォーマット済みメッセージ
     */
    public static String format(String template, Object parameter) {
        return format(template, new Object[]{parameter});
    }

    /**
     * 2つのパラメータ用の便利メソッド
     * 
     * @param template メッセージテンプレート
     * @param param1 第1パラメータ
     * @param param2 第2パラメータ
     * @return フォーマット済みメッセージ
     */
    public static String format(String template, Object param1, Object param2) {
        return format(template, new Object[]{param1, param2});
    }

    /**
     * 3つのパラメータ用の便利メソッド
     * 
     * @param template メッセージテンプレート
     * @param param1 第1パラメータ
     * @param param2 第2パラメータ
     * @param param3 第3パラメータ
     * @return フォーマット済みメッセージ
     */
    public static String format(String template, Object param1, Object param2, Object param3) {
        return format(template, new Object[]{param1, param2, param3});
    }
}
