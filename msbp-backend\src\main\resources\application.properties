# Default configuration (Development environment)
# AWS configuration
aws.region=ap-northeast-1
aws.s3.bucket.name=ms-bp-files-dev-standard

# Database configuration
# Parameter Store settings (priority)
db.use.parameter.store=true
db.parameter.prefix=/ms-bp/dev/standard/db
# Local development settings (fallback when Parameter Store and Secrets Manager are not available)
#db.host=**************
#db.port=5432
#db.name=postgres
#db.username=postgres
#db.password=123456

db.host=127.0.0.1
db.port=13311
db.name=bpdatabase
db.username=bpadmin2025
db.password=qRrHKz5Lzz7Ko8lGyUaV

# SSH Tunnel configuration for connecting to AWS dev database
ssh.tunnel.enabled=true
ssh.tunnel.ssh.host=*************
ssh.tunnel.ssh.port=22
ssh.tunnel.ssh.username=ec2-user
ssh.tunnel.ssh.private.key.path=src/main/resources/ms-bp-key-ec2-dev.pem
ssh.tunnel.local.port=15432
ssh.tunnel.remote.host=ms-bp-db-dev-standard.cra8u4cw216c.ap-northeast-1.rds.amazonaws.com
ssh.tunnel.remote.port=5432
ssh.tunnel.connection.timeout=30000
ssh.tunnel.keep.alive.interval=60000

# JWT configuration
jwt.secret.key=business-plan-development

# Email configuration
email.default.from=<EMAIL>

# Frontend configuration
frontend.base.url=https://dev.example.com

# Logging configuration
aws.lambda.log.format=TEXT
aws.lambda.log.level=DEBUG
