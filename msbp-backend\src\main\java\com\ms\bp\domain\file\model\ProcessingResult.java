package com.ms.bp.domain.file.model;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;
import java.util.HashMap;

/**
 * ファイル処理結果値オブジェクト
 * インポート・エクスポート処理の結果を表現する
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessingResult {
    
    /**
     * 処理タイプ（IMPORT/EXPORT）
     */
    private String processingType;
    
    /**
     * データタイプ（USERS等）
     */
    private String dataType;
    
    /**
     * 処理ステータス（SUCCESS/FAILED/PARTIAL）
     */
    private String status;
    
    /**
     * 処理開始時刻
     */
    private Date startTime;
    
    /**
     * 処理終了時刻
     */
    private Date endTime;
    
    /**
     * 処理対象レコード数
     */
    private int totalRecords;
    
    /**
     * 成功レコード数
     */
    private int successRecords;
    
    /**
     * 失敗レコード数
     */
    private int failedRecords;
    
    /**
     * エラーメッセージ
     */
    private String errorMessage;
    
    /**
     * 処理統計情報
     */
    @Builder.Default
    private Map<String, Object> statistics = new HashMap<>();
    
    // ==================== ビジネスメソッド ====================
    
    /**
     * 処理が成功したかどうかを判定
     * @return 成功の場合true
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(status);
    }
    
    /**
     * 処理が失敗したかどうかを判定
     * @return 失敗の場合true
     */
    public boolean isFailed() {
        return "FAILED".equals(status);
    }
    
    /**
     * 部分的成功かどうかを判定
     * @return 部分的成功の場合true
     */
    public boolean isPartialSuccess() {
        return "PARTIAL".equals(status);
    }
    
    /**
     * 処理時間（ミリ秒）を取得
     * @return 処理時間、終了時刻が設定されていない場合は-1
     */
    public long getProcessingTimeMs() {
        if (startTime == null || endTime == null) {
            return -1;
        }
        return endTime.getTime() - startTime.getTime();
    }
    
    /**
     * 処理時間（秒）を取得
     * @return 処理時間（秒）
     */
    public double getProcessingTimeSeconds() {
        long timeMs = getProcessingTimeMs();
        return timeMs >= 0 ? timeMs / 1000.0 : -1;
    }
    
    /**
     * 成功率を取得
     * @return 成功率（0.0-1.0）
     */
    public double getSuccessRate() {
        if (totalRecords == 0) {
            return 0.0;
        }
        return (double) successRecords / totalRecords;
    }
    
    /**
     * 成功率（パーセント）を取得
     * @return 成功率（0-100）
     */
    public double getSuccessRatePercent() {
        return getSuccessRate() * 100.0;
    }
    
    /**
     * インポート処理結果かどうかを判定
     * @return インポート処理の場合true
     */
    public boolean isImportResult() {
        return "IMPORT".equals(processingType);
    }
    
    /**
     * エクスポート処理結果かどうかを判定
     * @return エクスポート処理の場合true
     */
    public boolean isExportResult() {
        return "EXPORT".equals(processingType);
    }
    
    /**
     * 統計情報を追加
     * @param key キー
     * @param value 値
     */
    public void addStatistic(String key, Object value) {
        if (statistics == null) {
            statistics = new HashMap<>();
        }
        statistics.put(key, value);
    }
    
    /**
     * 統計情報を取得
     * @param key キー
     * @return 値、存在しない場合はnull
     */
    public Object getStatistic(String key) {
        return statistics != null ? statistics.get(key) : null;
    }
    
    /**
     * 処理結果のサマリーを取得
     * @return サマリー文字列
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(processingType).append(" ").append(dataType);
        summary.append(" - ").append(status);
        summary.append(" (").append(successRecords).append("/").append(totalRecords).append(")");
        
        if (getProcessingTimeMs() >= 0) {
            summary.append(" ").append(String.format("%.2f", getProcessingTimeSeconds())).append("秒");
        }
        
        if (failedRecords > 0) {
            summary.append(" エラー: ").append(failedRecords).append("件");
        }
        
        return summary.toString();
    }
    
    /**
     * 処理完了をマーク
     * @param status 最終ステータス
     */
    public void markCompleted(String status) {
        this.status = status;
        this.endTime = new Date();
    }
    
    /**
     * 処理失敗をマーク
     * @param errorMessage エラーメッセージ
     */
    public void markFailed(String errorMessage) {
        this.status = "FAILED";
        this.errorMessage = errorMessage;
        this.endTime = new Date();
    }
    
    /**
     * インポート処理結果を作成するファクトリメソッド
     * @param dataType データタイプ
     * @return インポート処理結果
     */
    public static ProcessingResult createImportResult(String dataType) {
        return ProcessingResult.builder()
                .processingType("IMPORT")
                .dataType(dataType)
                .status("PROCESSING")
                .startTime(new Date())
                .totalRecords(0)
                .successRecords(0)
                .failedRecords(0)
                .statistics(new HashMap<>())
                .build();
    }
    
    /**
     * エクスポート処理結果を作成するファクトリメソッド
     * @param dataType データタイプ
     * @return エクスポート処理結果
     */
    public static ProcessingResult createExportResult(String dataType) {
        return ProcessingResult.builder()
                .processingType("EXPORT")
                .dataType(dataType)
                .status("PROCESSING")
                .startTime(new Date())
                .totalRecords(0)
                .successRecords(0)
                .failedRecords(0)
                .statistics(new HashMap<>())
                .build();
    }
}
