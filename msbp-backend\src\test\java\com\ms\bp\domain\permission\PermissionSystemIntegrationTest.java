package com.ms.bp.domain.permission;

import com.ms.bp.application.PermissionApplicationService;
import com.ms.bp.domain.permission.model.UserPermissionInfo;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.interfaces.dto.response.UserPermissionsResponse;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.util.TestDataManager;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 権限システム集成テスト
 * 権限システムの3張核心データ表（m_kengen、m_ken_rule、m_ken_person）に基づく
 * 完整な権限取得フローを検証する
 *
 * テストデータはExcelファイル（permission_system_test_data.xlsx）で管理し、
 * TestDataManagerを使用してデータベースに自動挿入・削除を行う
 *
 * Excelファイル構成：
 * - M_KENGEN シート: 権限マスタデータ
 * - M_KEN_RULE シート: 権限ルールマスタデータ
 * - M_KEN_PERSON シート: 個人権限テーブルデータ
 */
@DisplayName("権限システム集成テスト")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class PermissionSystemIntegrationTest {

    private static final Logger logger = LoggerFactory.getLogger(PermissionSystemIntegrationTest.class);

    private PermissionApplicationService permissionApplicationService;
    private TestDataManager testDataManager;
    private Map<String, List<Map<String, Object>>> insertedDataTracker;

    @BeforeEach
    void setUp() {
        logger.info("=== 権限システム集成テストセットアップ開始 ===");

        try {
            // AWS設定を環境変数で設定してリソース初期化エラーを回避
            System.setProperty("aws.region", "ap-northeast-1");

            // 被测试服务の初期化
            permissionApplicationService = new PermissionApplicationService();

            // 権限システム用テストデータ管理器の初期化（Excel文件を指定）
            testDataManager = new TestDataManager("permission_system_test_data.xlsx");

            // Excel からテストデータを読み込んでデータベースに挿入
            insertedDataTracker = testDataManager.insertAllTestData();

            logger.info("=== 権限システム集成テストセットアップ完了 ===");

        } catch (Exception e) {
            logger.error("権限システムテストセットアップエラー: {}", e.getMessage(), e);
            throw new RuntimeException("権限システムテストセットアップに失敗しました", e);
        }
    }

    @AfterEach
    void tearDown() {
        logger.info("=== 権限システム集成テストクリーンアップ開始 ===");

        if (insertedDataTracker != null) {
            logger.info("権限システムテストデータクリーンアップを実行します: {} テーブル", insertedDataTracker.size());
            testDataManager.deleteAllTestData(insertedDataTracker);
            logger.info("権限システムテストデータクリーンアップが完了しました");
        }

        logger.info("=== 権限システム集成テストクリーンアップ完了 ===");
    }

    /**
     * 権限システム正常フロー統合テスト
     * 共通権限と個人権限の統合、権限去重処理を含む完整なフローを検証
     */
    @Test
    @Order(1)
    @DisplayName("権限システム正常フロー_共通権限と個人権限統合テスト")
    void testPermissionSystem_正常フロー統合() {
        logger.info("=== 権限システム正常フロー統合テスト開始 ===");

        try {
            // Given: テスト用ユーザー情報を準備
            UserInfo testUserInfo = createTestUserInfo("115651", "100001", "12345", "0202");

            // When: ユーザー権限を取得（操作区分指定なし）
            UserPermissionsResponse response = permissionApplicationService.getUserPermissions(
                    testUserInfo, null);

            // Then: 結果を検証
            assertNotNull(response, "権限レスポンスが取得できること");
            assertNotNull(response.getPermissions(), "権限リストが設定されていること");

            List<UserPermissionsResponse.PermissionInfo> permissions = response.getPermissions();
            logger.info("取得された権限数: {}", permissions.size());

            // 期待される権限の検証
            verifyExpectedPermissions(permissions);

            // 権限去重処理の検証（本社権限優先）
            verifyPermissionDeduplication(permissions);

            logger.info("✅ 権限システム正常フロー統合テスト完了");

        } catch (Exception e) {
            logger.error("権限システム正常フローテストエラー: {}", e.getMessage(), e);
            fail("権限システム正常フローテストに失敗しました: " + e.getMessage());
        }
    }

    /**
     * ダウンロード権限フィルタリングテスト
     * 操作区分でダウンロード権限のみを取得する処理を検証
     */
    @Test
    @Order(2)
    @DisplayName("権限システム_ダウンロード権限フィルタリングテスト")
    void testPermissionSystem_ダウンロード権限フィルタリング() {
        logger.info("=== ダウンロード権限フィルタリングテスト開始 ===");

        try {
            // Given: テスト用ユーザー情報を準備
            UserInfo testUserInfo = createTestUserInfo("115651", "100001", "12345", "0202");

            // When: ダウンロード権限のみを取得
            UserPermissionsResponse response = permissionApplicationService.getUserPermissions(
                    testUserInfo, BusinessConstants.OPERATION_DOWNLOAD_CODE);

            // Then: ダウンロード権限のみが返されることを検証
            assertNotNull(response, "権限レスポンスが取得できること");
            List<UserPermissionsResponse.PermissionInfo> permissions = response.getPermissions();

            // 全ての権限がダウンロード権限であることを確認
            permissions.forEach(permission -> {
                assertThat(permission.getOperationDivision())
                        .isEqualTo(BusinessConstants.OPERATION_DOWNLOAD_CODE);
                logger.debug("ダウンロード権限確認: {}", permission.getPermissionCode());
            });

            logger.info("✅ ダウンロード権限フィルタリングテスト完了: {}件", permissions.size());

        } catch (Exception e) {
            logger.error("ダウンロード権限フィルタリングテストエラー: {}", e.getMessage(), e);
            fail("ダウンロード権限フィルタリングテストに失敗しました: " + e.getMessage());
        }
    }

    /**
     * アップロード権限フィルタリングテスト
     * 操作区分でアップロード権限のみを取得する処理を検証
     */
    @Test
    @Order(3)
    @DisplayName("権限システム_アップロード権限フィルタリングテスト")
    void testPermissionSystem_アップロード権限フィルタリング() {
        logger.info("=== アップロード権限フィルタリングテスト開始 ===");

        try {
            // Given: テスト用ユーザー情報を準備
            UserInfo testUserInfo = createTestUserInfo("115651", "100001", "12345", "0202");

            // When: アップロード権限のみを取得
            UserPermissionsResponse response = permissionApplicationService.getUserPermissions(
                    testUserInfo, BusinessConstants.OPERATION_UPLOAD_CODE);

            // Then: アップロード権限のみが返されることを検証
            assertNotNull(response, "権限レスポンスが取得できること");
            List<UserPermissionsResponse.PermissionInfo> permissions = response.getPermissions();

            // 全ての権限がアップロード権限であることを確認
            permissions.forEach(permission -> {
                assertThat(permission.getOperationDivision())
                        .isEqualTo(BusinessConstants.OPERATION_UPLOAD_CODE);
                logger.debug("アップロード権限確認: {}", permission.getPermissionCode());
            });

            logger.info("✅ アップロード権限フィルタリングテスト完了: {}件", permissions.size());

        } catch (Exception e) {
            logger.error("アップロード権限フィルタリングテストエラー: {}", e.getMessage(), e);
            fail("アップロード権限フィルタリングテストに失敗しました: " + e.getMessage());
        }
    }

    /**
     * 権限なしユーザーテスト
     * 権限が設定されていないユーザーの処理を検証
     */
    @Test
    @Order(4)
    @DisplayName("権限システム_権限なしユーザーテスト")
    void testPermissionSystem_権限なしユーザー() {
        logger.info("=== 権限なしユーザーテスト開始 ===");

        try {
            // Given: 権限が設定されていないユーザー情報を準備
            UserInfo noPermissionUser = createTestUserInfo("999999", "100001", "99999", "9999");

            // When: ユーザー権限を取得
            UserPermissionsResponse response = permissionApplicationService.getUserPermissions(
                    noPermissionUser, null);

            // Then: 空の権限リストが返されることを検証
            assertNotNull(response, "権限レスポンスが取得できること");
            List<UserPermissionsResponse.PermissionInfo> permissions = response.getPermissions();
            assertThat(permissions).isEmpty();

            logger.info("✅ 権限なしユーザーテスト完了: 権限数=0");

        } catch (Exception e) {
            logger.error("権限なしユーザーテストエラー: {}", e.getMessage(), e);
            fail("権限なしユーザーテストに失敗しました: " + e.getMessage());
        }
    }

    /**
     * 異なる企業コードユーザーテスト
     * 異なる企業コードのユーザーが適切に権限を取得できることを検証
     */
    @Test
    @Order(5)
    @DisplayName("権限システム_異なる企業コードユーザーテスト")
    void testPermissionSystem_異なる企業コードユーザー() {
        logger.info("=== 異なる企業コードユーザーテスト開始 ===");

        try {
            // Given: 異なる企業コードのユーザー情報を準備
            UserInfo differentCompanyUser = createTestUserInfo("115652", "100002", "12346", "0203");

            // When: ユーザー権限を取得
            UserPermissionsResponse response = permissionApplicationService.getUserPermissions(
                    differentCompanyUser, null);

            // Then: 企業コードに応じた権限が取得されることを検証
            assertNotNull(response, "権限レスポンスが取得できること");
            List<UserPermissionsResponse.PermissionInfo> permissions = response.getPermissions();

            // 企業コード100002用の権限が取得されることを確認
            logger.info("異なる企業コードユーザーの権限数: {}", permissions.size());

            logger.info("✅ 異なる企業コードユーザーテスト完了");

        } catch (Exception e) {
            logger.error("異なる企業コードユーザーテストエラー: {}", e.getMessage(), e);
            fail("異なる企業コードユーザーテストに失敗しました: " + e.getMessage());
        }
    }

    // ==================== プライベートメソッド ====================

    /**
     * 期待される権限の検証
     * 共通権限と個人権限の統合結果を詳細に検証
     */
    private void verifyExpectedPermissions(List<UserPermissionsResponse.PermissionInfo> permissions) {
        logger.info("期待される権限の詳細検証開始");

        // 最低限期待される権限数を確認
        assertThat(permissions).isNotEmpty();

        // 各権限の詳細検証
        for (UserPermissionsResponse.PermissionInfo permission : permissions) {
            // 権限コードが設定されていることを確認
            assertThat(permission.getPermissionCode()).isNotNull().isNotEmpty();

            // 操作区分が正しく設定されていることを確認
            assertThat(permission.getOperationDivision())
                    .isIn(BusinessConstants.OPERATION_DOWNLOAD_CODE, BusinessConstants.OPERATION_UPLOAD_CODE);

            // エリアパターンが正しく設定されていることを確認
            assertThat(permission.getAreaPattern())
                    .isIn(BusinessConstants.AFFILIATION_HEAD_OFFICE, BusinessConstants.AFFILIATION_AREA);

            // ファイル種別コードが設定されていることを確認
            assertThat(permission.getFileTypeCode()).isNotNull();

            logger.debug("権限検証完了: コード={}, 操作={}, エリア={}, ファイル種別={}",
                    permission.getPermissionCode(),
                    permission.getOperationDivision(),
                    permission.getAreaPattern(),
                    permission.getFileTypeCode());
        }

        logger.info("期待される権限の詳細検証完了: {}件", permissions.size());
    }

    /**
     * 権限去重処理の検証
     * 本社権限(H)とエリア権限(A)が共存する場合、エリア権限が削除されることを確認
     */
    private void verifyPermissionDeduplication(List<UserPermissionsResponse.PermissionInfo> permissions) {
        logger.info("権限去重処理検証開始");

        // 権限コードのパターンを分析
        for (UserPermissionsResponse.PermissionInfo permission : permissions) {
            String permissionCode = permission.getPermissionCode();

            if (permissionCode.length() >= 3) {
                char areaMarker = permissionCode.charAt(2);

                if (areaMarker == 'H') {
                    // 本社権限の場合、対応するエリア権限が存在しないことを確認
                    String correspondingAreaCode = permissionCode.substring(0, 2) + 'A' + permissionCode.substring(3);

                    boolean hasCorrespondingAreaPermission = permissions.stream()
                            .anyMatch(p -> correspondingAreaCode.equals(p.getPermissionCode()));

                    assertThat(hasCorrespondingAreaPermission)
                            .as("本社権限 %s に対応するエリア権限 %s が存在しないこと", permissionCode, correspondingAreaCode)
                            .isFalse();

                    logger.debug("権限去重確認: 本社権限 {} - 対応エリア権限なし ✓", permissionCode);
                }
            }
        }

        logger.info("権限去重処理検証完了");
    }

    /**
     * テスト用ユーザー情報を作成
     */
    private UserInfo createTestUserInfo(String shainCode, String systemOperationCompanyCode,
                                       String unitCode, String areaCode) {
        UserInfo userInfo = new UserInfo();
        userInfo.setShainCode(shainCode);
        userInfo.setSystemOperationCompanyCode(systemOperationCompanyCode);
        userInfo.setUnitCode(unitCode);
        userInfo.setAreaCode(areaCode);
        userInfo.setPositionCode("01");
        userInfo.setGroupCode("G001");
        return userInfo;
    }
}
