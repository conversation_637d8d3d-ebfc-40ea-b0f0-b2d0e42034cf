package com.ms.bp.domain.admin.repository;

import com.ms.bp.domain.admin.model.SystemAdminInfo;

import java.util.Optional;

/**
 * システム管理者マスタリポジトリ領域インターフェース
 * システム管理者情報のデータアクセスを抽象化し、領域層を基盤設備層から分離する
 */
public interface SystemAdminRepository {
    
    /**
     * 指定社員がシステム管理者かどうかを確認
     * 使用禁止区分が'0'のレコードが存在すればシステム管理者として判定
     * 
     * @param shainCode 社員コード
     * @param systemOperationCompanyCode システム運用企業コード
     * @return システム管理者の場合true
     */
    boolean isValidSystemAdmin(String shainCode, String systemOperationCompanyCode);
}