# Evaluation Results

This directory contains historical evaluation results for GPT-<PERSON>er using the SimpleQA methodology.

## Latest Results

### [SimpleQA Eval 100 Problems 2-22-25](./SimpleQA%20Eval%20100%20Problems%202-22-25.txt)

Evaluation run by [<PERSON> (kga245)](https://github.com/kga245)

**Summary:**
- Date: February 22, 2025
- Sample Size: 100 queries
- Success Rate: 100% (100/100 queries completed)

**Performance Metrics:**
- Accuracy: 92.9%
- F1 Score: 92.5%
- Answer Rate: 99%

**Response Distribution:**
- Correct: 92%
- Incorrect: 7%
- Not Attempted: 1%

**Cost Efficiency:**
- Total Cost: $9.60
- Average Cost per Query: $0.096

This evaluation demonstrates strong performance in factual accuracy while maintaining reasonable cost efficiency. The high answer rate (99%) and accuracy (92.9%) suggest that GPT-Researcher is effective at finding and reporting accurate information.

## Historical Context

These logs are maintained in version control to:
1. Track performance improvements over time
2. Provide benchmarks for future enhancements
3. Enable analysis of different configurations
4. Ensure transparency in our evaluation process

Each log file contains detailed information about:
- Individual query results
- Source citations
- Cost breakdowns
- Error analysis
- Aggregate metrics

## Running New Evaluations

To generate new evaluation logs, see the [main evaluation documentation](../README.md) for instructions on running evaluations with different configurations or sample sizes. 