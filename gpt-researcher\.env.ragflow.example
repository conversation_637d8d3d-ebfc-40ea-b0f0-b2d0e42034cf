# RAGFlow Integration Configuration
# Copy this file to .env and fill in your actual values

# === RAGFlow Settings ===
# Your RAGFlow instance URL (without /api/v1 suffix)
RAGFLOW_BASE_URL=http://localhost:9380

# RAGFlow API key (get from RAG<PERSON>low UI -> Settings -> API Keys)
RAGFLOW_API_KEY=your_ragflow_api_key_here

# Dataset IDs to search (comma-separated)
# Get these from RAGFlow UI -> Datasets
RAGFLOW_DATASET_IDS=dataset_id_1,dataset_id_2

# Optional: Specific document IDs to search (comma-separated)
# RAGFLOW_DOCUMENT_IDS=doc_id_1,doc_id_2

# === Search Parameters ===
# Minimum similarity threshold (0.0-1.0, default: 0.2)
RAGFLOW_SIMILARITY_THRESHOLD=0.2

# Weight for vector similarity vs keyword similarity (0.0-1.0, default: 0.3)
RAGFLOW_VECTOR_WEIGHT=0.3

# Maximum number of chunks to retrieve from vector store (default: 20)
RAGFLOW_TOP_K=20

# Page size for API requests (default: 30)
RAGFLOW_PAGE_SIZE=30

# === GPT Researcher Settings ===
# OpenAI API key for LLM
OPENAI_API_KEY=your_openai_api_key

# Tavily API key for web search (optional, for hybrid search)
TAVILY_API_KEY=your_tavily_api_key

# === Hybrid Search Settings ===
# Enable combining RAGFlow with web search
ENABLE_HYBRID_SEARCH=true

# Weight for RAGFlow results in hybrid search (0.0-1.0, default: 0.7)
RAGFLOW_WEIGHT=0.7

# Weight for web search results in hybrid search (0.0-1.0, default: 0.3)
WEB_SEARCH_WEIGHT=0.3

# === Advanced Settings ===
# Logging level
LOG_LEVEL=INFO

# Custom retriever endpoint (leave empty to use RAGFlow)
# RETRIEVER_ENDPOINT=

# Custom report types
# CUSTOM_REPORT_TYPES=business_analysis,technical_review,market_research