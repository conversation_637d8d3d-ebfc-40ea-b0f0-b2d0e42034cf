package com.ms.bp.infrastructure.repository.dao;

import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.List;

/**
 * 次年度計画マスタデータアクセス実装
 * T_JINENDO_KKK（次年度計画マスタ）への具体的なデータアクセスを実装
 */
public class JinendoMasterDataAccess {
    private static final Logger logger = LoggerFactory.getLogger(JinendoMasterDataAccess.class);

    // SQL定数
    private static final String EXISTS_BY_SSNKN_TNCD_SQL = """
        SELECT 1
        FROM T_JINENDO_KKK
        WHERE NENDO = ?
        AND   SSNKN_TNCD = ?
        AND   TKY_AREA_CODE = ?
        AND   TKY_GROUP_CODE = ?
        AND   TKY_UNIT_CODE = ?
        LIMIT 1
        """;
    // SQL定数
    private static final String EXISTS_BY_SSNKN_TNCD_SQL2 = """
        SELECT 1
        FROM T_JINENDO_KKK
        WHERE NENDO = ?
        AND   SSNKN_TNCD = ?
        AND   TKY_AREA_CODE = ?
        AND   TKY_GROUP_CODE = ?
        LIMIT 1
        """;

    private final JdbcTemplate jdbcTemplate;

    public JinendoMasterDataAccess(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 採算管理単位コードでグループマスタの存在チェック
     *
     * @param nendo 年度
     * @param saisanCode 採算管理コード
     * @param areaCode エリアコード
     * @param groupCode グループコード
     * @param unitCode ユニットコード
     * @return 存在する場合true、存在しない場合false
     * @throws SQLException データベースアクセスエラー
     */
    public boolean existsBySaisanCode(String nendo,
                                      String saisanCode,
                                      String areaCode,
                                      String groupCode,
                                      String unitCode) throws SQLException {
        logger.debug("次年度計画マスタ存在チェック開始: 採算管理単位コード={}", saisanCode);

        List<Integer> results = jdbcTemplate.query(
                EXISTS_BY_SSNKN_TNCD_SQL,
            new Object[]{nendo, saisanCode, areaCode, groupCode, unitCode},
            rs -> rs.getInt(1)
        );

        boolean exists = !results.isEmpty();
        logger.debug("次年度計画マスタ存在チェック完了: 採算管理単位コード={}, 存在={}", saisanCode, exists);

        return exists;
    }
    /**
     * 採算管理単位コードでグループマスタの存在チェック
     *
     * @param nendo 年度
     * @param saisanCode 採算管理コード
     * @param areaCode エリアコード
     * @param groupCode グループコード
     * @return 存在する場合true、存在しない場合false
     * @throws SQLException データベースアクセスエラー
     */
    public boolean existsBySaisanCode(String nendo,
                                      String saisanCode,
                                      String areaCode,
                                      String groupCode) throws SQLException {
        logger.debug("次年度計画マスタ存在チェック開始: 採算管理単位コード={}", saisanCode);

        List<Integer> results = jdbcTemplate.query(
                EXISTS_BY_SSNKN_TNCD_SQL2,
                new Object[]{nendo, saisanCode, areaCode, groupCode},
                rs -> rs.getInt(1)
        );

        boolean exists = !results.isEmpty();
        logger.debug("次年度計画マスタ存在チェック完了: 採算管理単位コード={}, 存在={}", saisanCode, exists);

        return exists;
    }
}
