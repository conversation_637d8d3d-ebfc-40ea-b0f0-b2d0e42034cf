package com.ms.bp.application.factory;



import com.ms.bp.domain.file.repository.ImportJobStatusRepository;
import com.ms.bp.domain.file.repository.ExportJobStatusRepository;
import com.ms.bp.domain.master.repository.AnnounceMessageRepository;
import com.ms.bp.domain.permission.PermissionService;
import com.ms.bp.domain.permission.repository.PermissionRepository;
import com.ms.bp.domain.master.repository.AreaCodeRepository;
import com.ms.bp.domain.master.repository.UserMasterRepository;
import com.ms.bp.domain.master.repository.GroupAreaRepository;
import com.ms.bp.domain.user.UserBusinessDomainService;
import com.ms.bp.infrastructure.repository.dao.AnnounceMessageDataAccess;
import com.ms.bp.infrastructure.repository.impl.*;
import com.ms.bp.infrastructure.repository.dao.ImportJobStatusDataAccess;
import com.ms.bp.infrastructure.repository.dao.ExportJobStatusDataAccess;
import com.ms.bp.infrastructure.repository.dao.PermissionDataAccess;
import com.ms.bp.infrastructure.external.email.EmailService;
import com.ms.bp.infrastructure.external.s3.S3Service;
import com.ms.bp.shared.common.db.JdbcTemplate;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * ドメインサービスファクトリ
 * Lambda環境下での依存関係管理を担当
 * 各領域のサービスインスタンスを作成・提供する
 */
public class DomainServiceFactory {
    private static final Logger logger = LoggerFactory.getLogger(DomainServiceFactory.class);

    @Getter
    private final JdbcTemplate jdbcTemplate;
    private final EmailService emailService;
    private final S3Service s3Service;

    private ImportJobStatusRepository importJobStatusRepository;
    private ExportJobStatusRepository exportJobStatusRepository;
    private PermissionRepository permissionRepository;
    private AreaCodeRepository areaCodeRepository;
    private UserMasterRepository userMasterRepository;
    private GroupAreaRepository groupAreaRepository;
    private AnnounceMessageRepository announceMessageRepository;
    private UserBusinessDomainService userBusinessDomainService;
    

    /**
     * ファクトリを初期化
     * @param jdbcTemplate データベース接続
     */
    public DomainServiceFactory(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.emailService = new EmailService();
        this.s3Service = new S3Service();

        logger.debug("DomainServiceFactoryが初期化されました");
    }

    // ==================== 認証領域サービス ====================


    // ==================== ユーザー業務領域サービス ====================

    /**
     * ユーザー業務ドメインサービスを取得（シングルトン）
     * @return ユーザー業務ドメインサービス
     */
    public UserBusinessDomainService getUserBusinessDomainService() {
        if (userBusinessDomainService == null) {
            userBusinessDomainService = new UserBusinessDomainService();
        }
        return userBusinessDomainService;
    }

    // ==================== 権限領域サービス ====================

    /**
     * 権限サービスを作成
     * @return 権限サービス
     */
    public PermissionService createPermissionService() {
        return new PermissionService(getPermissionRepository());
    }

    // ==================== ファイル処理領域サービス ====================
    

    // ==================== リポジトリ取得メソッド ====================
    


    /**
     * インポートジョブステータスリポジトリを取得（シングルトン）
     * @return インポートジョブステータスリポジトリ
     */
    public ImportJobStatusRepository getImportJobStatusRepository() {
        if (importJobStatusRepository == null) {
            ImportJobStatusDataAccess dataAccess = new ImportJobStatusDataAccess(jdbcTemplate);
            importJobStatusRepository = new ImportJobStatusRepositoryImpl(dataAccess);
        }
        return importJobStatusRepository;
    }

    /**
     * エクスポートジョブステータスリポジトリを取得（シングルトン）
     * @return エクスポートジョブステータスリポジトリ
     */
    public ExportJobStatusRepository getExportJobStatusRepository() {
        if (exportJobStatusRepository == null) {
            ExportJobStatusDataAccess dataAccess = new ExportJobStatusDataAccess(jdbcTemplate);
            exportJobStatusRepository = new ExportJobStatusRepositoryImpl(dataAccess);
        }
        return exportJobStatusRepository;
    }

    /**
     * 権限リポジトリを取得（シングルトン）
     * @return 権限リポジトリ
     */
    public PermissionRepository getPermissionRepository() {
        if (permissionRepository == null) {
            PermissionDataAccess dataAccess = new PermissionDataAccess(jdbcTemplate);
            permissionRepository = new PermissionRepositoryImpl(dataAccess);
        }
        return permissionRepository;
    }

    /**
     * エリアコードリポジトリを取得（シングルトン）
     * @return エリアコードリポジトリ
     */
    public AreaCodeRepository getAreaCodeRepository() {
        if (areaCodeRepository == null) {
            areaCodeRepository = new AreaCodeRepositoryImpl(jdbcTemplate);
        }
        return areaCodeRepository;
    }

    /**
     * 社員マスタリポジトリを取得（シングルトン）
     * @return 社員マスタリポジトリ
     */
    public UserMasterRepository getUserMasterRepository() {
        if (userMasterRepository == null) {
            userMasterRepository = new UserMasterRepositoryImpl(jdbcTemplate);
        }
        return userMasterRepository;
    }

    /**
     * アナウンスメッセージリポジトリを取得（シングルトン）
     * @return アナウンスメッセージリポジトリ
     */
    public AnnounceMessageRepository getAnnounceMessageRepository() {
        if (announceMessageRepository == null) {
            AnnounceMessageDataAccess dataAccess = new AnnounceMessageDataAccess(jdbcTemplate);
            announceMessageRepository = new AnnounceMessageRepositoryImpl(dataAccess);
        }
        return announceMessageRepository;
    }

    /**
     * 組織エリアマスタリポジトリを取得（シングルトン）
     * @return 組織エリアマスタリポジトリ
     */
    public GroupAreaRepository getGroupAreaRepository() {
        if (groupAreaRepository == null) {
            groupAreaRepository = new GroupAreaRepositoryImpl(jdbcTemplate);
        }
        return groupAreaRepository;
    }

    // ==================== リソース管理 ====================
    
    /**
     * リソースをクリーンアップ
     */
    public void cleanup() {
        try {
            if (s3Service != null) {
                s3Service.close();
            }
            if (emailService != null) {
                emailService.close();
            }
            logger.debug("DomainServiceFactoryのリソースがクリーンアップされました");
        } catch (Exception e) {
            logger.warn("リソースクリーンアップ中にエラーが発生しました", e);
        }
    }

}
