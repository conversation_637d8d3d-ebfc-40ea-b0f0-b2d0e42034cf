package com.ms.bp.domain.concurrent.repository;

import com.ms.bp.domain.concurrent.model.ConcurrentJobInfo;

import java.util.List;

/**
 * 兼務マスタリポジトリ領域インターフェース
 * 兼務情報のデータアクセスを抽象化し、領域層を基盤設備層から分離する
 */
public interface ConcurrentJobRepository {
    
    /**
     * 指定社員の有効な兼務情報を取得
     * 連携状態区分が"1"（有効）かつ兼務区分が"2"（兼務）のレコードを取得
     * 
     * @param shainCode 社員コード
     * @param systemOperationCompanyCode システム運用企業コード
     * @return 有効な兼務情報リスト（該当データがない場合は空のリスト）
     */
    List<ConcurrentJobInfo> findValidConcurrentJobs(String shainCode, String systemOperationCompanyCode);
    
    /**
     * 指定社員の有効な兼務情報を取得（エリアコード込み）
     * 一回のSQLでユニットマスタと結合してエリアコードも同時に取得
     * 連携状態区分が"1"（有効）かつ兼務区分が"2"（兼務）のレコードを対象
     * 
     * @param shainCode 社員コード
     * @param systemOperationCompanyCode システム運用企業コード
     * @return 有効な兼務情報リスト（エリアコード含む、該当データがない場合は空のリスト）
     */
    List<ConcurrentJobInfo> findValidConcurrentJobsWithAreaCode(String shainCode, String systemOperationCompanyCode);
    
    /**
     * 指定社員の全兼務情報を取得（主務含む）
     * デバッグや管理用途で使用
     * 
     * @param shainCode 社員コード
     * @param systemOperationCompanyCode システム運用企業コード
     * @return 全兼務情報リスト（該当データがない場合は空のリスト）
     */
    List<ConcurrentJobInfo> findAllConcurrentJobs(String shainCode, String systemOperationCompanyCode);
}