package com.ms.bp.shared.common.io.validation;

import lombok.Getter;

/**
 * バリデーションルールクラス
 * 個別の検証ルールの情報を保持します
 * GlobalCodeConstants統合対応版
 */
@Getter
public class ValidationRule {

    /**
     * バリデーションルールの種類
     */
    public enum Type {
        REQUIRED,                   // 必須チェック
        RANGE,                      // 範囲チェック
        NUMERIC_HALF_WIDTH,         // 半角数字チェック
        HALF_WIDTH_ALPHANUMERIC,    // 半角英数字チェック
        DECIMAL_FORMAT              // 数値フォーマットチェック
    }

    /** ルールの種類
     * -- GETTER --
     *  ルールの種類を取得
     *
     */
    private final Type type;

    /** カスタム日本語フィールド名
     * -- GETTER --
     *  カスタム日本語フィールド名を取得
     */
    private final String customFieldName;

    /** エラーコード
     * -- GETTER --
     *  GlobalCodeConstantsで定義されたエラーコードを取得
     */
    private final String errorCode;

    /** エラーメッセージテンプレート
     * -- GETTER --
     *  エラーメッセージテンプレートを取得
     */
    private final String messageTemplate;

    /** ルール固有のパラメータ
     * -- GETTER --
     *  ルール固有のパラメータを取得
     *
     */
    private final Object parameter;

    /**
     * コンストラクタ（GlobalCodeConstants統合版）
     *
     * @param type ルールの種類
     * @param customFieldName カスタム日本語フィールド名
     * @param errorCode エラーコード
     * @param messageTemplate エラーメッセージテンプレート
     * @param parameter ルール固有のパラメータ
     */
    public ValidationRule(Type type, String customFieldName, String errorCode, String messageTemplate, Object parameter) {
        this.type = type;
        this.customFieldName = customFieldName;
        this.errorCode = errorCode;
        this.messageTemplate = messageTemplate;
        this.parameter = parameter;
    }


    /**
     * パラメータを長整数配列として取得（範囲チェック用）
     *
     * @return 長整数配列パラメータ [min, max]
     */
    public long[] getRangeParameter() {
        if (parameter instanceof long[]) {
            return (long[]) parameter;
        }
        throw new IllegalStateException("パラメータが範囲配列ではありません: " + parameter);
    }

    /**
     * カスタムフィールド名が指定されているかどうかを確認
     *
     * @return カスタムフィールド名が指定されている場合はtrue
     */
    public boolean hasCustomFieldName() {
        return customFieldName != null && !customFieldName.trim().isEmpty();
    }

    @Override
    public String toString() {
        return "ValidationRule{" +
                "type=" + type +
                ", customFieldName='" + customFieldName + '\'' +
                ", errorCode='" + errorCode + '\'' +
                ", messageTemplate='" + messageTemplate + '\'' +
                ", parameter=" + parameter +
                '}';
    }
}
