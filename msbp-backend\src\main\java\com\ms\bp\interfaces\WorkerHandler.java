package com.ms.bp.interfaces;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.ms.bp.application.data.DataApplicationService;
import com.ms.bp.interfaces.dto.request.WorkerPayload;
import com.ms.bp.shared.util.RequestContext;
import com.ms.bp.shared.common.constants.BusinessConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Worker Lambda関数のエントリーポイント
 * メインLambda関数から非同期で呼び出され、DataApplicationServiceに処理を委譲
 */
public class WorkerHandler implements RequestHandler<WorkerPayload, Void> {
    
    private static final Logger logger = LoggerFactory.getLogger(WorkerHandler.class);

    /**
     * Worker Lambda関数のメインエントリーポイント
     * 
     * @param payload メインLambda関数から渡されるペイロードデータ
     * @param context Lambda実行コンテキスト
     * @return null（戻り値は使用しない）
     */
    @Override
    public Void handleRequest(WorkerPayload payload, Context context) {
        String jobId = payload.getJobId();
        String operationType = payload.getOperationType();
        
        logger.info("Worker Lambda処理開始: jobId={}, operationType={}", jobId, operationType);

        // RequestContextにユーザー情報を設定
        RequestContext.setUserInfo(payload.getUserInfo());

        // DataApplicationServiceに処理を委譲
        DataApplicationService dataApplicationService = new DataApplicationService();

        if (BusinessConstants.OPERATION_UPLOAD_CODE.equals(operationType)) {
            dataApplicationService.executeImportTask(payload, context);
        } else if (BusinessConstants.OPERATION_DOWNLOAD_CODE.equals(operationType)) {
            dataApplicationService.executeExportTask(payload, context);
        }
        logger.info("Worker Lambda処理正常完了: jobId={}", jobId);

        // RequestContextをクリア
        RequestContext.clear();
        
        return null;
    }
}