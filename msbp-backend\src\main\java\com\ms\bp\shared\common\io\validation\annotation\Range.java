package com.ms.bp.shared.common.io.validation.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 文字列長の範囲を指定するアノテーション
 * GlobalCodeConstants.ERR_017と統合されたバリデーション機能を提供
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Range {
    /**
     * 最小値
     */
    long min() default Long.MIN_VALUE;

    /**
     * 最大値
     */
    long max() default Long.MAX_VALUE;

    /**
     * カスタム日本語フィールド名
     * 指定された場合、エラーメッセージでJavaフィールド名の代わりに使用される
     * 例：fieldName = "年齢"
     */
    String fieldName() default "";

    /**
     * エラーコード（デフォルト：ERR_017）
     * GlobalCodeConstantsで定義されたエラーコードを指定
     */
    String errorCode() default "ERR_017";

    /**
     * カスタムエラーメッセージ
     * 空の場合はerrorCodeに対応するGlobalCodeConstantsのメッセージテンプレートを使用
     * 指定された場合は{field}、{min}、{max}占位符をサポート
     */
    String message() default "";
}
