package com.ms.bp.infrastructure.external.s3;

import lombok.Data;

/**
 * S3アップロードURL情報クラス
 * 署名付きアップロードURLとS3キーパスを含む情報を保持する
 */
@Data
public class UploadUrlInfo {
    /**
     * 署名付きアップロードURL
     * フロントエンドがファイルアップロードに使用する
     */
    private String uploadUrl;
    
    /**
     * S3オブジェクトキー（パス）
     * S3内でのファイル保存場所を示す完全パス
     * 後続のファイル参照や管理に使用される
     */
    private String s3Key;
    
    /**
     * コンストラクタ
     * @param uploadUrl 署名付きアップロードURL
     * @param s3Key S3オブジェクトキー
     */
    public UploadUrlInfo(String uploadUrl, String s3Key) {
        this.uploadUrl = uploadUrl;
        this.s3Key = s3Key;
    }
    
    /**
     * デフォルトコンストラクタ
     */
    public UploadUrlInfo() {
    }
}
