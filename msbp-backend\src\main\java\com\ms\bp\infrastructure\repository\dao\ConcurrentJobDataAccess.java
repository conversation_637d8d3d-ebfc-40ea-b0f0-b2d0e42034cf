package com.ms.bp.infrastructure.repository.dao;

import com.ms.bp.domain.concurrent.model.ConcurrentJobInfo;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.List;

/**
 * 兼務マスタデータアクセス実装
 * JDK21のText Blocks、Switch Expression等を活用した最適化実装
 */
public class ConcurrentJobDataAccess {
    private static final Logger logger = LoggerFactory.getLogger(ConcurrentJobDataAccess.class);

    private final JdbcTemplate jdbcTemplate;

    public ConcurrentJobDataAccess(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 指定社員の有効な兼務情報を取得
     * JDK21のText Blocksを活用したSQL構築とSwitch Expressionによるマッピング
     *
     * @param shainCode 社員コード
     * @param systemOperationCompanyCode システム運用企業コード
     * @return 有効な兼務情報リスト
     * @throws SQLException データベースアクセスエラー
     */
    public List<ConcurrentJobInfo> findValidConcurrentJobs(String shainCode, String systemOperationCompanyCode) throws SQLException {
        // JDK21: Text BlocksでクリーンなSQL構築
        var sql = """
            SELECT 
                SYSTM_UNYO_KIGYO_CODE,
                SHAIN_CODE,
                UNIT_CODE,
                YKSHK_KUBUN,
                KMM_KUBUN,
                RNK_JOTAI_KUBUN,
                VRSN,
                NCHJ,
                RCRD_KSHN_NCHJ,
                RCRD_TRK_KMM_KUBUN
            FROM M_KENMUMASTER
            WHERE SHAIN_CODE = ?
            AND SYSTM_UNYO_KIGYO_CODE = ?
            AND RNK_JOTAI_KUBUN = '1'  -- 有効
            AND KMM_KUBUN = '2'        -- 兼務（主務'1'を除く）
            ORDER BY UNIT_CODE
            """;

        logger.debug("有効兼務情報取得SQL実行: shainCode={}, systemOperationCompanyCode={}", 
                    shainCode, systemOperationCompanyCode);

        var params = new Object[]{shainCode, systemOperationCompanyCode};

        return jdbcTemplate.query(sql, params, (rs) -> {
            var concurrentJob = new ConcurrentJobInfo();
            concurrentJob.setSystemOperationCompanyCode(rs.getString("SYSTM_UNYO_KIGYO_CODE"));
            concurrentJob.setShainCode(rs.getString("SHAIN_CODE"));
            concurrentJob.setUnitCode(rs.getString("UNIT_CODE"));
            concurrentJob.setPositionDivision(rs.getString("YKSHK_KUBUN"));
            concurrentJob.setConcurrentDivision(rs.getString("KMM_KUBUN"));
            concurrentJob.setCooperationStatus(rs.getString("RNK_JOTAI_KUBUN"));
            concurrentJob.setVersion(rs.getString("VRSN"));
            concurrentJob.setRecordInsertDateTime(rs.getString("NCHJ"));
            concurrentJob.setRecordUpdateDateTime(rs.getString("RCRD_KSHN_NCHJ"));
            concurrentJob.setRecordImportDivisionCode(rs.getString("RCRD_TRK_KMM_KUBUN"));
            return concurrentJob;
        });
    }

    /**
     * 指定社員の有効な兼務情報を取得（エリアコード込み）
     * JDK21のText Blocksを使用してユニットマスタと結合し、一回のSQLでエリアコードも取得
     * 兼務のみ取得（兼務区分='2'）
     *
     * @param shainCode 社員コード
     * @param systemOperationCompanyCode システム運用企業コード
     * @return 有効な兼務情報リスト（エリアコード含む）
     * @throws SQLException データベースアクセスエラー
     */
    public List<ConcurrentJobInfo> findValidConcurrentJobsWithAreaCode(String shainCode, String systemOperationCompanyCode) throws SQLException {
        // JDK21: Text BlocksでUNIT -> GROUP -> AREAの関連を辿るSQL構築
        var sql = """
            SELECT 
                kenmu.SYSTM_UNYO_KIGYO_CODE,
                kenmu.SHAIN_CODE,
                kenmu.UNIT_CODE,
                kenmu.YKSHK_KUBUN,
                kenmu.KMM_KUBUN,
                kenmu.RNK_JOTAI_KUBUN,
                kenmu.VRSN,
                kenmu.NCHJ,
                kenmu.RCRD_KSHN_NCHJ,
                kenmu.RCRD_TRK_KMM_KUBUN,
                grp.AREA_CODE
            FROM M_KENMUMASTER kenmu
            INNER JOIN M_UNIT unit ON (
                unit.UNIT_CODE = kenmu.UNIT_CODE
                AND unit.SYSTM_UNYO_KIGYO_CODE = kenmu.SYSTM_UNYO_KIGYO_CODE
            )
            INNER JOIN M_GROUP grp ON (
                grp.GROUP_CODE = unit.GROUP_CODE
                AND grp.SYSTM_UNYO_KIGYO_CODE = unit.SYSTM_UNYO_KIGYO_CODE
            )
            WHERE kenmu.SHAIN_CODE = ?
            AND kenmu.SYSTM_UNYO_KIGYO_CODE = ?
            AND kenmu.RNK_JOTAI_KUBUN = '1'  -- 有効
            AND kenmu.KMM_KUBUN = '2'        -- 兼務のみ
            ORDER BY grp.AREA_CODE, kenmu.UNIT_CODE
            """;

        logger.debug("有効兼務情報（エリアコード含む）取得SQL実行: shainCode={}, systemOperationCompanyCode={}", 
                    shainCode, systemOperationCompanyCode);

        var params = new Object[]{shainCode, systemOperationCompanyCode};

        return jdbcTemplate.query(sql, params, (rs) -> {
            var concurrentJob = new ConcurrentJobInfo();
            concurrentJob.setSystemOperationCompanyCode(rs.getString("SYSTM_UNYO_KIGYO_CODE"));
            concurrentJob.setShainCode(rs.getString("SHAIN_CODE"));
            concurrentJob.setUnitCode(rs.getString("UNIT_CODE"));
            concurrentJob.setAreaCode(rs.getString("AREA_CODE")); // エリアコードを設定
            concurrentJob.setPositionDivision(rs.getString("YKSHK_KUBUN"));
            concurrentJob.setConcurrentDivision(rs.getString("KMM_KUBUN"));
            concurrentJob.setCooperationStatus(rs.getString("RNK_JOTAI_KUBUN"));
            concurrentJob.setVersion(rs.getString("VRSN"));
            concurrentJob.setRecordInsertDateTime(rs.getString("NCHJ"));
            concurrentJob.setRecordUpdateDateTime(rs.getString("RCRD_KSHN_NCHJ"));
            concurrentJob.setRecordImportDivisionCode(rs.getString("RCRD_TRK_KMM_KUBUN"));
            return concurrentJob;
        });
    }

    /**
     * 指定社員の全兼務情報を取得（主務含む）
     * デバッグ・管理用途での全件取得
     *
     * @param shainCode 社員コード
     * @param systemOperationCompanyCode システム運用企業コード
     * @return 全兼務情報リスト
     * @throws SQLException データベースアクセスエラー
     */
    public List<ConcurrentJobInfo> findAllConcurrentJobs(String shainCode, String systemOperationCompanyCode) throws SQLException {
        // JDK21: Text BlocksでクリーンなSQL構築
        var sql = """
            SELECT 
                SYSTM_UNYO_KIGYO_CODE,
                SHAIN_CODE,
                UNIT_CODE,
                YKSHK_KUBUN,
                KMM_KUBUN,
                RNK_JOTAI_KUBUN,
                VRSN,
                NCHJ,
                RCRD_KSHN_NCHJ,
                RCRD_TRK_KMM_KUBUN
            FROM M_KENMUMASTER
            WHERE SHAIN_CODE = ?
            AND SYSTM_UNYO_KIGYO_CODE = ?
            ORDER BY KMM_KUBUN, UNIT_CODE
            """;

        logger.debug("全兼務情報取得SQL実行: shainCode={}, systemOperationCompanyCode={}", 
                    shainCode, systemOperationCompanyCode);

        var params = new Object[]{shainCode, systemOperationCompanyCode};

        return jdbcTemplate.query(sql, params, (rs) -> {
            var concurrentJob = new ConcurrentJobInfo();
            concurrentJob.setSystemOperationCompanyCode(rs.getString("SYSTM_UNYO_KIGYO_CODE"));
            concurrentJob.setShainCode(rs.getString("SHAIN_CODE"));
            concurrentJob.setUnitCode(rs.getString("UNIT_CODE"));
            concurrentJob.setPositionDivision(rs.getString("YKSHK_KUBUN"));
            concurrentJob.setConcurrentDivision(rs.getString("KMM_KUBUN"));
            concurrentJob.setCooperationStatus(rs.getString("RNK_JOTAI_KUBUN"));
            concurrentJob.setVersion(rs.getString("VRSN"));
            concurrentJob.setRecordInsertDateTime(rs.getString("NCHJ"));
            concurrentJob.setRecordUpdateDateTime(rs.getString("RCRD_KSHN_NCHJ"));
            concurrentJob.setRecordImportDivisionCode(rs.getString("RCRD_TRK_KMM_KUBUN"));
            return concurrentJob;
        });
    }
}