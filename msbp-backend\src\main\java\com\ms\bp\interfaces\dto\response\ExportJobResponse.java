package com.ms.bp.interfaces.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * エクスポートジョブレスポンス
 * エクスポート処理開始時のレスポンス情報
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExportJobResponse {
    
    /**
     * ジョブID
     */
    private String jobId;
    
    /**
     * ステータス
     */
    private String status;
    
    /**
     * メッセージ
     */
    private String message;

    
    /**
     * 受付済みレスポンスを作成
     * @param jobId ジョブID
     * @return 受付済みレスポンス
     */
    public static ExportJobResponse accepted(String jobId) {
        return new ExportJobResponse(
            jobId,
            "ACCEPTED",
            "エクスポート処理を受け付けました。処理状況はジョブIDで確認してください。"
        );
    }

}
