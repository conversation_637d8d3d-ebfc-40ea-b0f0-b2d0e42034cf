package com.ms.bp.interfaces.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * インポートジョブレスポンス
 * インポート処理開始時のレスポンス情報
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImportJobResponse {
    
    /**
     * ジョブID
     */
    private String jobId;
    
    /**
     * ステータス
     */
    private String status;
    
    /**
     * メッセージ
     */
    private String message;

    
    /**
     * 受付済みレスポンスを作成
     * @param jobId ジョブID
     * @return 受付済みレスポンス
     */
    public static ImportJobResponse accepted(String jobId) {
        return new ImportJobResponse(
            jobId,
            "ACCEPTED",
            "インポート処理を受け付けました。処理状況はジョブIDで確認してください。"
        );
    }

}
