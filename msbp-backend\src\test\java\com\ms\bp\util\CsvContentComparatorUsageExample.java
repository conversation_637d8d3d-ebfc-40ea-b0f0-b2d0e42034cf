package com.ms.bp.util;

import com.ms.bp.infrastructure.external.s3.S3Service;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * CsvContentComparator使用例
 * 汎用CSV内容比較検証器の各種比較方法の実践的な使用パターンを示す
 * 
 * 重点機能：
 * - 正常データの詳細比較検証
 * - 異常・エラーデータの検証
 * - 多文件比較（異なるヘッダー・データ構造）
 * - 段階的検証パターン
 * - 特定ファイル指定検証
 */
public class CsvContentComparatorUsageExample {
    
    @Mock
    private S3Service mockS3Service;
    
    /**
     * 正常データの詳細比較検証例
     * 実際のCSVデータ内容を期待値と詳細比較する方法
     */
    @Test
    public void example_正常データ詳細比較検証() {
        List<String> expectedAreaCodes = Arrays.asList("0001", "0002");
        
        // 期待されるCSVデータ行を定義（実際のエクスポート結果に基づく）
        List<List<String>> expectedDataRows = Arrays.asList(
            Arrays.asList(
                "0001", "東京エリア", "0001", "冷凍食品", "G001", "U0001", "田中太郎",
                "K000001", "企業A株式会社", "01", "冷凍食品", "S001", "サブカテゴリA",
                "1001001", "採算管理単位名1", "変更前取組1", "変更後取組1",
                "0002", "大阪エリア", "G002", "U0002", "1000000", "800000"
            ),
            Arrays.asList(
                "0002", "大阪エリア", "0002", "冷蔵食品", "G002", "U0002", "佐藤花子",
                "K000002", "企業B株式会社", "02", "冷蔵食品", "S002", "サブカテゴリB",
                "1001002", "採算管理単位名2", "変更前取組2", "変更後取組2",
                "", "", "", "", "1500000", "1200000"
            )
        );
        
        // 全体データ比較検証
        CsvContentComparator.CsvValidationConfig fullDataConfig = 
            CsvContentComparator.createPlanMasterConfigWithData(expectedAreaCodes, expectedDataRows);
        CsvContentComparator.validateCsvDataComparison(mockS3Service, fullDataConfig);
        
        // 重要フィールドのみの選択的比較検証
        List<String> keyFields = Arrays.asList(
            "エリアCD", "エリア名", "企業CD", "企業名", 
            "採算管理単位CD", "採算管理単位名", "当年度実績累計(参考)"
        );
        CsvContentComparator.CsvValidationConfig fieldConfig = 
            CsvContentComparator.createPlanMasterConfigWithSelectedFields(
                expectedAreaCodes, expectedDataRows, keyFields);
        CsvContentComparator.validateCsvFieldComparison(mockS3Service, fieldConfig);
    }
    
    /**
     * 異常・エラーデータの検証例
     * データが存在しない場合のエラーCSV検証
     */
    @Test
    public void example_異常エラーデータ検証() {
        List<String> expectedAreaCodes = Arrays.asList("9999"); // 存在しないエリアコード
        
        // エラーCSV検証設定
        CsvContentComparator.CsvValidationConfig errorConfig = 
            CsvContentComparator.createPlanMasterConfig(expectedAreaCodes);
        
        // エラーCSVファイルの検証を実行
        // - ファイル名が "error_" プレフィックスで始まることを確認
        // - エラーメッセージ "条件に一致するデータが取得できませんでした。" を確認
        // - CSVが1行のみであることを確認
        CsvContentComparator.validateErrorCsvContent(mockS3Service, errorConfig);
    }
    
    /**
     * 段階的検証パターン例
     * 基本検証 → 構造検証 → データ比較検証の順で段階的に実行
     */
    @Test
    public void example_段階的検証パターン() {
        List<String> expectedAreaCodes = Arrays.asList("0001", "0002");
        List<List<String>> expectedDataRows = Arrays.asList(
            Arrays.asList("0001", "東京エリア", "K001", "企業A", "1000000"),
            Arrays.asList("0002", "大阪エリア", "K002", "企業B", "800000")
        );
        
        // 段階1: 基本的な構造検証（エラーでないことの確認）
        CsvContentComparator.CsvValidationConfig basicConfig = 
            CsvContentComparator.createPlanMasterConfig(expectedAreaCodes);
        CsvContentComparator.validateNormalCsvContent(mockS3Service, basicConfig);
        
        // 段階2: データ比較検証（重要フィールドのみ）
        List<String> keyFields = Arrays.asList("エリアCD", "エリア名", "企業CD", "企業名", "当年度実績累計(参考)");
        CsvContentComparator.CsvValidationConfig fieldConfig = 
            CsvContentComparator.createPlanMasterConfigWithSelectedFields(
                expectedAreaCodes, expectedDataRows, keyFields);
        CsvContentComparator.validateCsvFieldComparison(mockS3Service, fieldConfig);
        
        // 段階3: 全体データ比較検証（必要に応じて）
        CsvContentComparator.CsvValidationConfig fullConfig = 
            CsvContentComparator.createPlanMasterConfigWithData(expectedAreaCodes, expectedDataRows);
        CsvContentComparator.validateCsvDataComparison(mockS3Service, fullConfig);
    }
    
    /**
     * 特定ファイル名指定検証例
     * ZIP内の特定ファイルを指定して検証
     */
    @Test
    public void example_特定ファイル名指定検証() {
        List<String> expectedAreaCodes = Arrays.asList("0001");
        List<String> headers = Arrays.asList("エリアCD", "データ1", "データ2");
        List<List<String>> expectedData = Arrays.asList(
            Arrays.asList("0001", "値1", "値2")
        );
        
        // 特定ファイル名を指定した設定を作成
        CsvContentComparator.CsvValidationConfig config = 
            CsvContentComparator.createSpecificFileConfig(
                "特定業務",                    // 業務名
                headers,                      // ヘッダー
                "複数ファイル",               // ZIPファイルパターン
                "specific_file_001.csv",      // 特定ファイル名
                expectedAreaCodes,            // エリアコード
                expectedData,                 // 期待データ
                null                          // 全フィールド比較
            );
        
        CsvContentComparator.validateCsvDataComparison(mockS3Service, config);
    }
    
    /**
     * 汎用設定作成メソッド使用例
     * 共通メソッドを使用したカスタム業務の設定作成
     */
    @Test
    public void example_汎用設定作成メソッド使用() {
        List<String> expectedAreaCodes = Arrays.asList("0001", "0002");
        List<String> customHeaders = Arrays.asList("ID", "名前", "売上", "利益");
        List<List<String>> expectedData = Arrays.asList(
            Arrays.asList("001", "商品A", "1000000", "100000"),
            Arrays.asList("002", "商品B", "1500000", "200000")
        );
        
        // 汎用設定作成メソッドを使用
        CsvContentComparator.CsvValidationConfig config = 
            CsvContentComparator.createCsvValidationConfig(
                "商品売上データ",           // 業務名
                customHeaders,             // ヘッダー
                "商品売上",               // ZIPファイルパターン
                expectedAreaCodes,         // エリアコード
                expectedData,              // 期待データ
                Arrays.asList("ID", "名前", "売上"), // 比較フィールド
                "データが見つかりません",   // カスタムエラーメッセージ
                null                       // 特定ファイル名なし
            );
        
        CsvContentComparator.validateCsvFieldComparison(mockS3Service, config);
    }
    
    /**
     * 複数ファイル一括検証例 - 異なるヘッダー・データ構造
     * ZIP内の複数CSVファイルを一括で検証（各ファイルが異なる構造）
     */
    @Test
    public void example_複数ファイル一括検証_異なる構造() {
        List<String> expectedAreaCodes = Arrays.asList("0001", "0002");
        
        // ファイル1: 売上データ（売上関連のヘッダー）
        List<String> salesHeaders = Arrays.asList("エリアCD", "売上", "利益", "達成率");
        List<List<String>> salesData = Arrays.asList(
            Arrays.asList("0001", "1000000", "100000", "95.5"),
            Arrays.asList("0002", "800000", "80000", "88.2")
        );
        CsvContentComparator.CsvValidationConfig salesConfig = 
            CsvContentComparator.createSpecificFileConfig(
                "売上データ", salesHeaders, "複合データ", "sales_data.csv", 
                expectedAreaCodes, salesData, Arrays.asList("エリアCD", "売上", "利益"));
        
        // ファイル2: 顧客データ（顧客関連のヘッダー）
        List<String> customerHeaders = Arrays.asList("エリアCD", "顧客数", "契約数", "満足度");
        List<List<String>> customerData = Arrays.asList(
            Arrays.asList("0001", "500", "300", "4.2"),
            Arrays.asList("0002", "400", "250", "4.0")
        );
        CsvContentComparator.CsvValidationConfig customerConfig = 
            CsvContentComparator.createSpecificFileConfig(
                "顧客データ", customerHeaders, "複合データ", "customer_data.csv", 
                expectedAreaCodes, customerData, Arrays.asList("エリアCD", "顧客数"));
        
        // ファイル3: 在庫データ（在庫関連のヘッダー）
        List<String> inventoryHeaders = Arrays.asList("エリアCD", "商品CD", "在庫数", "回転率");
        List<List<String>> inventoryData = Arrays.asList(
            Arrays.asList("0001", "P001", "1200", "2.5"),
            Arrays.asList("0002", "P002", "800", "3.1")
        );
        CsvContentComparator.CsvValidationConfig inventoryConfig = 
            CsvContentComparator.createSpecificFileConfig(
                "在庫データ", inventoryHeaders, "複合データ", "inventory_data.csv", 
                expectedAreaCodes, inventoryData, null); // 全フィールド比較
        
        // 複数ファイル検証設定を作成
        CsvContentComparator.MultiFileValidationConfig multiConfig = 
            new CsvContentComparator.MultiFileValidationConfig(
                "複合データ", 
                Arrays.asList(salesConfig, customerConfig, inventoryConfig)
            );
        
        // 複数ファイル一括検証を実行
        CsvContentComparator.validateMultipleCsvFiles(mockS3Service, multiConfig);
    }
    
    /**
     * 大規模ZIP内ファイル検証例
     * 多数のCSVファイルが含まれるZIPの効率的な検証
     */
    @Test
    public void example_大規模ZIP内ファイル検証() {
        List<String> expectedAreaCodes = Arrays.asList("0001", "0002", "0003");
        
        // 複数の業務データファイル設定を作成
        List<CsvContentComparator.CsvValidationConfig> configs = new ArrayList<>();
        
        // 次年度計画マスタ
        List<String> planMasterHeaders = Arrays.asList(
            "エリアCD", "エリア名", "カテCD", "カテゴリ", "グループ", "ユニット", "担当者",
            "企業CD", "企業名", "業態集計", "業態名", "サブカテゴリ", "サブカテゴリ名",
            "採算管理単位CD", "採算管理単位名", "変更前取組区分", "変更後取組区分",
            "移管先エリアCD", "移管先エリア名", "移管先グループCD", "移管先ユニットCD",
            "当年度実績累計(参考)", "当年度計画累計(参考)"
        );
        configs.add(CsvContentComparator.createSpecificFileConfig(
            "次年度計画マスタ", 
            planMasterHeaders, 
            "年次データ", 
            "plan_master_20250101.csv", 
            expectedAreaCodes, null, null));
        
        // 見通しデータ
        configs.add(CsvContentComparator.createSpecificFileConfig(
            "見通しデータ", 
            Arrays.asList("エリアCD", "見通し値", "計画値"), 
            "年次データ", 
            "outlook_data_20250101.csv", 
            expectedAreaCodes, null, Arrays.asList("エリアCD", "見通し値")));
        
        // 実績データ
        configs.add(CsvContentComparator.createSpecificFileConfig(
            "実績データ", 
            Arrays.asList("エリアCD", "実績値", "達成率"), 
            "年次データ", 
            "actual_data_20250101.csv", 
            expectedAreaCodes, null, null));
        
        // 複数ファイル検証を実行
        CsvContentComparator.MultiFileValidationConfig multiConfig =
            new CsvContentComparator.MultiFileValidationConfig("年次データ", configs);
        CsvContentComparator.validateMultipleCsvFiles(mockS3Service, multiConfig);
    }

    /**
     * 混合検証シナリオ例
     * 正常データとエラーデータが混在するZIPファイルの検証
     */
    @Test
    public void example_混合検証シナリオ() {
        List<String> expectedAreaCodes = Arrays.asList("0001", "0002");

        // シナリオ1: 正常データファイル
        List<List<String>> normalData = Arrays.asList(
            Arrays.asList("0001", "正常データ1", "100"),
            Arrays.asList("0002", "正常データ2", "200")
        );
        CsvContentComparator.CsvValidationConfig normalConfig =
            CsvContentComparator.createSpecificFileConfig(
                "正常データ",
                Arrays.asList("エリアCD", "データ名", "値"),
                "混合データ",
                "normal_data.csv",
                expectedAreaCodes,
                normalData,
                null);

        // シナリオ2: エラーファイル（データなし）
        CsvContentComparator.CsvValidationConfig errorConfig =
            CsvContentComparator.createSpecificFileConfig(
                "エラーデータ",
                Arrays.asList("エラー"),
                "混合データ",
                "error_data.csv",
                Arrays.asList("9999"), // 存在しないエリア
                null,
                null);

        // 個別検証実行
        CsvContentComparator.validateCsvDataComparison(mockS3Service, normalConfig);
        CsvContentComparator.validateErrorCsvContent(mockS3Service, errorConfig);
    }

    /**
     * パフォーマンス最適化検証例
     * 大量データでの効率的な検証方法
     */
    @Test
    public void example_パフォーマンス最適化検証() {
        List<String> expectedAreaCodes = Arrays.asList("0001", "0002", "0003", "0004", "0005");

        // 大量データの場合は選択フィールド比較を推奨
        List<String> criticalFields = Arrays.asList(
            "エリアCD", "企業CD", "採算管理単位CD", "当年度実績累計(参考)"
        );

        // 重要フィールドのみの効率的検証
        CsvContentComparator.CsvValidationConfig optimizedConfig =
            CsvContentComparator.createPlanMasterConfigWithSelectedFields(
                expectedAreaCodes,
                null, // データ比較なし（構造のみ）
                criticalFields);

        // 基本構造検証のみ実行（高速）
        CsvContentComparator.validateNormalCsvContent(mockS3Service, optimizedConfig);
    }

    /**
     * カスタムエラーメッセージ検証例
     * 業務固有のエラーメッセージを検証
     */
    @Test
    public void example_カスタムエラーメッセージ検証() {
        List<String> expectedAreaCodes = Arrays.asList("0001");
        List<String> customHeaders = Arrays.asList("業務CD", "業務名", "ステータス");

        // カスタムエラーメッセージを設定
        CsvContentComparator.CsvValidationConfig customErrorConfig =
            CsvContentComparator.createCsvValidationConfig(
                "カスタム業務",
                customHeaders,
                "カスタムデータ",
                expectedAreaCodes,
                null, // データなし
                null,
                "指定された条件でデータが見つかりませんでした", // カスタムエラーメッセージ
                null
            );

        // カスタムエラーメッセージでの検証
        CsvContentComparator.validateErrorCsvContent(mockS3Service, customErrorConfig);
    }

    /**
     * 実践的な業務テスト統合例
     * 実際のテストクラスでの使用パターン
     */
    @Test
    public void example_実践的業務テスト統合() {
        // 実際のテストメソッド内での使用例
        List<String> testAreaCodes = Arrays.asList("0001", "0002");

        try {
            // Step 1: 基本検証（エラーでないことを確認）
            CsvContentComparator.CsvValidationConfig basicConfig =
                CsvContentComparator.createPlanMasterConfig(testAreaCodes);
            CsvContentComparator.validateNormalCsvContent(mockS3Service, basicConfig);

            // Step 2: 重要データの検証
            List<String> keyFields = Arrays.asList("エリアCD", "企業CD", "採算管理単位CD");
            List<List<String>> expectedKeyData = Arrays.asList(
                Arrays.asList("0001", "K001", "1001001"),
                Arrays.asList("0002", "K002", "1001002")
            );

            CsvContentComparator.CsvValidationConfig keyDataConfig =
                CsvContentComparator.createPlanMasterConfigWithSelectedFields(
                    testAreaCodes, expectedKeyData, keyFields);
            CsvContentComparator.validateCsvFieldComparison(mockS3Service, keyDataConfig);

            System.out.println("✅ CSV検証成功: 正常データが期待通りに生成されました");

        } catch (RuntimeException e) {
            if (e.getMessage().contains("エラーCSVが検出されました")) {
                // エラーCSVの場合の処理
                CsvContentComparator.CsvValidationConfig errorConfig =
                    CsvContentComparator.createPlanMasterConfig(testAreaCodes);
                CsvContentComparator.validateErrorCsvContent(mockS3Service, errorConfig);

                System.out.println("⚠️ CSV検証結果: エラーCSVが生成されました（データなし）");
            } else {
                throw e; // その他のエラーは再スロー
            }
        }
    }
}
