package com.ms.bp.domain.concurrent.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 兼務情報ドメインモデル
 * 従業員の兼務に関するビジネスロジックを含む富ドメインモデル
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConcurrentJobInfo {
    
    /**
     * システム運用企業コード
     */
    private String systemOperationCompanyCode;
    
    /**
     * 社員コード
     */
    private String shainCode;
    
    /**
     * ユニットコード（兼務先）
     */
    private String unitCode;
    
    /**
     * エリアコード（ユニットコードに対応）
     */
    private String areaCode;
    
    /**
     * 役職区分
     */
    private String positionDivision;
    
    /**
     * 兼務区分
     * "1": 主務, "2": 兼務
     */
    private String concurrentDivision;
    
    /**
     * 連携状態区分
     * "1": 有効, "0": 無効
     */
    private String cooperationStatus;
    
    /**
     * バージョン
     */
    private String version;
    
    /**
     * レコード登録日時
     */
    private String recordInsertDateTime;
    
    /**
     * レコード更新日時
     */
    private String recordUpdateDateTime;
    
    /**
     * レコード取込区分コード  
     */
    private String recordImportDivisionCode;
    
    /**
     * 兼務情報が有効かどうかを判定
     * 
     * @return 有効な兼務情報の場合true
     */
    public boolean isValidConcurrentJob() {
        return "1".equals(cooperationStatus) && "2".equals(concurrentDivision);
    }
    
    /**
     * areaCode,unitCode形式の文字列を生成
     * 
     * @return "areaCode,unitCode"形式の文字列（areaCodeがnullの場合は空文字列）
     */
    public String toAreaUnitPair() {
        return areaCode != null && !areaCode.trim().isEmpty() 
            ? STR."\{areaCode.trim()},\{unitCode.trim()}"
            : "";
    }
}