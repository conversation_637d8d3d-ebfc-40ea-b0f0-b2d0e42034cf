:root {
  --ifm-font-size-base: 16px;
  --ifm-code-font-size: 90%;

  --ifm-color-primary: #0c4da2;
  --ifm-color-primary-dark: rgb(11, 69, 146);
  --ifm-color-primary-darker: #0a418a;
  --ifm-color-primary-darkest: #083671;
  --ifm-color-primary-light: #0d55b2;
  --ifm-color-primary-lighter: #0e59ba;
  --ifm-color-primary-lightest: #1064d3;

  --ifm-color-emphasis-300: #1064d3;
  --ifm-link-color: #1064d3;
  --ifm-menu-color-active: #1064d3;
}

.docusaurus-highlight-code-line {
background-color: rgba(0, 0, 0, 0.1);
display: block;
margin: 0 calc(-1 * var(--ifm-pre-padding));
padding: 0 var(--ifm-pre-padding);
}
html[data-theme='dark'] .docusaurus-highlight-code-line {
background-color: rgb(0, 0, 0, 0.3);
}

.admonition-content a {
text-decoration: underline;
font-weight: 600;
color: inherit;
}

a {
font-weight: 600;
}

.markdown > p {
    font-size: 16px;
}

.navbar {
    font-size: 16px;
}

li {
font-size: 16px;
}

blockquote {
  /* samsung blue with lots of transparency */
  background-color: #0c4da224;
}
@media (prefers-color-scheme: dark) {
:root {
  --ifm-hero-text-color: white;
}
}
@media (prefers-color-scheme: dark) {
.hero.hero--primary { --ifm-hero-text-color: white;}
}

@media (prefers-color-scheme: dark) {
blockquote {
  --ifm-color-emphasis-300: var(--ifm-color-primary);
  /* border-left: 6px solid var(--ifm-color-emphasis-300); */
}
}
@media (prefers-color-scheme: dark) {
code {
  /* background-color: rgb(41, 45, 62); */
}
}


/* Docusaurus still defaults to their green! */
@media (prefers-color-scheme: dark) {
.react-toggle-thumb {
  border-color: var(--ifm-color-primary) !important;
}
}


.header-github-link:hover {
opacity: 0.6;
}

.header-github-link:before {
content: '';
width: 24px;
height: 24px;
display: flex;
background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12'/%3E%3C/svg%3E")
  no-repeat;
}

html[data-theme='dark'] .header-github-link:before {
background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='white' d='M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12'/%3E%3C/svg%3E")
  no-repeat;
}
